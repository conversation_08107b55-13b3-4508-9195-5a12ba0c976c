// SPDX-License-Identifier: GPL-3.0-only
pragma solidity ^0.8.28;

import {AmmalgamFactory, IPairFactory, PairFactory} from 'contracts/factories/AmmalgamFactory.sol';
import {NewTokensFactory} from 'contracts/factories/NewTokensFactory.sol';
import {ERC20LiquidityTokenFactory} from 'contracts/factories/ERC20LiquidityTokenFactory.sol';
import {ERC20DebtLiquidityTokenFactory} from 'contracts/factories/ERC20DebtLiquidityTokenFactory.sol';
import {ERC4626DepositTokenFactory} from 'contracts/factories/ERC4626DepositTokenFactory.sol';
import {ERC4626DebtTokenFactory} from 'contracts/factories/ERC4626DebtTokenFactory.sol';
import {PluginRegistry} from 'contracts/tokens/PluginRegistry.sol';

import {ITokenFactory} from 'contracts/interfaces/factories/ITokenFactory.sol';
import {INewTokensFactory} from 'contracts/interfaces/factories/INewTokensFactory.sol';
import {SaturationAndGeometricTWAPState} from 'contracts/SaturationAndGeometricTWAPState.sol';

import {DEFAULT_MID_TERM_INTERVAL, MINIMUM_LONG_TERM_TIME_UPDATE_CONFIG} from 'contracts/libraries/constants.sol';

// Used in scripts and tests.

function deployFactory(
    address deployer
) returns (AmmalgamFactory) {
    IPairFactory pairFactory = new PairFactory();
    SaturationAndGeometricTWAPState saturationAndGeometricTWAPState =
        new SaturationAndGeometricTWAPState(DEFAULT_MID_TERM_INTERVAL, MINIMUM_LONG_TERM_TIME_UPDATE_CONFIG);
    return deployFactory(deployer, pairFactory, address(saturationAndGeometricTWAPState));
}

function deployFactory(
    address deployer,
    IPairFactory pairFactory,
    address saturationAndGeometricTWAPState
) returns (AmmalgamFactory) {
    INewTokensFactory tokenFactory = deployTokenFactory();
    AmmalgamFactory factory = new AmmalgamFactory(
        deployer,
        address(tokenFactory),
        address(pairFactory),
        address(new PluginRegistry()),
        saturationAndGeometricTWAPState
    );
    factory.setFeeTo(deployer);
    return factory;
}

function deployTokenFactory() returns (INewTokensFactory) {
    ITokenFactory liquidityTokenFactory = new ERC20LiquidityTokenFactory();
    ITokenFactory depositTokenFactory = new ERC4626DepositTokenFactory();
    ITokenFactory debtTokenFactory = new ERC4626DebtTokenFactory();
    ITokenFactory liquidityDebtTokenFactory = new ERC20DebtLiquidityTokenFactory();
    INewTokensFactory tokenFactory =
        new NewTokensFactory(liquidityTokenFactory, depositTokenFactory, debtTokenFactory, liquidityDebtTokenFactory);

    return tokenFactory;
}
