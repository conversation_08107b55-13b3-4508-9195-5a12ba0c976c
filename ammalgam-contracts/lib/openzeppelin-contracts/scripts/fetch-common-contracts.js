#!/usr/bin/env node

// This script snapshots the bytecode and ABI for the `hardhat/common-contracts.js` script.
// - Bytecode is fetched directly from the blockchain by querying the provided client endpoint. If no endpoint is
//   provided, ethers default provider is used instead.
// - AB<PERSON> is fetched from etherscan's API using the provided etherscan API key. If no API key is provided, ABI will not
//   be fetched and saved.
//
// The produced artifacts are stored in the `output` folder ('test/bin' by default). For each contract, two files are
// produced:
// - `<name>.bytecode` containing the contract bytecode (in binary encoding)
// - `<name>.abi` containing the ABI (in utf-8 encoding)

const fs = require('fs');
const path = require('path');
const { ethers } = require('ethers');
const { request } = require('undici');
const { hideBin } = require('yargs/helpers');
const { argv } = require('yargs/yargs')(hideBin(process.argv))
  .env('')
  .options({
    output: { type: 'string', default: 'test/bin/' },
    client: { type: 'string' },
    etherscan: { type: 'string' },
  });

// List of contract names and addresses to fetch
const config = {
  EntryPoint070: '******************************************',
  SenderCreator070: '******************************************',
  EntryPoint080: '******************************************',
  SenderCreator080: '******************************************',
};

Promise.all(
  Object.entries(config).flatMap(([name, addr]) =>
    Promise.all([
      argv.etherscan &&
        request(`https://api.etherscan.io/api?module=contract&action=getabi&address=${addr}&apikey=${argv.etherscan}`)
          .then(({ body }) => body.json())
          .then(({ result: abi }) => fs.writeFile(path.join(argv.output, `${name}.abi`), abi, 'utf-8', () => {})),
      ethers
        .getDefaultProvider(argv.client)
        .getCode(addr)
        .then(bytecode =>
          fs.writeFile(path.join(argv.output, `${name}.bytecode`), ethers.getBytes(bytecode), 'binary', () => {}),
        ),
    ]),
  ),
);
