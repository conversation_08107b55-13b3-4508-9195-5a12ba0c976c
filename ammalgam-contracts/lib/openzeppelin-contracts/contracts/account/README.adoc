= Account
[.readme-notice]
NOTE: This document is better viewed at https://docs.openzeppelin.com/contracts/api/account

This directory includes contracts to build accounts for ERC-4337. These include:

 * {Account}: An ERC-4337 smart account implementation that includes the core logic to process user operations.
 * {AccountERC7579}: An extension of `Account` that implements support for ERC-7579 modules.
 * {AccountERC7579Hooked}: An extension of `AccountERC7579` with support for a single hook module (type 4).
 * {ERC7821}: Minimal batch executor implementation contracts. Useful to enable easy batch execution for smart contracts.
 * {ERC4337Utils}: Utility functions for working with ERC-4337 user operations.
 * {ERC7579Utils}: Utility functions for working with ERC-7579 modules and account modularity.

== Core

{{Account}}

== Extensions

{{AccountERC7579}}

{{AccountERC7579Hooked}}

{{ERC7821}}

== Utilities

{{ERC4337Utils}}

{{ERC7579Utils}}
