name: Halmos

on:
  push:
    branches:
      - main
  pull_request:
  workflow_dispatch:

jobs:
  verify:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4
        with:
          submodules: recursive

      - name: Install python
        uses: actions/setup-python@v5
        with:
          python-version: ">=3.9"

      - name: Install Foundry
        uses: foundry-rs/foundry-toolchain@v1

      - name: Install halmos
        run: pip install halmos

      - name: Run Halmos
        run: FOUNDRY_PROFILE=test halmos
