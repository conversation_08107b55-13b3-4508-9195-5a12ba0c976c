{"files": ["certora/helpers/MorphoHarness.sol", "certora/helpers/Util.sol"], "solc": "solc-0.8.19", "verify": "MorphoHarness:certora/specs/LiquidateBuffer.spec", "prover_args": ["-depth 5", "-mediumTimeout 20", "-timeout 3600", "-adaptiveSolverConfig false", "-smt_nonLinearArithmetic true", "-destructiveOptimizations twostage", "-solvers [z3:def{randomSeed=1},z3:def{randomSeed=2},z3:def{randomSeed=3},z3:def{randomSeed=4},z3:def{randomSeed=5},z3:def{randomSeed=6},z3:def{randomSeed=7},z3:def{randomSeed=8},z3:def{randomSeed=9},z3:def{randomSeed=10}]"], "multi_assert_check": true, "rule_sanity": "basic", "server": "production", "msg": "Morpho Blue Liquidate Buffer"}