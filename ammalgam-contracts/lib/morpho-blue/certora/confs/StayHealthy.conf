{"files": ["certora/helpers/MorphoHarness.sol", "certora/helpers/Util.sol", "src/mocks/OracleMock.sol"], "solc": "solc-0.8.19", "verify": "MorphoHarness:certora/specs/StayHealthy.spec", "prover_args": ["-solvers [z3:def{randomSeed=1},z3:def{randomSeed=2},z3:def{randomSeed=3},z3:def{randomSeed=4},z3:def{randomSeed=5},z3:def{randomSeed=6},z3:def{randomSeed=7},z3:def{randomSeed=8},z3:def{randomSeed=9},z3:def{randomSeed=10}]"], "multi_assert_check": true, "rule_sanity": "basic", "server": "production", "msg": "Morpho Blue Stay Healthy"}