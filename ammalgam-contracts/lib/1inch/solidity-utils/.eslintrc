{
  "root": true,
  "extends": [
    "eslint:recommended",
    "plugin:@typescript-eslint/recommended"
  ],
  "parser": "@typescript-eslint/parser",
  "plugins": [
    "@typescript-eslint"
  ],
  "env": {
    "browser": true,
    "node": true,
    "mocha": true,
    "jest": true
  },
  "globals": {
    "artifacts": false,
    "contract": false,
    "assert": false,
    "web3": false
  },
  "rules": {
    // TS
    "@typescript-eslint/no-non-null-assertion": "off",
    // Strict mode
    "strict": [
      2,
      "global"
    ],
    // Code style
    "indent": [
      2,
      4
    ],
    "quotes": [
      2,
      "single",
      {"avoidEscape": true}
    ],
    "semi": [
      "error",
      "always"
    ],
    "space-before-function-paren": [
      "error",
      {"anonymous": "always", "named": "never", "asyncArrow": "always"}
    ],
    "one-var-declaration-per-line": [
      "error",
      "always"
    ],
    "object-property-newline": [
      "error",
      { "allowAllPropertiesOnSameLine": true }
    ],
    "no-use-before-define": 0,
    "no-unused-expressions": "off",
    "eqeqeq": [
      2,
      "smart"
    ],
    "dot-notation": [
      2,
      {
        "allowKeywords": true,
        "allowPattern": ""
      }
    ],
    "no-redeclare": [
      2,
      {
        "builtinGlobals": true
      }
    ],
    "no-trailing-spaces": [
      2,
      {
        "skipBlankLines": true
      }
    ],
    "eol-last": 1,
    "comma-spacing": [
      2,
      {
        "before": false,
        "after": true
      }
    ],
    "camelcase": [
      2,
      {
        "properties": "always"
      }
    ],
    "no-mixed-spaces-and-tabs": [
      2,
      "smart-tabs"
    ],
    "comma-dangle": [
      1,
      "always-multiline"
    ],
    "no-dupe-args": 2,
    "no-dupe-keys": 2,
    "no-debugger": 0,
    "object-curly-spacing": [
      2,
      "always"
    ],
    "max-len": [
      2,
      200,
      2
    ],
    "generator-star-spacing": [
      "error",
      "before"
    ],
    "promise/avoid-new": 0,
    "promise/always-return": 0
  }
}
