{
  "compilerOptions": {
    "target": "es2020",
    "module": "commonjs",
    "strict": true,
    "esModuleInterop": true,
    "outDir": "dist",
    "sourceMap": true,
    "declaration": true,
    "moduleResolution": "node",
    "experimentalDecorators": true,
    "allowSyntheticDefaultImports": true,
    "importHelpers": true,
    "noFallthroughCasesInSwitch": true,
    "noImplicitReturns": true,
    "noUnusedParameters": true,
    "resolveJsonModule": true,
    "strictNullChecks": true,
    "typeRoots": ["node_modules/@types"],
    "lib": ["es2020", "dom", "esnext"],
    "skipLibCheck": true,
  }
}
