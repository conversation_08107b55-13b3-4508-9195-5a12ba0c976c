Change Log
==========

solidity-utils/6.4.0 (2025-02-03)
---------------------------------

- [SC-1427] Add Change Log and CI for release  ([#175](https://github.com/1inch/solidity-utils/pull/175); [e34a821](https://github.com/1inch/solidity-utils/commit/e34a821cb45eb47e6db53305381100850a1525ea)).
- [SC-1426] Permit2 for zksync ([#174](https://github.com/1inch/solidity-utils/pull/174); [8ce00fb](https://github.com/1inch/solidity-utils/commit/8ce00fbab103e27f50e66b0974bf0dd66439f290)).
- [SC-1426] Add `permit2address` method and patch `permit2Contract` method with `chainId` ([#178](https://github.com/1inch/solidity-utils/pull/178); [877c01f](https://github.com/1inch/solidity-utils/commit/877c01f6de0186877bcbc1f1f4e75e12d9e1b431)).
- [SC-1435] Fix Create3 deployment saving on contract source code verification failure ([#179](https://github.com/1inch/solidity-utils/pull/179); [28eaa1a](https://github.com/1inch/solidity-utils/commit/28eaa1a78413c03d96f3333a96f8b34d710a9c0b)).
- [SC-1428] Fix dependabot issues ([#177](https://github.com/1inch/solidity-utils/pull/177); [b799f40](https://github.com/1inch/solidity-utils/commit/b799f406b83d32f3cd48977cb5996cfd79846bb1)).
- [SC-1387] Remove gas usage limitation by using all available gas ([#176](https://github.com/1inch/solidity-utils/pull/176); [5bdc603](https://github.com/1inch/solidity-utils/commit/5bdc603395bae1b6923ce72f865b728cb6b6ad43)).

solidity-utils/6.3.2 (2025-01-22)
---------------------------------

- [SC-1394] Fix avax forking issue ([#171](https://github.com/1inch/solidity-utils/pull/171); [0268e85](https://github.com/1inch/solidity-utils/commit/0268e85fa84cff589e4397cfd925c054183e0210)).

solidity-utils/6.3.1 (2025-01-22)
---------------------------------

- [SC-1387] Use a consistent code style ([#170](https://github.com/1inch/solidity-utils/pull/170); [5085b30](https://github.com/1inch/solidity-utils/commit/5085b30d0a8617bdda624ed3ea590d5e679268ee)).

solidity-utils/6.3.0 (2024-12-30)
---------------------------------

- [SC-1343] Update zkSync verification to use an Etherscan-like explorer ([#167](https://github.com/1inch/solidity-utils/pull/167); [26969f6](https://github.com/1inch/solidity-utils/commit/26969f67e5ee32ad70d07ed1c3a8066ed2ede5e1)).
- chore: fix some typos ([#168](https://github.com/1inch/solidity-utils/pull/168); [422cc3f](https://github.com/1inch/solidity-utils/commit/422cc3ff0f6db41b681b814f2172d4d6016296fd)).
- chore: fix some typos in comment ([#166](https://github.com/1inch/solidity-utils/pull/166); [37b55c6](https://github.com/1inch/solidity-utils/commit/37b55c601328e53eea4502fa7376ecd5273808fb)).

solidity-utils/6.2.1 (2024-12-09)
---------------------------------

- [SC-1339] Update docs ([#165](https://github.com/1inch/solidity-utils/pull/165); [b0c7c19](https://github.com/1inch/solidity-utils/commit/b0c7c190d723e8c65bd708130f7d68cbdbf6ec23)).
- [SC-1338] Actualize README about Networks Class ([#164](https://github.com/1inch/solidity-utils/pull/164); [12a1fee](https://github.com/1inch/solidity-utils/commit/12a1fee05eee5f61e4645c57c4fd3200a0671fe6)).

solidity-utils/6.2.0 (2024-12-05)
---------------------------------

- Custom accounts in hardhat forking ([#163](https://github.com/1inch/solidity-utils/pull/163); [2d0a4a8](https://github.com/1inch/solidity-utils/commit/2d0a4a88a21b22aed4751cdc5ed8a825b29100ef)).

solidity-utils/6.1.0 (2024-12-03)
---------------------------------

- [SC-1321] Increase coverage ([#162](https://github.com/1inch/solidity-utils/pull/162); [59b54b1](https://github.com/1inch/solidity-utils/commit/59b54b10fa173376b737af68f423000e4a74ea95)).
- [SC-1320] `linea_mainnet` network as default ([#161](https://github.com/1inch/solidity-utils/pull/161); [db7ac6e](https://github.com/1inch/solidity-utils/commit/db7ac6e88bdfcde7c5f7cc0a41156454aac3ae5b)).

solidity-utils/6.0.0 (2024-11-05)
---------------------------------

- bump deps ([#158](https://github.com/1inch/solidity-utils/pull/158); [8604bc5](https://github.com/1inch/solidity-utils/commit/8604bc525b5bd4b71498b065f8b9f9f3d66c780f)).
- [SC-1287] Remove deprecated script for docgen ([#157](https://github.com/1inch/solidity-utils/pull/157); [3c89708](https://github.com/1inch/solidity-utils/commit/3c897080ca65deb0abcaa5b66f746eeeacd08cdd)).

solidity-utils/5.3.0 (2024-09-20)
---------------------------------

- [SC-1274] Add docgen templates to package ([#155](https://github.com/1inch/solidity-utils/pull/155); [2a69e2e](https://github.com/1inch/solidity-utils/commit/2a69e2ea6c7551cfae4731752a579de9564b263e)).

solidity-utils/5.2.3 (2024-08-21)
---------------------------------

- [SC-1242] skipIfAlreadyDeployed in deploy method via create3 ([#152](https://github.com/1inch/solidity-utils/pull/152); [98f381f](https://github.com/1inch/solidity-utils/commit/98f381f39a8504481871f28d67187ea9ce9596a7)).

solidity-utils/5.2.2 (2024-08-12)
---------------------------------

- [SC-1236] Patch permit2 interface ([#151](https://github.com/1inch/solidity-utils/pull/151); [1bb2620](https://github.com/1inch/solidity-utils/commit/1bb2620187b462f76a80ff0b5c72e9ceadc4e27a)).

solidity-utils/5.2.1 (2024-08-05)
---------------------------------

- [SC-1220] Fix dependabot issues ([#150](https://github.com/1inch/solidity-utils/pull/150); [b4d9f4e](https://github.com/1inch/solidity-utils/commit/b4d9f4e04b6570f7e42a1077e7b3335f8a755734)).
- Fix comment in ECDSA.sol ([#149](https://github.com/1inch/solidity-utils/pull/149); [1d237c3](https://github.com/1inch/solidity-utils/commit/1d237c3882909d003119a21df7ec7daf8aad5f9a)).
- Fix/types for build ([#148](https://github.com/1inch/solidity-utils/pull/148); [3f9eb9b](https://github.com/1inch/solidity-utils/commit/3f9eb9bdec4b440e63ef8fcdd03898f360d406fe)).

solidity-utils/5.2.0 (2024-07-03)
---------------------------------

- [SC-1177] Deploy with create3 and save deployment ([#147](https://github.com/1inch/solidity-utils/pull/147); [2e247c2](https://github.com/1inch/solidity-utils/commit/2e247c223b313ddf2757a98e8fcbd707102a4995)).

solidity-utils/5.1.0 (2024-06-06)
---------------------------------

- [SC-1140] Export bySig js helpers to package ([#145](https://github.com/1inch/solidity-utils/pull/145); [971276f](https://github.com/1inch/solidity-utils/commit/971276f9cbcb8f2d3dc98617bd2b2c04f6f209b0)).

solidity-utils/5.0.0 (2024-05-09)
---------------------------------

- [SC-1156] Safety `at` method in AddressArray and AddressSet libs ([#144](https://github.com/1inch/solidity-utils/pull/144); [176d078](https://github.com/1inch/solidity-utils/commit/176d0782ce5e29c1abd9f891b9574492525ab793)).

solidity-utils/4.2.1 (2024-04-26)
---------------------------------

- [SC-1121] Patch documents generation ([#143](https://github.com/1inch/solidity-utils/pull/143); [e5ff68f](https://github.com/1inch/solidity-utils/commit/e5ff68f5e0ccd8308a8781148fa5b88f9480324a)).
- [SC-1116] Change zksync testnet network ([#142](https://github.com/1inch/solidity-utils/pull/142); [828ada3](https://github.com/1inch/solidity-utils/commit/828ada3eda5ded20950a75b02e1a7bdb086fe4b7)).

solidity-utils/4.2.0 (2024-04-15)
---------------------------------

- Doc generation usage experience improvements ([#140](https://github.com/1inch/solidity-utils/pull/140); [7e895ad](https://github.com/1inch/solidity-utils/commit/7e895ad35c1012cfe9af17b15667d079a49fbc45)).
- update actions version ([#139](https://github.com/1inch/solidity-utils/pull/139); [ebf0bce](https://github.com/1inch/solidity-utils/commit/ebf0bcefcdd019f4f73b5c203af81f6330879a07)).
- Docs/new oz docgen ([#138](https://github.com/1inch/solidity-utils/pull/138); [0d918cd](https://github.com/1inch/solidity-utils/commit/0d918cd6d9e1a54f33cc2b5068e2c9737c7469e3)).
- [SC-1115] Update NatSpecs ([#136](https://github.com/1inch/solidity-utils/pull/136); [5f68a0a](https://github.com/1inch/solidity-utils/commit/5f68a0a22324787eb0ea26005ab2e8a2b8272ffe)).

solidity-utils/4.1.0 (2024-04-02)
---------------------------------

- [SC-1097] getEthPrice ([#135](https://github.com/1inch/solidity-utils/pull/135); [a870b9e](https://github.com/1inch/solidity-utils/commit/a870b9e95a9bfd26babb281d4a443d4a39cb5b30)).
- [SC-1103] add cheapEthSender ([#134](https://github.com/1inch/solidity-utils/pull/134); [ef05c02](https://github.com/1inch/solidity-utils/commit/ef05c02cbb41ff226bb2eebf59ebf95485125e4d)).
- [SC-1086] Natspecs and README ([#125](https://github.com/1inch/solidity-utils/pull/125); [98259b2](https://github.com/1inch/solidity-utils/commit/98259b24925ec02996ecd05a5b17736c00857ab1)).

solidity-utils/4.0.0 (2024-03-22)
---------------------------------

- [SC-583] Optimize AddressArray and AddressSet libs ([#61](https://github.com/1inch/solidity-utils/pull/61); [5ca7647](https://github.com/1inch/solidity-utils/commit/5ca764760a36ba598fe808acf0466a8ed6466ce2)).
- Add universal gasless operation implementation BySig ([#103](https://github.com/1inch/solidity-utils/pull/103); [6d1d795](https://github.com/1inch/solidity-utils/commit/6d1d795cdc224e3b8ecb617b60f0a710aeda03a7)).
- Move mixins into mixins subfolder ([#127](https://github.com/1inch/solidity-utils/pull/127); [92bb41d](https://github.com/1inch/solidity-utils/commit/92bb41d051051c718838fb0cb5b4d3ade92d5416)).
- [SC-1077] Add support for ERC-7597 permit ([#128](https://github.com/1inch/solidity-utils/pull/128); [fc1e783](https://github.com/1inch/solidity-utils/commit/fc1e783a739c2530e46ed6123ebc57a39d5dbb63)).

solidity-utils/3.8.3 (2024-03-19)
---------------------------------

- [SC-1085] Patch copyrights ([#124](https://github.com/1inch/solidity-utils/pull/124); [6f0de64](https://github.com/1inch/solidity-utils/commit/6f0de64d5d0dad561e81107038677dd2ec8dd23a)).
- [SC-1087] Custom output dir for docgen ([#126](https://github.com/1inch/solidity-utils/pull/126); [b3769eb](https://github.com/1inch/solidity-utils/commit/b3769eb3e0a09dce336bf99ee862652c40268fe2)).

solidity-utils/3.8.2 (2024-02-19)
---------------------------------

- Fix zksync network ([#123](https://github.com/1inch/solidity-utils/pull/123); [a90380e](https://github.com/1inch/solidity-utils/commit/a90380e3c034a39e8e54422b1ba365e49159f158)).

solidity-utils/3.8.1 (2024-02-14)
---------------------------------

- [SC-1071] update deps & fix coverage viaIR ([#120](https://github.com/1inch/solidity-utils/pull/120); [71b1b1f](https://github.com/1inch/solidity-utils/commit/71b1b1f9931a330e98ca6817ca172a78328887d4)).
- [SC-1068] Update the hardfork versions ([#119](https://github.com/1inch/solidity-utils/pull/119); [877c453](https://github.com/1inch/solidity-utils/commit/877c45316dad2aefc5c8be4324a47dc617169768)).
- [SC-1069] Refactor zksync networks ([#118](https://github.com/1inch/solidity-utils/pull/118); [4044a10](https://github.com/1inch/solidity-utils/commit/4044a10e233e968a426e15709092af086fe05a4d)).

solidity-utils/3.8.0 (2024-02-02)
---------------------------------

- [SC-1056] Fix resetHardhatNetworkFork method ([#116](https://github.com/1inch/solidity-utils/pull/116); [e7f3a19](https://github.com/1inch/solidity-utils/commit/e7f3a196f1cc7c90c79f722857cbafba21ef14c7)).
- [SC-1056] Add method to reset hardhat network ([#115](https://github.com/1inch/solidity-utils/pull/115); [c6ef0e4](https://github.com/1inch/solidity-utils/commit/c6ef0e4a6185b22fd75529cccfa82b133293e88f)).

solidity-utils/3.7.1 (2024-01-16)
---------------------------------

- fix ([74f8c2a](https://github.com/1inch/solidity-utils/commit/74f8c2a3c42403a5c85f94ba5d403f1fef51c1bf)).

solidity-utils/3.7.0 (2024-01-16)
---------------------------------

- move all the chai related stuff to expect.ts ([#113](https://github.com/1inch/solidity-utils/pull/113); [2d34aa0](https://github.com/1inch/solidity-utils/commit/2d34aa0726c81491f73f703239ff0265517ee225)).

solidity-utils/3.6.0 (2024-01-16)
---------------------------------

- add PermitAndCall contract ([#112](https://github.com/1inch/solidity-utils/pull/112); [5aa93dc](https://github.com/1inch/solidity-utils/commit/5aa93dcc3764da5f12cf309b2ed51228f8ea6267)).
- use jest for BytesMemory ([#111](https://github.com/1inch/solidity-utils/pull/111); [56fee7e](https://github.com/1inch/solidity-utils/commit/56fee7efe5178334573221f1f6b2b4f5b7a8beda)).
- [SC-972] Enable jest snapshots ([#95](https://github.com/1inch/solidity-utils/pull/95); [d0429c4](https://github.com/1inch/solidity-utils/commit/d0429c4929632aa958235f9911f7b88876e587d8)).
- [SC-975] Add library to handle bytes storage slices ([#96](https://github.com/1inch/solidity-utils/pull/96); [d7afb85](https://github.com/1inch/solidity-utils/commit/d7afb855a74d6513ed4167a69a20df88f2dac192)).

solidity-utils/3.5.6 (2023-12-26)
---------------------------------

- [SC-1034] Fix register  ([#109](https://github.com/1inch/solidity-utils/pull/109); [ebf7a89](https://github.com/1inch/solidity-utils/commit/ebf7a8990d65eb9bc00ea8ab4f0cbb2358d78e38)).
- [SC-1035] Fix xdai network name for etherscan verification ([#110](https://github.com/1inch/solidity-utils/pull/110); [2a5521f](https://github.com/1inch/solidity-utils/commit/2a5521f4e62a5303c8c7de2de79328b75612410f)).
- [SC-1033] Fix registerCustom ([#108](https://github.com/1inch/solidity-utils/pull/108); [07384fd](https://github.com/1inch/solidity-utils/commit/07384fd65cb6c6c6560e314a6d7920c72e1032aa)).

solidity-utils/3.5.5 (2023-12-18)
---------------------------------

- Patch hardhat-setup ([#107](https://github.com/1inch/solidity-utils/pull/107); [cd0079c](https://github.com/1inch/solidity-utils/commit/cd0079c7a3bd4d7dfdb52c30e4b7ca31721ed4f7)).
- [SC-990] update openZeppline and other deps ([#106](https://github.com/1inch/solidity-utils/pull/106); [c1c0492](https://github.com/1inch/solidity-utils/commit/c1c04927031ffbb8676407f79454862e3bd7843f)).

solidity-utils/3.5.4 (2023-12-11)
---------------------------------

- add notes about dirty bits cleaning ([bcb0ace](https://github.com/1inch/solidity-utils/commit/bcb0aceb4e6c549ca8eb3c6c30d11da4cd8f9f1c)).

solidity-utils/3.5.3 (2023-12-06)
---------------------------------

- make safeDeposit memory-safe ([#104](https://github.com/1inch/solidity-utils/pull/104); [35b6440](https://github.com/1inch/solidity-utils/commit/35b6440682f8f68fa59b123d2e8b415dcc4cba17)).

solidity-utils/3.5.2 (2023-11-23)
---------------------------------

- fix ([8585de6](https://github.com/1inch/solidity-utils/commit/8585de6fc8cf8dd16fdf557c0dc49a5b3138e398)).

solidity-utils/3.5.1 (2023-11-22)
---------------------------------

- [SC-1005] Patch trackReceivedTokenAndTx method ([#102](https://github.com/1inch/solidity-utils/pull/102); [b223f3c](https://github.com/1inch/solidity-utils/commit/b223f3c8673d9dc7ac05f7e112b8252e57652fff)).
- [SC-1004] Save context for getBalance in trackReceivedTokenAndTx ([#101](https://github.com/1inch/solidity-utils/pull/101); [71f36d7](https://github.com/1inch/solidity-utils/commit/71f36d7321c4ca1b7897e022fa3a49e1115c746e)).

solidity-utils/3.5.0 (2023-11-17)
---------------------------------

- [SC-1002] Hardhat-Setup Module ([#100](https://github.com/1inch/solidity-utils/pull/100); [2b15ce7](https://github.com/1inch/solidity-utils/commit/2b15ce76cf5fa087ff34d4028dac78ce5b1d8e45)).

solidity-utils/3.4.0 (2023-11-16)
---------------------------------

- bump deps ([#99](https://github.com/1inch/solidity-utils/pull/99); [2d8f878](https://github.com/1inch/solidity-utils/commit/2d8f8781addbaf6d6d546917f1fe3bd3f7346e08)).
- [SC-1001] Networks Register Class ([#98](https://github.com/1inch/solidity-utils/pull/98); [1d47206](https://github.com/1inch/solidity-utils/commit/1d472062112014af7c06f1f447adfa5ca61a63d4)).

solidity-utils/3.3.0 (2023-11-14)
---------------------------------

- [SC-989][SC-990] Bump @openzeppelin to version 5.0.0 ([#97](https://github.com/1inch/solidity-utils/pull/97); [8bf9ccb](https://github.com/1inch/solidity-utils/commit/8bf9ccba41e5090feabae6eb2710be866d16fafb)).

solidity-utils/3.2.0 (2023-10-18)
---------------------------------

- [SC-967] Deploy contract from bytecode ([#94](https://github.com/1inch/solidity-utils/pull/94); [a0202c6](https://github.com/1inch/solidity-utils/commit/a0202c68f4551d78e7f0b3843412991ac7d15f15)).

solidity-utils/3.1.0 (2023-09-28)
---------------------------------

- Introduce RevertReasonForwarder.reReason() ([#93](https://github.com/1inch/solidity-utils/pull/93); [339ff9e](https://github.com/1inch/solidity-utils/commit/339ff9e7e7a5288a5ee42539bb6fa4320405a441)).

solidity-utils/3.0.1 (2023-07-26)
---------------------------------

- Fixed package building ([#92](https://github.com/1inch/solidity-utils/pull/92); [34886f1](https://github.com/1inch/solidity-utils/commit/34886f19209ca922b09668ccd7f7f78068f8e9a0)).

solidity-utils/3.0.0 (2023-07-12)
---------------------------------

- Migrate to new hardhat-ethers ([#89](https://github.com/1inch/solidity-utils/pull/89); [a6fc5ee](https://github.com/1inch/solidity-utils/commit/a6fc5eedc8f837e71533263458f35d3770a79180)).

solidity-utils/2.4.0 (2023-07-10)
---------------------------------

- Interface for create3 deployer ([#91](https://github.com/1inch/solidity-utils/pull/91); [5fdd4c2](https://github.com/1inch/solidity-utils/commit/5fdd4c2c02525855e9f64d9a43d779013c6444ea)).

solidity-utils/2.3.1 (2023-06-28)
---------------------------------

- Patch trackReceivedTokenAndTx method ([#88](https://github.com/1inch/solidity-utils/pull/88); [068fc83](https://github.com/1inch/solidity-utils/commit/068fc83b4290c91a7a7db5522a02eb089029a0e6)).

solidity-utils/2.3.0 (2023-06-20)
---------------------------------

- [SC-846] Extract deploy helper implementations to solidity-utils ([#87](https://github.com/1inch/solidity-utils/pull/87); [47e1997](https://github.com/1inch/solidity-utils/commit/47e19975f7c572d1e30164e361babea30a9119f0)).

solidity-utils/2.2.28 (2023-06-12)
---------------------------------

- Add missing WETH events (improve tracing in projects using the lib) ([#86](https://github.com/1inch/solidity-utils/pull/86); [a2214b9](https://github.com/1inch/solidity-utils/commit/a2214b9b24204af91ac1e563d3be3a6943ef30c6)).
- [SC-800] Natspecs SafeERC20.sol ([#84](https://github.com/1inch/solidity-utils/pull/84); [26c5b24](https://github.com/1inch/solidity-utils/commit/26c5b24321555c7b5b563e876a6dcad85050dd0e)).

solidity-utils/2.2.27 (2023-05-03)
---------------------------------

- [SC-841] Introduce cheap token.safeBalanceOf() method ([#83](https://github.com/1inch/solidity-utils/pull/83); [b51b934](https://github.com/1inch/solidity-utils/commit/b51b9348f703f7612fda7573d3cb476bccb34383)).

solidity-utils/2.2.26 (2023-04-24)
---------------------------------

- improve memory safety ([90dcb3b](https://github.com/1inch/solidity-utils/commit/90dcb3b3cb37a0f0660e2b0e64f8289a2153e1ca)).

solidity-utils/2.2.25 (2023-04-19)
---------------------------------

- [SC-779] Natspecs for AddressLib ([#82](https://github.com/1inch/solidity-utils/pull/82); [06b7ec3](https://github.com/1inch/solidity-utils/commit/06b7ec36c45b3e50d17516f13bd121c599435c31)).

solidity-utils/2.2.24 (2023-04-04)
---------------------------------

- use consistent length for permit calls ([ba1b22a](https://github.com/1inch/solidity-utils/commit/ba1b22a50fc428a7774b02574c7fa3fec8a25ce6)).
- bump version ([de14fa0](https://github.com/1inch/solidity-utils/commit/de14fa02305f3743d745b0a2a1ec6ea8c485cab9)).

solidity-utils/2.2.23 (2023-04-03)
---------------------------------

- add input validation ([#80](https://github.com/1inch/solidity-utils/pull/80); [f23ae09](https://github.com/1inch/solidity-utils/commit/f23ae0964c9786c0aea1a854c3e6883fe0556ff8)).
- add deployContract ([#79](https://github.com/1inch/solidity-utils/pull/79); [f0d2c52](https://github.com/1inch/solidity-utils/commit/f0d2c526ade6d9626ca0914db016268ab152a503)).

solidity-utils/2.2.22 (2023-03-29)
---------------------------------

- remove @skip-on-coverage flag ([#78](https://github.com/1inch/solidity-utils/pull/78); [eeb5a45](https://github.com/1inch/solidity-utils/commit/eeb5a45bcc656f2882040df117e67e2289ddc5ec)).
- Improve permit2 ([#77](https://github.com/1inch/solidity-utils/pull/77); [7b6c0ee](https://github.com/1inch/solidity-utils/commit/7b6c0eea8f95075fefe132478c7bb7c1b9b64e72)).

solidity-utils/2.2.21 (2023-03-06)
---------------------------------

- [SC-762] Update copyright ([#74](https://github.com/1inch/solidity-utils/pull/74); [d6fa9fe](https://github.com/1inch/solidity-utils/commit/d6fa9fea701a1f6d8affa7afeebde9317f3a72b3)).

solidity-utils/2.2.20 (2023-03-02)
---------------------------------

- [SC-752] Tests for IWETH methods in SafeERC20 library ([#73](https://github.com/1inch/solidity-utils/pull/73); [24a03de](https://github.com/1inch/solidity-utils/commit/24a03dea40cf240070fbd6a77a9ca1daec3c3172)).
- Create CONTRIBUTING.md ([#69](https://github.com/1inch/solidity-utils/pull/69); [d89fa6b](https://github.com/1inch/solidity-utils/commit/d89fa6b8f31717abebdcac198c3743967d7dfee8)).
- Improve package.json scripts ([#71](https://github.com/1inch/solidity-utils/pull/71); [22c22b7](https://github.com/1inch/solidity-utils/commit/22c22b7fc0001e59b01e71f02e626d7abcb90f78)).

solidity-utils/2.2.18 (2023-02-27)
---------------------------------

- Improve memory safe assembly annotation ([#70](https://github.com/1inch/solidity-utils/pull/70); [46e90d4](https://github.com/1inch/solidity-utils/commit/46e90d49b4326164b633368bcfb9e604a2162545)).

solidity-utils/2.2.17 (2023-02-27)
---------------------------------

- [SC-737] Packed permit2 ([#67](https://github.com/1inch/solidity-utils/pull/67); [60fa6eb](https://github.com/1inch/solidity-utils/commit/60fa6eba06f50f2acac5286dea5fb54f206e2434)).

solidity-utils/2.2.16 (2023-02-09)
---------------------------------

- WETH helpers ([#68](https://github.com/1inch/solidity-utils/pull/68); [31a399d](https://github.com/1inch/solidity-utils/commit/31a399d09949246759cfcd769061edf059c8bf5e)).

solidity-utils/2.2.15 (2023-02-09)
---------------------------------

- Leave only low 160 bits as non-zero for Address get() ([#66](https://github.com/1inch/solidity-utils/pull/66); [4ce4ff1](https://github.com/1inch/solidity-utils/commit/4ce4ff15ae9e5719a4cb4409bdce7fb8f4f44f0b)).

solidity-utils/2.2.14 (2023-02-09)
---------------------------------

- Fix permit2 transferFrom ([#65](https://github.com/1inch/solidity-utils/pull/65); [b3550db](https://github.com/1inch/solidity-utils/commit/b3550db2016dc4e28d0bad1dd6333d08e5ffac3a)).

solidity-utils/2.2.13 (2023-01-20)
---------------------------------

- Permit2 small refactoring ([#64](https://github.com/1inch/solidity-utils/pull/64); [533efeb](https://github.com/1inch/solidity-utils/commit/533efeb93706841fc7c1ced1e343298282714328)).

solidity-utils/2.2.12 (2023-01-19)
---------------------------------

- Add permit contract into getPermit2 ([#63](https://github.com/1inch/solidity-utils/pull/63); [1c20b95](https://github.com/1inch/solidity-utils/commit/1c20b950d570cd452adc019026be0e88a7d2f97f)).

solidity-utils/2.2.11 (2023-01-19)
---------------------------------

- [SC-728] SafeERC20 Permit2 universal transfer ([#62](https://github.com/1inch/solidity-utils/pull/62); [a05c649](https://github.com/1inch/solidity-utils/commit/a05c6495b06cbed783720d7adecc0869e9f36e92)).

solidity-utils/2.2.10 (2023-01-12)
---------------------------------

- Fix ([7eea88b](https://github.com/1inch/solidity-utils/commit/7eea88b4522afb605c8944cf339900a03dfe6524)).

solidity-utils/2.2.9 (2023-01-12)
---------------------------------

- New version ([405c156](https://github.com/1inch/solidity-utils/commit/405c1561b5e324b684b116cc57775fa1173352aa)).

solidity-utils/2.2.8 (2023-01-12)
---------------------------------

- New release ([29d991d](https://github.com/1inch/solidity-utils/commit/29d991df738473a647414c4b99ffce6568bc2c35)).

solidity-utils/2.2.7 (2023-01-12)
---------------------------------

- Optimize permit call ([#60](https://github.com/1inch/solidity-utils/pull/60); [b8fe80d](https://github.com/1inch/solidity-utils/commit/b8fe80d8bffa453eac582e4d9b2b97263d5c89e7)).

solidity-utils/2.2.6 (2023-01-03)
---------------------------------

- Add AddressLib ([#59](https://github.com/1inch/solidity-utils/pull/59); [4039c2e](https://github.com/1inch/solidity-utils/commit/4039c2e23f4133d84a88c4dbd7a1aaa0f39ceb8a)).

solidity-utils/2.2.5 (2022-12-05)
---------------------------------

- Remove hardhat from src ([#58](https://github.com/1inch/solidity-utils/pull/58); [95bc66f](https://github.com/1inch/solidity-utils/commit/95bc66ff4267ae112f97b8296c2515a6dd999449)).

solidity-utils/2.2.4 (2022-12-05)
---------------------------------

- Remove provider type ([#57](https://github.com/1inch/solidity-utils/pull/57); [6a89024](https://github.com/1inch/solidity-utils/commit/6a89024f555565d89cd46a8544600e78e946e0f1)).
- Add provider as param to profileEVM functions ([#56](https://github.com/1inch/solidity-utils/pull/56); [f94d797](https://github.com/1inch/solidity-utils/commit/f94d79788890d29a41750762361116cca6303dda)).

solidity-utils/2.2.3 (2022-12-04)
---------------------------------

- fix ERC20PermitMock ([#55](https://github.com/1inch/solidity-utils/pull/55); [f47c769](https://github.com/1inch/solidity-utils/commit/f47c7693e87a0c5e3d09a315dc3cc7a224bc1789)).

solidity-utils/2.2.2 (2022-11-04)
---------------------------------

- bump version ([92a4754](https://github.com/1inch/solidity-utils/commit/92a4754b880dbababcb08cc30050c57bdd0573da)).

solidity-utils/2.2.1 (2022-11-02)
---------------------------------

- Introduce SafeERC20.tryPermit() ([#54](https://github.com/1inch/solidity-utils/pull/54); [b931028](https://github.com/1inch/solidity-utils/commit/b931028f491283d7879b1229443d8c6af82545f5)).

solidity-utils/2.2.0 (2022-10-10)
---------------------------------

- fix deps and bump version ([67bf96a](https://github.com/1inch/solidity-utils/commit/67bf96a23a3cae591f5c94a90a4182b8a7ab00cc)).

solidity-utils/2.1.3 (2022-10-08)
---------------------------------

- linter ([29de078](https://github.com/1inch/solidity-utils/commit/29de078c07b96f439ee8c4bcab1fe0329b5ca0b7)).
- fix ([e3b764b](https://github.com/1inch/solidity-utils/commit/e3b764b7d1769d8bca27feef36d64917c536e93b)).
- fix build ([72bedcd](https://github.com/1inch/solidity-utils/commit/72bedcdf0cc18c8e0f62d05f0838447cf93afda5)).

solidity-utils/2.1.2 (2022-10-07)
---------------------------------

- [SC-611] update typechain, migrate to ethers + new hardhat ([#48](https://github.com/1inch/solidity-utils/pull/48); [5b4d063](https://github.com/1inch/solidity-utils/commit/5b4d0634c8cdfc11d67e08da4548b9db9eb0d187)).
- Added tool to list import dependencies    ([#52](https://github.com/1inch/solidity-utils/pull/52); [d92db4b](https://github.com/1inch/solidity-utils/commit/d92db4b59631aad274b494f02e818dfaec30a633)).
- [SC-602] Solidity utils tech debt ([#49](https://github.com/1inch/solidity-utils/pull/49); [c8d596b](https://github.com/1inch/solidity-utils/commit/c8d596bb6690978f364f3f1a65c590c36c98b747)).

solidity-utils/2.1.1 (2022-09-20)
---------------------------------

- use raw call with low gas limit for ETH transfers ([#46](https://github.com/1inch/solidity-utils/pull/46); [c4df6a1](https://github.com/1inch/solidity-utils/commit/c4df6a1d317adb5585b1b4267976835346ba9ef3)).
- use named constants + and instead of shr-shl ([#45](https://github.com/1inch/solidity-utils/pull/45); [388810b](https://github.com/1inch/solidity-utils/commit/388810b495e4f95441e1c2ffea52af67768ddd70)).
- Oz fixes ([#40](https://github.com/1inch/solidity-utils/pull/40); [a561c52](https://github.com/1inch/solidity-utils/commit/a561c52e0b33a11c9601237a580f7f1a06868c21)).
- make SafeERC20 safer ([#44](https://github.com/1inch/solidity-utils/pull/44); [8062f0c](https://github.com/1inch/solidity-utils/commit/8062f0c9d1f4ccae50c285e3514ac0cb04bc6b69)).
- [N01] move interface to separate file ([#43](https://github.com/1inch/solidity-utils/pull/43); [dff3dfe](https://github.com/1inch/solidity-utils/commit/dff3dfe320f68f31be741208679dfb0504d438a6)).
- add comment about zero-amount ([#42](https://github.com/1inch/solidity-utils/pull/42); [4d9ec08](https://github.com/1inch/solidity-utils/commit/4d9ec086be2a999ae6577fcd0a0b0003f8cb6df2)).
- Oz/l07 ([#41](https://github.com/1inch/solidity-utils/pull/41); [0dcec70](https://github.com/1inch/solidity-utils/commit/0dcec70a229b5247b4847e5f1f03c86d572cd8a3)).
- explain 20k gas stipend ([#39](https://github.com/1inch/solidity-utils/pull/39); [e07d51d](https://github.com/1inch/solidity-utils/commit/e07d51ddcb43db4932cea54d9cd4cfa7ab011056)).
- return false when signer is zero ([#38](https://github.com/1inch/solidity-utils/pull/38); [dea1fe4](https://github.com/1inch/solidity-utils/commit/dea1fe4b645ce1d6d29ec89c4aa31addc8cccde4)).

solidity-utils/2.0.25 (2022-08-29)
---------------------------------

- add more correctness to isValidSignature ([#35](https://github.com/1inch/solidity-utils/pull/35); [4d068c0](https://github.com/1inch/solidity-utils/commit/4d068c03086e9082ea2757492a572bd14685716f)).
- ERC20PermitMock ([#36](https://github.com/1inch/solidity-utils/pull/36); [1c736be](https://github.com/1inch/solidity-utils/commit/1c736be9c8e17c332516c79204a8db3328948e63)).
- Fixes/a2 ([#34](https://github.com/1inch/solidity-utils/pull/34); [f38ca84](https://github.com/1inch/solidity-utils/commit/f38ca8482eb11583a66117a37f6a5e70fc603bbe)).

solidity-utils/2.0.24 (2022-08-09)
---------------------------------

- Feature/safer ecdsa ([#33](https://github.com/1inch/solidity-utils/pull/33); [99b24b0](https://github.com/1inch/solidity-utils/commit/99b24b02cf13803f29f712279b95ce6b63e80655)).
- Safer SafeERC20 ([#32](https://github.com/1inch/solidity-utils/pull/32); [846cba9](https://github.com/1inch/solidity-utils/commit/846cba9249d4f7bc1922b64705a00771a59e0e27)).
- inherit OnlyWethReceiver from EthReceiver ([#31](https://github.com/1inch/solidity-utils/pull/31); [2b00cd3](https://github.com/1inch/solidity-utils/commit/2b00cd382124a1bef8e4d76f89ed2457aa6db241)).
- use low level call for transfer ([#30](https://github.com/1inch/solidity-utils/pull/30); [d17429c](https://github.com/1inch/solidity-utils/commit/d17429cb4dfedb4d31b53ef6728a4d8c38824835)).
- Feature/a fixes 1 ([#29](https://github.com/1inch/solidity-utils/pull/29); [71d6fe4](https://github.com/1inch/solidity-utils/commit/71d6fe457cc420eeba55f612e902e66920faa64c)).

solidity-utils/2.0.23 (2022-07-21)
---------------------------------

- Mocks upgrade ([#28](https://github.com/1inch/solidity-utils/pull/28); [637063e](https://github.com/1inch/solidity-utils/commit/637063e465bfa7524fa72e103e80ca126f2ff674)).

solidity-utils/2.0.22 (2022-07-07)
---------------------------------

- use IWETH ([eec6b52](https://github.com/1inch/solidity-utils/commit/eec6b523860af5215a8dd196fe3aff3a4d252fc9)).

solidity-utils/2.0.21 (2022-07-07)
---------------------------------

- Optimize ERC20 metadata getter ([#27](https://github.com/1inch/solidity-utils/pull/27); [a694c53](https://github.com/1inch/solidity-utils/commit/a694c53651ac6499f0d2512adfa404987639c46a)).
- Reuse forceApprove in UniERC20 library ([#26](https://github.com/1inch/solidity-utils/pull/26); [e7c13eb](https://github.com/1inch/solidity-utils/commit/e7c13eb5ab845ffd7497fbb7b7e6e51e714208eb)).

solidity-utils/2.0.20 (2022-07-04)
---------------------------------

- [SC-554] ECDSA library ([#25](https://github.com/1inch/solidity-utils/pull/25); [6a165a9](https://github.com/1inch/solidity-utils/commit/6a165a9167f7867850e67f0f5d576042a90cb3cc)).
- Add 0x prefix support to toBN() method ([#24](https://github.com/1inch/solidity-utils/pull/24); [9547fdb](https://github.com/1inch/solidity-utils/commit/9547fdb01291ff22ccfdb2b0415ebccd2acc6e6d)).

solidity-utils/2.0.18 (2022-06-30)
---------------------------------

- [SC-549] memory-safe-assembly ([#23](https://github.com/1inch/solidity-utils/pull/23); [509238b](https://github.com/1inch/solidity-utils/commit/509238b42914cf48badd4765dfee847ff0486e51)).

solidity-utils/2.0.17 (2022-06-29)
---------------------------------

- bump deps ([fe7c314](https://github.com/1inch/solidity-utils/commit/fe7c31458205d7d2aa1500887bb45da7a7d057fc)).
- fix deps ([2868510](https://github.com/1inch/solidity-utils/commit/286851062ce34d41665c2bd3344d382e3fdfa041)).
- add abicoder v1 where possible ([3363924](https://github.com/1inch/solidity-utils/commit/33639243c4b9319577102f723721602fe9df422a)).

solidity-utils/2.0.16 (2022-06-23)
---------------------------------

- better toBN ([e3f0248](https://github.com/1inch/solidity-utils/commit/e3f0248fe88c632f661a79b4e02f5881381ec630)).
- remove redundand SafeMath ([4df9e79](https://github.com/1inch/solidity-utils/commit/4df9e7964e89b46757521ed9de54363df419eb97)).
- bump package version ([a7a7996](https://github.com/1inch/solidity-utils/commit/a7a799627d88c39a04e954cb4f884d9318e14135)).

solidity-utils/2.0.15 (2022-06-20)
---------------------------------

- Introduce SafestERC20 with test stolen from OZ ([#22](https://github.com/1inch/solidity-utils/pull/22); [c5bfac8](https://github.com/1inch/solidity-utils/commit/c5bfac87a1f6801462c158cecc10726baeeed91c)).

solidity-utils/2.0.14 (2022-04-20)
---------------------------------

- Added script to generate documentation for tests ([#21](https://github.com/1inch/solidity-utils/pull/21); [35d7708](https://github.com/1inch/solidity-utils/commit/35d77084f77a8b8b638a90243b00e7399ac334e4)).
- Feature/revert reason forwarder ([#20](https://github.com/1inch/solidity-utils/pull/20); [28fdca6](https://github.com/1inch/solidity-utils/commit/28fdca65bf65360fd462ff84e508170b68a59ef7)).

solidity-utils/2.0.12 (2022-04-08)
---------------------------------

- Autopublish new packages ([#19](https://github.com/1inch/solidity-utils/pull/19); [354a1f5](https://github.com/1inch/solidity-utils/commit/354a1f55ed9828023b05e747a0be77f90281095a)).
- 0.8 improvements ([#16](https://github.com/1inch/solidity-utils/pull/16); [4f7aaee](https://github.com/1inch/solidity-utils/commit/4f7aaee81b2e9d12a69d44e280518b9b7352571e)).
- Update ci.yml ([#17](https://github.com/1inch/solidity-utils/pull/17); [e5e959e](https://github.com/1inch/solidity-utils/commit/e5e959e3cac831c63d190f6fe493a834c0686fad)).

solidity-utils/2.0.10 (2022-03-23)
---------------------------------

- Feature/custom errors ([#15](https://github.com/1inch/solidity-utils/pull/15); [7315979](https://github.com/1inch/solidity-utils/commit/7315979603520e319c74ad2acbef1b0fe9273751)).

solidity-utils/2.0.9 (2022-03-19)
---------------------------------

- Fix docs generation util ([#14](https://github.com/1inch/solidity-utils/pull/14); [651fdfa](https://github.com/1inch/solidity-utils/commit/651fdfa58e88d809f45576f7878f8d1813f04bd8)).
- update logo readmi for dark-theme ([#13](https://github.com/1inch/solidity-utils/pull/13); [025ec9a](https://github.com/1inch/solidity-utils/commit/025ec9aec1887f84c430365a2ecdfe6d06d4d0de)).

solidity-utils/2.0.7 (2022-02-28)
---------------------------------

- Upgrade version ([c9a312d](https://github.com/1inch/solidity-utils/commit/c9a312de5eda13032babefb0fcf70804e7943ab7)).

solidity-utils/2.0.6 (2022-02-28)
---------------------------------

- [SC-333] Patch UniERC20 ([#12](https://github.com/1inch/solidity-utils/pull/12); [cd86301](https://github.com/1inch/solidity-utils/commit/cd863012445f23d1a409b5319651aff2e61623a1)).
- Feature/typescript support ([#10](https://github.com/1inch/solidity-utils/pull/10); [b8e5043](https://github.com/1inch/solidity-utils/commit/b8e50436622dc1913272a3b7bd9ea5265ef6ffc9)).

solidity-utils/1.3.0 (2022-02-15)
---------------------------------

- Introduce AddressArray get to output array ([#11](https://github.com/1inch/solidity-utils/pull/11); [f65ec3d](https://github.com/1inch/solidity-utils/commit/f65ec3d4efc6d5595ee6c336cde41da4749490e2)).

solidity-utils/1.2.7 (2021-12-28)
---------------------------------

- [SC-298] Utils for permit tests ([#9](https://github.com/1inch/solidity-utils/pull/9); [a0516f8](https://github.com/1inch/solidity-utils/commit/a0516f8181b50b94c3c829c3677c86d5dd42a672)).

solidity-utils/1.2.6 (2021-12-14)
---------------------------------

- Improve evm profiling script ([#7](https://github.com/1inch/solidity-utils/pull/7); [19e1cb6](https://github.com/1inch/solidity-utils/commit/19e1cb64a32a8c74fd04def1d270a4d7ce49b581)).

solidity-utils/1.2.5 (2021-12-06)
---------------------------------

- Patched _normalizeOp method + tests ([42af6d3](https://github.com/1inch/solidity-utils/commit/42af6d32d09d93d321dff475d097aecc7c67c212)).

solidity-utils/1.2.4 (2021-12-06)
---------------------------------

- [SC-217] Added tests for Permitable contract ([#6](https://github.com/1inch/solidity-utils/pull/6); [f4afe1b](https://github.com/1inch/solidity-utils/commit/f4afe1be6b4775cfe6c0639850e39ba4d6b57018)).

solidity-utils/1.2.3 (2021-11-12)
---------------------------------

- Patched readme, removed mocks and tests from npm package ([91f5543](https://github.com/1inch/solidity-utils/commit/91f5543279c56aafd0573d987f04b9b8487a313d)).
- Added readme to npm package ([d1637b0](https://github.com/1inch/solidity-utils/commit/d1637b0448b06b68bf4d78afeac0e4f20b2ca5b8)).
- Added contracts to dist for npm package ([2ec6fe4](https://github.com/1inch/solidity-utils/commit/2ec6fe499dab97e634fc6681df1a949eb107d754)).

solidity-utils/1.2.0 (2021-11-09)
---------------------------------

- Added AddressArray, AddressSet libraries ([#8](https://github.com/1inch/solidity-utils/pull/8); [1eb38b5](https://github.com/1inch/solidity-utils/commit/1eb38b507664a6e84261848f783c826f6fc59857)).
- String util ([#5](https://github.com/1inch/solidity-utils/pull/5); [75748ce](https://github.com/1inch/solidity-utils/commit/75748ce41610846271e86330b4c5945b47c52858)).
- Added RevertReasonParserTest ([#4](https://github.com/1inch/solidity-utils/pull/4); [45da614](https://github.com/1inch/solidity-utils/commit/45da614a88bebf3b3aa700fb8232773e985ae51a)).

solidity-utils/1.1.3 (2022-02-20)
---------------------------------

solidity-utils/0.2.19 (2023-02-27)
---------------------------------

- Improve package.json scripts ([#71](https://github.com/1inch/solidity-utils/pull/71); [22c22b7](https://github.com/1inch/solidity-utils/commit/22c22b7fc0001e59b01e71f02e626d7abcb90f78)).
- Improve memory safe assembly annotation ([#70](https://github.com/1inch/solidity-utils/pull/70); [46e90d4](https://github.com/1inch/solidity-utils/commit/46e90d49b4326164b633368bcfb9e604a2162545)).
- [SC-737] Packed permit2 ([#67](https://github.com/1inch/solidity-utils/pull/67); [60fa6eb](https://github.com/1inch/solidity-utils/commit/60fa6eba06f50f2acac5286dea5fb54f206e2434)).
- WETH helpers ([#68](https://github.com/1inch/solidity-utils/pull/68); [31a399d](https://github.com/1inch/solidity-utils/commit/31a399d09949246759cfcd769061edf059c8bf5e)).
- Leave only low 160 bits as non-zero for Address get() ([#66](https://github.com/1inch/solidity-utils/pull/66); [4ce4ff1](https://github.com/1inch/solidity-utils/commit/4ce4ff15ae9e5719a4cb4409bdce7fb8f4f44f0b)).
- Fix permit2 transferFrom ([#65](https://github.com/1inch/solidity-utils/pull/65); [b3550db](https://github.com/1inch/solidity-utils/commit/b3550db2016dc4e28d0bad1dd6333d08e5ffac3a)).
- Permit2 small refactoring ([#64](https://github.com/1inch/solidity-utils/pull/64); [533efeb](https://github.com/1inch/solidity-utils/commit/533efeb93706841fc7c1ced1e343298282714328)).
- Add permit contract into getPermit2 ([#63](https://github.com/1inch/solidity-utils/pull/63); [1c20b95](https://github.com/1inch/solidity-utils/commit/1c20b950d570cd452adc019026be0e88a7d2f97f)).
- [SC-728] SafeERC20 Permit2 universal transfer ([#62](https://github.com/1inch/solidity-utils/pull/62); [a05c649](https://github.com/1inch/solidity-utils/commit/a05c6495b06cbed783720d7adecc0869e9f36e92)).
- Optimize permit call ([#60](https://github.com/1inch/solidity-utils/pull/60); [b8fe80d](https://github.com/1inch/solidity-utils/commit/b8fe80d8bffa453eac582e4d9b2b97263d5c89e7)).
- Add AddressLib ([#59](https://github.com/1inch/solidity-utils/pull/59); [4039c2e](https://github.com/1inch/solidity-utils/commit/4039c2e23f4133d84a88c4dbd7a1aaa0f39ceb8a)).
- Remove hardhat from src ([#58](https://github.com/1inch/solidity-utils/pull/58); [95bc66f](https://github.com/1inch/solidity-utils/commit/95bc66ff4267ae112f97b8296c2515a6dd999449)).
- Remove provider type ([#57](https://github.com/1inch/solidity-utils/pull/57); [6a89024](https://github.com/1inch/solidity-utils/commit/6a89024f555565d89cd46a8544600e78e946e0f1)).
- Add provider as param to profileEVM functions ([#56](https://github.com/1inch/solidity-utils/pull/56); [f94d797](https://github.com/1inch/solidity-utils/commit/f94d79788890d29a41750762361116cca6303dda)).
- fix ERC20PermitMock ([#55](https://github.com/1inch/solidity-utils/pull/55); [f47c769](https://github.com/1inch/solidity-utils/commit/f47c7693e87a0c5e3d09a315dc3cc7a224bc1789)).
- Introduce SafeERC20.tryPermit() ([#54](https://github.com/1inch/solidity-utils/pull/54); [b931028](https://github.com/1inch/solidity-utils/commit/b931028f491283d7879b1229443d8c6af82545f5)).
- [SC-611] update typechain, migrate to ethers + new hardhat ([#48](https://github.com/1inch/solidity-utils/pull/48); [5b4d063](https://github.com/1inch/solidity-utils/commit/5b4d0634c8cdfc11d67e08da4548b9db9eb0d187)).
- Added tool to list import dependencies    ([#52](https://github.com/1inch/solidity-utils/pull/52); [d92db4b](https://github.com/1inch/solidity-utils/commit/d92db4b59631aad274b494f02e818dfaec30a633)).
- [SC-602] Solidity utils tech debt ([#49](https://github.com/1inch/solidity-utils/pull/49); [c8d596b](https://github.com/1inch/solidity-utils/commit/c8d596bb6690978f364f3f1a65c590c36c98b747)).
- use raw call with low gas limit for ETH transfers ([#46](https://github.com/1inch/solidity-utils/pull/46); [c4df6a1](https://github.com/1inch/solidity-utils/commit/c4df6a1d317adb5585b1b4267976835346ba9ef3)).
- use named constants + and instead of shr-shl ([#45](https://github.com/1inch/solidity-utils/pull/45); [388810b](https://github.com/1inch/solidity-utils/commit/388810b495e4f95441e1c2ffea52af67768ddd70)).
- Oz fixes ([#40](https://github.com/1inch/solidity-utils/pull/40); [a561c52](https://github.com/1inch/solidity-utils/commit/a561c52e0b33a11c9601237a580f7f1a06868c21)).
- make SafeERC20 safer ([#44](https://github.com/1inch/solidity-utils/pull/44); [8062f0c](https://github.com/1inch/solidity-utils/commit/8062f0c9d1f4ccae50c285e3514ac0cb04bc6b69)).
- [N01] move interface to separate file ([#43](https://github.com/1inch/solidity-utils/pull/43); [dff3dfe](https://github.com/1inch/solidity-utils/commit/dff3dfe320f68f31be741208679dfb0504d438a6)).
- add comment about zero-amount ([#42](https://github.com/1inch/solidity-utils/pull/42); [4d9ec08](https://github.com/1inch/solidity-utils/commit/4d9ec086be2a999ae6577fcd0a0b0003f8cb6df2)).
- Oz/l07 ([#41](https://github.com/1inch/solidity-utils/pull/41); [0dcec70](https://github.com/1inch/solidity-utils/commit/0dcec70a229b5247b4847e5f1f03c86d572cd8a3)).
- explain 20k gas stipend ([#39](https://github.com/1inch/solidity-utils/pull/39); [e07d51d](https://github.com/1inch/solidity-utils/commit/e07d51ddcb43db4932cea54d9cd4cfa7ab011056)).
- return false when signer is zero ([#38](https://github.com/1inch/solidity-utils/pull/38); [dea1fe4](https://github.com/1inch/solidity-utils/commit/dea1fe4b645ce1d6d29ec89c4aa31addc8cccde4)).
- add more correctness to isValidSignature ([#35](https://github.com/1inch/solidity-utils/pull/35); [4d068c0](https://github.com/1inch/solidity-utils/commit/4d068c03086e9082ea2757492a572bd14685716f)).
- ERC20PermitMock ([#36](https://github.com/1inch/solidity-utils/pull/36); [1c736be](https://github.com/1inch/solidity-utils/commit/1c736be9c8e17c332516c79204a8db3328948e63)).
- Fixes/a2 ([#34](https://github.com/1inch/solidity-utils/pull/34); [f38ca84](https://github.com/1inch/solidity-utils/commit/f38ca8482eb11583a66117a37f6a5e70fc603bbe)).
- Feature/safer ecdsa ([#33](https://github.com/1inch/solidity-utils/pull/33); [99b24b0](https://github.com/1inch/solidity-utils/commit/99b24b02cf13803f29f712279b95ce6b63e80655)).
- Safer SafeERC20 ([#32](https://github.com/1inch/solidity-utils/pull/32); [846cba9](https://github.com/1inch/solidity-utils/commit/846cba9249d4f7bc1922b64705a00771a59e0e27)).
- inherit OnlyWethReceiver from EthReceiver ([#31](https://github.com/1inch/solidity-utils/pull/31); [2b00cd3](https://github.com/1inch/solidity-utils/commit/2b00cd382124a1bef8e4d76f89ed2457aa6db241)).
- use low level call for transfer ([#30](https://github.com/1inch/solidity-utils/pull/30); [d17429c](https://github.com/1inch/solidity-utils/commit/d17429cb4dfedb4d31b53ef6728a4d8c38824835)).
- Feature/a fixes 1 ([#29](https://github.com/1inch/solidity-utils/pull/29); [71d6fe4](https://github.com/1inch/solidity-utils/commit/71d6fe457cc420eeba55f612e902e66920faa64c)).
- Mocks upgrade ([#28](https://github.com/1inch/solidity-utils/pull/28); [637063e](https://github.com/1inch/solidity-utils/commit/637063e465bfa7524fa72e103e80ca126f2ff674)).
- Optimize ERC20 metadata getter ([#27](https://github.com/1inch/solidity-utils/pull/27); [a694c53](https://github.com/1inch/solidity-utils/commit/a694c53651ac6499f0d2512adfa404987639c46a)).
- Reuse forceApprove in UniERC20 library ([#26](https://github.com/1inch/solidity-utils/pull/26); [e7c13eb](https://github.com/1inch/solidity-utils/commit/e7c13eb5ab845ffd7497fbb7b7e6e51e714208eb)).
- [SC-554] ECDSA library ([#25](https://github.com/1inch/solidity-utils/pull/25); [6a165a9](https://github.com/1inch/solidity-utils/commit/6a165a9167f7867850e67f0f5d576042a90cb3cc)).
- Add 0x prefix support to toBN() method ([#24](https://github.com/1inch/solidity-utils/pull/24); [9547fdb](https://github.com/1inch/solidity-utils/commit/9547fdb01291ff22ccfdb2b0415ebccd2acc6e6d)).
- [SC-549] memory-safe-assembly ([#23](https://github.com/1inch/solidity-utils/pull/23); [509238b](https://github.com/1inch/solidity-utils/commit/509238b42914cf48badd4765dfee847ff0486e51)).
- Introduce SafestERC20 with test stolen from OZ ([#22](https://github.com/1inch/solidity-utils/pull/22); [c5bfac8](https://github.com/1inch/solidity-utils/commit/c5bfac87a1f6801462c158cecc10726baeeed91c)).
- Added script to generate documentation for tests ([#21](https://github.com/1inch/solidity-utils/pull/21); [35d7708](https://github.com/1inch/solidity-utils/commit/35d77084f77a8b8b638a90243b00e7399ac334e4)).
- Feature/revert reason forwarder ([#20](https://github.com/1inch/solidity-utils/pull/20); [28fdca6](https://github.com/1inch/solidity-utils/commit/28fdca65bf65360fd462ff84e508170b68a59ef7)).
- Autopublish new packages ([#19](https://github.com/1inch/solidity-utils/pull/19); [354a1f5](https://github.com/1inch/solidity-utils/commit/354a1f55ed9828023b05e747a0be77f90281095a)).
- 0.8 improvements ([#16](https://github.com/1inch/solidity-utils/pull/16); [4f7aaee](https://github.com/1inch/solidity-utils/commit/4f7aaee81b2e9d12a69d44e280518b9b7352571e)).
- Update ci.yml ([#17](https://github.com/1inch/solidity-utils/pull/17); [e5e959e](https://github.com/1inch/solidity-utils/commit/e5e959e3cac831c63d190f6fe493a834c0686fad)).
- Feature/custom errors ([#15](https://github.com/1inch/solidity-utils/pull/15); [7315979](https://github.com/1inch/solidity-utils/commit/7315979603520e319c74ad2acbef1b0fe9273751)).
- Fix docs generation util ([#14](https://github.com/1inch/solidity-utils/pull/14); [651fdfa](https://github.com/1inch/solidity-utils/commit/651fdfa58e88d809f45576f7878f8d1813f04bd8)).
- update logo readmi for dark-theme ([#13](https://github.com/1inch/solidity-utils/pull/13); [025ec9a](https://github.com/1inch/solidity-utils/commit/025ec9aec1887f84c430365a2ecdfe6d06d4d0de)).
- [SC-333] Patch UniERC20 ([#12](https://github.com/1inch/solidity-utils/pull/12); [cd86301](https://github.com/1inch/solidity-utils/commit/cd863012445f23d1a409b5319651aff2e61623a1)).
- Feature/typescript support ([#10](https://github.com/1inch/solidity-utils/pull/10); [b8e5043](https://github.com/1inch/solidity-utils/commit/b8e50436622dc1913272a3b7bd9ea5265ef6ffc9)).
- Introduce AddressArray get to output array ([#11](https://github.com/1inch/solidity-utils/pull/11); [f65ec3d](https://github.com/1inch/solidity-utils/commit/f65ec3d4efc6d5595ee6c336cde41da4749490e2)).
- [SC-298] Utils for permit tests ([#9](https://github.com/1inch/solidity-utils/pull/9); [a0516f8](https://github.com/1inch/solidity-utils/commit/a0516f8181b50b94c3c829c3677c86d5dd42a672)).
- Improve evm profiling script ([#7](https://github.com/1inch/solidity-utils/pull/7); [19e1cb6](https://github.com/1inch/solidity-utils/commit/19e1cb64a32a8c74fd04def1d270a4d7ce49b581)).
- [SC-217] Added tests for Permitable contract ([#6](https://github.com/1inch/solidity-utils/pull/6); [f4afe1b](https://github.com/1inch/solidity-utils/commit/f4afe1be6b4775cfe6c0639850e39ba4d6b57018)).
- Added AddressArray, AddressSet libraries ([#8](https://github.com/1inch/solidity-utils/pull/8); [1eb38b5](https://github.com/1inch/solidity-utils/commit/1eb38b507664a6e84261848f783c826f6fc59857)).
- String util ([#5](https://github.com/1inch/solidity-utils/pull/5); [75748ce](https://github.com/1inch/solidity-utils/commit/75748ce41610846271e86330b4c5945b47c52858)).
- Added RevertReasonParserTest ([#4](https://github.com/1inch/solidity-utils/pull/4); [45da614](https://github.com/1inch/solidity-utils/commit/45da614a88bebf3b3aa700fb8232773e985ae51a)).
- Add docify 0.8 ([#2](https://github.com/1inch/solidity-utils/pull/2); [8d7a2ce](https://github.com/1inch/solidity-utils/commit/8d7a2ce301bfa2d8bb950a50941be022f0cb23e1)).
