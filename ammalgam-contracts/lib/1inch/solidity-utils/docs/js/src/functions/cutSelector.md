[**@1inch/solidity-utils**](../../README.md) • **Docs**

***

[@1inch/solidity-utils](../../README.md) / [src](../README.md) / cutSelector

# Function: cutSelector()

> **cutSelector**(`data`): `string`

## Parameters

• **data**: `string`

The transaction data string from which to trim the selector.

## Returns

`string`

The trimmed selector string.

## Defined in

[src/permit.ts:58](https://github.com/1inch/solidity-utils/blob/f9426ba6dab1eac9ac07fe3976b8d1cb2d2e5ba1/src/permit.ts#L58)
