[**@1inch/solidity-utils**](../../README.md) • **Docs**

***

[@1inch/solidity-utils](../../README.md) / [src](../README.md) / withTarget

# Function: withTarget()

> **withTarget**(`target`, `data`): `string`

## Parameters

• **target**: `string` \| `bigint`

The target address or value to prepend.

• **data**: `string` \| `bigint`

The data string to be concatenated after trimming.

## Returns

`string`

A concatenated string of target and data.

## Defined in

[src/permit.ts:339](https://github.com/1inch/solidity-utils/blob/f9426ba6dab1eac9ac07fe3976b8d1cb2d2e5ba1/src/permit.ts#L339)
