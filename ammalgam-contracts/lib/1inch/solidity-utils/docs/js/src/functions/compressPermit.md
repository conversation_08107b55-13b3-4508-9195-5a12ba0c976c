[**@1inch/solidity-utils**](../../README.md) • **Docs**

***

[@1inch/solidity-utils](../../README.md) / [src](../README.md) / compressPermit

# Function: compressPermit()

> **compressPermit**(`permit`): `string`

## Parameters

• **permit**: `string`

The full permit function call string.

## Returns

`string`

A compressed permit string.

## Defined in

[src/permit.ts:352](https://github.com/1inch/solidity-utils/blob/f9426ba6dab1eac9ac07fe3976b8d1cb2d2e5ba1/src/permit.ts#L352)
