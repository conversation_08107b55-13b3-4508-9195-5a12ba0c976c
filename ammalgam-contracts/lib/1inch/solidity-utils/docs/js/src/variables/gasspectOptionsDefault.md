[**@1inch/solidity-utils**](../../README.md) • **Docs**

***

[@1inch/solidity-utils](../../README.md) / [src](../README.md) / gasspectOptionsDefault

# Variable: gasspectOptionsDefault

> `const` **gasspectOptionsDefault**: `object`

## Type declaration

### args

> **args**: `boolean` = `false`

### minOpGasCost

> **minOpGasCost**: `number` = `300`

### res

> **res**: `boolean` = `false`

## Defined in

[src/profileEVM.ts:11](https://github.com/1inch/solidity-utils/blob/f9426ba6dab1eac9ac07fe3976b8d1cb2d2e5ba1/src/profileEVM.ts#L11)
