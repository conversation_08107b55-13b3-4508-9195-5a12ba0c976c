[**@1inch/solidity-utils**](../../README.md) • **Docs**

***

[@1inch/solidity-utils](../../README.md) / [hardhat-setup](../README.md) / Etherscan

# Type Alias: Etherscan

> **Etherscan**: `object`

## Type declaration

### apiKey

> **apiKey**: `object`

#### Index Signature

 \[`key`: `string`\]: `string`

### customChains

> **customChains**: `ChainConfig`[]

## Param

Dictionary of API keys for accessing Etherscan, indexed by network name.

## Param

Array of custom blockchain network configurations.

## Defined in

[hardhat-setup/networks.ts:11](https://github.com/1inch/solidity-utils/blob/f9426ba6dab1eac9ac07fe3976b8d1cb2d2e5ba1/hardhat-setup/networks.ts#L11)
