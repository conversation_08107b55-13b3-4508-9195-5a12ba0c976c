name: CI

on:
  workflow_dispatch:
  push:
    branches: 
      - master
      - develop
      - 'run-ci/**'
  pull_request:
    branches: [ master, develop, test-pr ]
    types: [opened, synchronize, reopened, labeled]

concurrency: 
  group: ${{ github.workflow }}-${{ github.head_ref || github.run_id }}
  cancel-in-progress: true

env:
  NODE_ENV: test
jobs:
  # ==== Job: Build and test mangrove-core
  check:
    runs-on: ubuntu-22.0-4core

    steps:

    - name: Checkout
      uses: actions/checkout@v4
      with:
        submodules: recursive

    # == verify changelog has been updated ==
    - name: Check changelog updated
      uses: mangrovedao/.github/.github/actions/changelog-updated@master

    - name: Yarn setup (caching yarn dependencies)
      uses: actions/setup-node@v4
      with:
        cache: 'yarn'
        
    - run: yarn install --immutable

    - name: Foundry Setup
      uses: mangrovedao/.github/.github/actions/foundry-setup@master

    - name: Build
      run: yarn run build

    # Pinned forks are used in tests and utilize secrets. For PRs from forks these are not available
    # Let the tests attempt to run with demo endpoints which are more unstable.
    - name: Solidity Tests (fast)
      run: yarn run test:fast
      env:
        POLYGON_NODE_URL: ${{ secrets.POLYGON_NODE_URL || 'https://polygon.llamarpc.com' }}
        MUMBAI_NODE_URL: ${{ secrets.MUMBAI_NODE_URL || 'unused' }}

    # Memory intensive tests run without -vvv as GitHub actions otherwise cancels the step due to memory usage
    - name: Solidity Tests (memory intensive)
      run: yarn run test:memory-intensive

      # For push runs we also create a coverage report
    - name: Create coverage report
      if: github.event_name != 'pull_request' 
      run: forge coverage --report lcov
      env:
        POLYGON_NODE_URL: ${{ secrets.POLYGON_NODE_URL }}
        MUMBAI_NODE_URL: ${{ secrets.MUMBAI_NODE_URL }}

     # == Send coverage report to Coveralls == 
     # Coverallsapp by default uses GITHUB_SHA but that does not necessarily correspond
     # to HEAD because a branch is checked out. We here find the actual SHA for HEAD.
    - name: Set Coveralls vars
      id: coveralls_vars
      if: github.event_name != 'pull_request' 
      run: echo "::set-output name=sha_for_head::$(git rev-parse HEAD)"

    - name: Upload to Coveralls
      uses: coverallsapp/github-action@master
      if: github.event_name != 'pull_request' 
      with:
        git-commit: ${{ steps.coveralls_vars.outputs.sha_for_head }}
        github-token: ${{ secrets.GITHUB_TOKEN }}
        path-to-lcov: lcov.info
        base-path	: .
        flag-name: solidity

    # NOTE: uncomment once you can manage forge test 
    # outputs in gh actions
    # - name: Mangrove Solidity Test Report
    #   uses: dorny/test-reporter@v1
    #   if: ${{ env.mangrove_built && (success() || failure()) }}
    #   with:
    #     name: Solidity Tests                  # Name of the check run which will be created
    #     path: ./solidity-mocha-test-report.json # Path to test results
    #     reporter: mocha-json                  # Format of test results

    # == check docs can be created ==
    - run: yarn run doc

    # == check precommit works ==
    - run: yarn run precommit
