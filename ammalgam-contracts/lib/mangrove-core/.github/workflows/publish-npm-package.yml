# Publishes npm package with a version bump chosen by the user.
# 
# From the master branch, package bumps can be patch, minor, or major.
#
# From the develop branch, package bumps can only be prerelease, prepatch, preminor, or premajor.
name: "Publish npm package"

on:
  workflow_dispatch:
    inputs:
      versionBumpStrategy:
        description: 'Version bump strategy'
        required: true
        default: 'prerelease'
        type: choice
        options:
        - prerelease
        - prepatch
        - preminor
        - premajor
        - patch
        - minor
        - major

jobs:
  call-reusable-workflow:
    name: "Publish"
    uses: mangrovedao/.github/.github/workflows/reusable-publish-npm-package.yml@master
    with:
      versionBumpStrategy: ${{ inputs.versionBumpStrategy }}
      withFoundry: true
    secrets:
      BOT_GH_PAT_TOKEN: ${{ secrets.BOT_GH_PAT_TOKEN }}
      BOT_NPM_TOKEN: ${{ secrets.BOT_NPM_TOKEN }}
      BOT_GPG_PRIVATE_KEY: ${{ secrets.BOT_GPG_PRIVATE_KEY }}
      BOT_GPG_PASSPHRASE: ${{ secrets.BOT_GPG_PASSPHRASE }}