{"abi": [{"type": "constructor", "inputs": [{"name": "_symbol", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "symbol", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}], "bytecode": {"object": "0x6080604052348015600e575f5ffd5b5060405160c938038060c9833981016040819052602991602f565b5f556045565b5f60208284031215603e575f5ffd5b5051919050565b60798060505f395ff3fe6080604052348015600e575f5ffd5b50600436106026575f3560e01c806395d89b4114602a575b5f5ffd5b60315f5481565b60405190815260200160405180910390f3fea2646970667358221220106e3dfdfd0d620e68632627ee7c58348a5fbc1a9c80f1b417220e7755f11abd64736f6c634300081c0033", "sourceMap": "58:148:184:-:0;;;128:76;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;181:6;:16;58:148;;14:184:190;84:6;137:2;125:9;116:7;112:23;108:32;105:52;;;153:1;150;143:12;105:52;-1:-1:-1;176:16:190;;14:184;-1:-1:-1;14:184:190:o;:::-;58:148:184;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "0x6080604052348015600e575f5ffd5b50600436106026575f3560e01c806395d89b4114602a575b5f5ffd5b60315f5481565b60405190815260200160405180910390f3fea2646970667358221220106e3dfdfd0d620e68632627ee7c58348a5fbc1a9c80f1b417220e7755f11abd64736f6c634300081c0033", "sourceMap": "58:148:184:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;100:21;;;;;;;;;160:25:190;;;148:2;133:18;100:21:184;;;;;;", "linkReferences": {}}, "methodIdentifiers": {"symbol()": "95d89b41"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"_symbol\",\"type\":\"bytes32\"}],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"inputs\":[],\"name\":\"symbol\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"test/stubs/TokenWithBytes32SymbolStub.sol\":\"TokenWithBytes32SymbolStub\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":1inch/=lib/1inch/\",\":@1inch/=lib/1inch/\",\":@mangrovedao/mangrove-core/=lib/mangrove-core/\",\":@mgv/forge-std/=lib/mangrove-core/lib/forge-std/src/\",\":@mgv/lib/=lib/mangrove-core/lib/\",\":@mgv/script/=lib/mangrove-core/script/\",\":@mgv/src/=lib/mangrove-core/src/\",\":@mgv/test/=lib/mangrove-core/test/\",\":@morpho-org/morpho-blue/=lib/morpho-blue/\",\":@openzeppelin/=lib/openzeppelin-contracts/\",\":ExcessivelySafeCall/=lib/ExcessivelySafeCall/src/\",\":core/=lib/mangrove-core/lib/core/\",\":ds-test/=lib/ExcessivelySafeCall/lib/ds-test/src/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":forge-std/=lib/openzeppelin-contracts/lib/forge-std/src/\",\":halmos-cheatcodes/=lib/morpho-blue/lib/halmos-cheatcodes/src/\",\":mangrove-core/=lib/mangrove-core/\",\":morpho-blue/=lib/morpho-blue/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":preprocessed/=lib/mangrove-core/lib/preprocessed/\"]},\"sources\":{\"test/stubs/TokenWithBytes32SymbolStub.sol\":{\"keccak256\":\"0xb6c9a55e95d18c11d387872fbda6af2da5b148fb35e4a96af211b6d7a1e7226f\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://da64a93d32206b664304e453b7671630d2d48871ecb4ebdcf1c5820d5cc45440\",\"dweb:/ipfs/QmcpgtaeNVesQbH2ebNDHuNSVnDujwJCNTA3rxdGLKiCs2\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "bytes32", "name": "_symbol", "type": "bytes32"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "symbol", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["1inch/=lib/1inch/", "@1inch/=lib/1inch/", "@mangrovedao/mangrove-core/=lib/mangrove-core/", "@mgv/forge-std/=lib/mangrove-core/lib/forge-std/src/", "@mgv/lib/=lib/mangrove-core/lib/", "@mgv/script/=lib/mangrove-core/script/", "@mgv/src/=lib/mangrove-core/src/", "@mgv/test/=lib/mangrove-core/test/", "@morpho-org/morpho-blue/=lib/morpho-blue/", "@openzeppelin/=lib/openzeppelin-contracts/", "ExcessivelySafeCall/=lib/ExcessivelySafeCall/src/", "core/=lib/mangrove-core/lib/core/", "ds-test/=lib/ExcessivelySafeCall/lib/ds-test/src/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "forge-std/=lib/openzeppelin-contracts/lib/forge-std/src/", "halmos-cheatcodes/=lib/morpho-blue/lib/halmos-cheatcodes/src/", "mangrove-core/=lib/mangrove-core/", "morpho-blue/=lib/morpho-blue/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "preprocessed/=lib/mangrove-core/lib/preprocessed/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"test/stubs/TokenWithBytes32SymbolStub.sol": "TokenWithBytes32SymbolStub"}, "evmVersion": "cancun", "libraries": {}}, "sources": {"test/stubs/TokenWithBytes32SymbolStub.sol": {"keccak256": "0xb6c9a55e95d18c11d387872fbda6af2da5b148fb35e4a96af211b6d7a1e7226f", "urls": ["bzz-raw://da64a93d32206b664304e453b7671630d2d48871ecb4ebdcf1c5820d5cc45440", "dweb:/ipfs/QmcpgtaeNVesQbH2ebNDHuNSVnDujwJCNTA3rxdGLKiCs2"], "license": "MIT"}}, "version": 1}, "id": 184}