{"abi": [{"type": "error", "name": "IndexOutOfBounds", "inputs": []}, {"type": "error", "name": "OutputArrayTooSmall", "inputs": []}, {"type": "error", "name": "PopFromEmptyArray", "inputs": []}], "bytecode": {"object": "0x60556032600b8282823980515f1a607314602657634e487b7160e01b5f525f60045260245ffd5b305f52607381538281f3fe730000000000000000000000000000000000000000301460806040525f5ffdfea2646970667358221220377c84669867222b71cb33641809eb31b362f3d315ba50bd1d41094787788dee64736f6c634300081c0033", "sourceMap": "355:8751:40:-:0;;;;;;;;;;;;;;;-1:-1:-1;;;355:8751:40;;;;;;;;;;;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "0x730000000000000000000000000000000000000000301460806040525f5ffdfea2646970667358221220377c84669867222b71cb33641809eb31b362f3d315ba50bd1d41094787788dee64736f6c634300081c0033", "sourceMap": "355:8751:40:-:0;;;;;;;;", "linkReferences": {}}, "methodIdentifiers": {}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"name\":\"IndexOutOfBounds\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"OutputArrayTooSmall\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"PopFromEmptyArray\",\"type\":\"error\"}],\"devdoc\":{\"details\":\"This library provides basic functionalities such as push, pop, set, and retrieval of addresses in a storage-efficient manner.\",\"errors\":{\"IndexOutOfBounds()\":[{\"details\":\"Error thrown when attempting to access an index outside the bounds of the array.\"}],\"OutputArrayTooSmall()\":[{\"details\":\"Error thrown when the output array provided for getting the list of addresses is too small.\"}],\"PopFromEmptyArray()\":[{\"details\":\"Error thrown when attempting to pop an element from an empty array.\"}]},\"kind\":\"dev\",\"methods\":{},\"title\":\"AddressArray\",\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"notice\":\"Implements a dynamic array of addresses using a mapping for storage efficiency, with the array length stored at index 0.\",\"version\":1}},\"settings\":{\"compilationTarget\":{\"lib/1inch/solidity-utils/contracts/libraries/AddressArray.sol\":\"AddressArray\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":1inch/=lib/1inch/\",\":@1inch/=lib/1inch/\",\":@mangrovedao/mangrove-core/=lib/mangrove-core/\",\":@mgv/forge-std/=lib/mangrove-core/lib/forge-std/src/\",\":@mgv/lib/=lib/mangrove-core/lib/\",\":@mgv/script/=lib/mangrove-core/script/\",\":@mgv/src/=lib/mangrove-core/src/\",\":@mgv/test/=lib/mangrove-core/test/\",\":@morpho-org/morpho-blue/=lib/morpho-blue/\",\":@openzeppelin/=lib/openzeppelin-contracts/\",\":ExcessivelySafeCall/=lib/ExcessivelySafeCall/src/\",\":core/=lib/mangrove-core/lib/core/\",\":ds-test/=lib/ExcessivelySafeCall/lib/ds-test/src/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":forge-std/=lib/openzeppelin-contracts/lib/forge-std/src/\",\":halmos-cheatcodes/=lib/morpho-blue/lib/halmos-cheatcodes/src/\",\":mangrove-core/=lib/mangrove-core/\",\":morpho-blue/=lib/morpho-blue/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":preprocessed/=lib/mangrove-core/lib/preprocessed/\"]},\"sources\":{\"lib/1inch/solidity-utils/contracts/libraries/AddressArray.sol\":{\"keccak256\":\"0x7895eaf7b55d9612b22ec586970488de51436c021b9f9414b3c27c3583c8856e\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://e43897dfeff84e2983b017ab100d74061a6b9bed4618ec37a7cbb68bf064ac22\",\"dweb:/ipfs/Qmejoj4CuNr1giwGBDG7SgRtfYmi64fgy2DsGP2AAW9gH9\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [], "type": "error", "name": "IndexOutOfBounds"}, {"inputs": [], "type": "error", "name": "OutputArrayTooSmall"}, {"inputs": [], "type": "error", "name": "PopFromEmptyArray"}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["1inch/=lib/1inch/", "@1inch/=lib/1inch/", "@mangrovedao/mangrove-core/=lib/mangrove-core/", "@mgv/forge-std/=lib/mangrove-core/lib/forge-std/src/", "@mgv/lib/=lib/mangrove-core/lib/", "@mgv/script/=lib/mangrove-core/script/", "@mgv/src/=lib/mangrove-core/src/", "@mgv/test/=lib/mangrove-core/test/", "@morpho-org/morpho-blue/=lib/morpho-blue/", "@openzeppelin/=lib/openzeppelin-contracts/", "ExcessivelySafeCall/=lib/ExcessivelySafeCall/src/", "core/=lib/mangrove-core/lib/core/", "ds-test/=lib/ExcessivelySafeCall/lib/ds-test/src/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "forge-std/=lib/openzeppelin-contracts/lib/forge-std/src/", "halmos-cheatcodes/=lib/morpho-blue/lib/halmos-cheatcodes/src/", "mangrove-core/=lib/mangrove-core/", "morpho-blue/=lib/morpho-blue/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "preprocessed/=lib/mangrove-core/lib/preprocessed/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"lib/1inch/solidity-utils/contracts/libraries/AddressArray.sol": "AddressArray"}, "evmVersion": "cancun", "libraries": {}}, "sources": {"lib/1inch/solidity-utils/contracts/libraries/AddressArray.sol": {"keccak256": "0x7895eaf7b55d9612b22ec586970488de51436c021b9f9414b3c27c3583c8856e", "urls": ["bzz-raw://e43897dfeff84e2983b017ab100d74061a6b9bed4618ec37a7cbb68bf064ac22", "dweb:/ipfs/Qmejoj4CuNr1giwGBDG7SgRtfYmi64fgy2DsGP2AAW9gH9"], "license": "MIT"}}, "version": 1}, "id": 40}