{"abi": [{"type": "error", "name": "InvalidRevertReason", "inputs": []}], "bytecode": {"object": "0x60556032600b8282823980515f1a607314602657634e487b7160e01b5f525f60045260245ffd5b305f52607381538281f3fe730000000000000000000000000000000000000000301460806040525f5ffdfea2646970667358221220555c8ea83e17f094e49af58702be34cdf0222b55dd6ca20e8dba64d6b7f6359f64736f6c634300081c0033", "sourceMap": "519:2645:42:-:0;;;;;;;;;;;;;;;-1:-1:-1;;;519:2645:42;;;;;;;;;;;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "0x730000000000000000000000000000000000000000301460806040525f5ffdfea2646970667358221220555c8ea83e17f094e49af58702be34cdf0222b55dd6ca20e8dba64d6b7f6359f64736f6c634300081c0033", "sourceMap": "519:2645:42:-:0;;;;;;;;", "linkReferences": {}}, "methodIdentifiers": {}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"name\":\"InvalidRevertReason\",\"type\":\"error\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"title\":\"RevertReasonParser\",\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"notice\":\"Library that allows to parse unsuccessful arbitrary calls revert reasons. See https://solidity.readthedocs.io/en/latest/control-structures.html#revert for details. Note that we assume revert reason being abi-encoded as Error(string) so it may fail to parse reason if structured reverts appear in the future. All unsuccessful parsings get encoded as Unknown(data) string\",\"version\":1}},\"settings\":{\"compilationTarget\":{\"lib/1inch/solidity-utils/contracts/libraries/RevertReasonParser.sol\":\"RevertReasonParser\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":1inch/=lib/1inch/\",\":@1inch/=lib/1inch/\",\":@mangrovedao/mangrove-core/=lib/mangrove-core/\",\":@mgv/forge-std/=lib/mangrove-core/lib/forge-std/src/\",\":@mgv/lib/=lib/mangrove-core/lib/\",\":@mgv/script/=lib/mangrove-core/script/\",\":@mgv/src/=lib/mangrove-core/src/\",\":@mgv/test/=lib/mangrove-core/test/\",\":@morpho-org/morpho-blue/=lib/morpho-blue/\",\":@openzeppelin/=lib/openzeppelin-contracts/\",\":ExcessivelySafeCall/=lib/ExcessivelySafeCall/src/\",\":core/=lib/mangrove-core/lib/core/\",\":ds-test/=lib/ExcessivelySafeCall/lib/ds-test/src/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":forge-std/=lib/openzeppelin-contracts/lib/forge-std/src/\",\":halmos-cheatcodes/=lib/morpho-blue/lib/halmos-cheatcodes/src/\",\":mangrove-core/=lib/mangrove-core/\",\":morpho-blue/=lib/morpho-blue/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":preprocessed/=lib/mangrove-core/lib/preprocessed/\"]},\"sources\":{\"lib/1inch/solidity-utils/contracts/libraries/RevertReasonParser.sol\":{\"keccak256\":\"0x596a583d37871e754fa1bf9e878a0f1d42a1633ae316e4e50cc194ccc295b223\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://71a10c02002ea17696bbc08f244af72a130856b0b456c2dd3df65bd6b5c5c5a4\",\"dweb:/ipfs/QmV9G8eFn1fSw964ukyhyJ6W1C2oiaAxtfDBAfGZ1PYj1A\"]},\"lib/1inch/solidity-utils/contracts/libraries/StringUtil.sol\":{\"keccak256\":\"0xb05bff1a3a5461b1f16248220067a2bd52e5c7e41c14be0aebb6520985b67ccb\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://03495f22f0128969aef7dd46f65e215851cbd52cfa4d87eac0c930c5cd84826d\",\"dweb:/ipfs/QmekA9rM82XXPKdHpCwAYhirtSCB5AJo16TUvyBYVWhKhZ\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [], "type": "error", "name": "InvalidRevertReason"}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["1inch/=lib/1inch/", "@1inch/=lib/1inch/", "@mangrovedao/mangrove-core/=lib/mangrove-core/", "@mgv/forge-std/=lib/mangrove-core/lib/forge-std/src/", "@mgv/lib/=lib/mangrove-core/lib/", "@mgv/script/=lib/mangrove-core/script/", "@mgv/src/=lib/mangrove-core/src/", "@mgv/test/=lib/mangrove-core/test/", "@morpho-org/morpho-blue/=lib/morpho-blue/", "@openzeppelin/=lib/openzeppelin-contracts/", "ExcessivelySafeCall/=lib/ExcessivelySafeCall/src/", "core/=lib/mangrove-core/lib/core/", "ds-test/=lib/ExcessivelySafeCall/lib/ds-test/src/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "forge-std/=lib/openzeppelin-contracts/lib/forge-std/src/", "halmos-cheatcodes/=lib/morpho-blue/lib/halmos-cheatcodes/src/", "mangrove-core/=lib/mangrove-core/", "morpho-blue/=lib/morpho-blue/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "preprocessed/=lib/mangrove-core/lib/preprocessed/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"lib/1inch/solidity-utils/contracts/libraries/RevertReasonParser.sol": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "evmVersion": "cancun", "libraries": {}}, "sources": {"lib/1inch/solidity-utils/contracts/libraries/RevertReasonParser.sol": {"keccak256": "0x596a583d37871e754fa1bf9e878a0f1d42a1633ae316e4e50cc194ccc295b223", "urls": ["bzz-raw://71a10c02002ea17696bbc08f244af72a130856b0b456c2dd3df65bd6b5c5c5a4", "dweb:/ipfs/QmV9G8eFn1fSw964ukyhyJ6W1C2oiaAxtfDBAfGZ1PYj1A"], "license": "MIT"}, "lib/1inch/solidity-utils/contracts/libraries/StringUtil.sol": {"keccak256": "0xb05bff1a3a5461b1f16248220067a2bd52e5c7e41c14be0aebb6520985b67ccb", "urls": ["bzz-raw://03495f22f0128969aef7dd46f65e215851cbd52cfa4d87eac0c930c5cd84826d", "dweb:/ipfs/QmekA9rM82XXPKdHpCwAYhirtSCB5AJo16TUvyBYVWhKhZ"], "license": "MIT"}}, "version": 1}, "id": 42}