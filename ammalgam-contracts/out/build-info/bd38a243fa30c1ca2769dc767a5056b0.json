{"id": "bd38a243fa30c1ca2769dc767a5056b0", "source_id_to_path": {"0": "contracts/AmmalgamPair.sol", "1": "contracts/SaturationAndGeometricTWAPState.sol", "2": "contracts/factories/AmmalgamFactory.sol", "3": "contracts/factories/ERC20DebtLiquidityTokenFactory.sol", "4": "contracts/factories/ERC20LiquidityTokenFactory.sol", "5": "contracts/factories/ERC4626DebtTokenFactory.sol", "6": "contracts/factories/ERC4626DepositTokenFactory.sol", "7": "contracts/factories/NewTokensFactory.sol", "8": "contracts/interfaces/IAmmalgamPair.sol", "9": "contracts/interfaces/ISaturationAndGeometricTWAPState.sol", "10": "contracts/interfaces/callbacks/IAmmalgamCallee.sol", "11": "contracts/interfaces/callbacks/ITransferValidator.sol", "12": "contracts/interfaces/factories/IAmmalgamFactory.sol", "13": "contracts/interfaces/factories/IFactoryCallback.sol", "14": "contracts/interfaces/factories/INewTokensFactory.sol", "15": "contracts/interfaces/factories/ITokenFactory.sol", "16": "contracts/interfaces/tokens/IAmmalgamERC20.sol", "17": "contracts/interfaces/tokens/IERC20DebtToken.sol", "18": "contracts/interfaces/tokens/IPluginRegistry.sol", "19": "contracts/interfaces/tokens/ITokenController.sol", "20": "contracts/libraries/Convert.sol", "21": "contracts/libraries/GeometricTWAP.sol", "22": "contracts/libraries/Interest.sol", "23": "contracts/libraries/Liquidation.sol", "24": "contracts/libraries/QuadraticSwapFees.sol", "25": "contracts/libraries/Saturation.sol", "26": "contracts/libraries/TickMath.sol", "27": "contracts/libraries/TokenSymbol.sol", "28": "contracts/libraries/Uint16Set.sol", "29": "contracts/libraries/Validation.sol", "30": "contracts/libraries/constants.sol", "31": "contracts/tokens/ERC20Base.sol", "32": "contracts/tokens/ERC20DebtBase.sol", "33": "contracts/tokens/ERC20DebtLiquidityToken.sol", "34": "contracts/tokens/ERC20LiquidityToken.sol", "35": "contracts/tokens/ERC4626DebtToken.sol", "36": "contracts/tokens/ERC4626DepositToken.sol", "37": "contracts/tokens/PluginRegistry.sol", "38": "contracts/tokens/TokenController.sol", "39": "contracts/utils/deployHelper.sol", "40": "lib/1inch/solidity-utils/contracts/libraries/AddressArray.sol", "41": "lib/1inch/solidity-utils/contracts/libraries/AddressSet.sol", "42": "lib/1inch/token-plugins/contracts/ERC20Hooks.sol", "43": "lib/1inch/token-plugins/contracts/interfaces/IERC20Hooks.sol", "44": "lib/1inch/token-plugins/contracts/interfaces/IHook.sol", "45": "lib/1inch/token-plugins/contracts/libs/ReentrancyGuard.sol", "46": "lib/ExcessivelySafeCall/src/ExcessivelySafeCall.sol", "47": "lib/mangrove-core/lib/core/BitLib.sol", "48": "lib/morpho-blue/src/libraries/MathLib.sol", "49": "lib/openzeppelin-contracts/contracts/access/AccessControl.sol", "50": "lib/openzeppelin-contracts/contracts/access/IAccessControl.sol", "51": "lib/openzeppelin-contracts/contracts/access/Ownable.sol", "52": "lib/openzeppelin-contracts/contracts/governance/utils/IVotes.sol", "53": "lib/openzeppelin-contracts/contracts/governance/utils/Votes.sol", "54": "lib/openzeppelin-contracts/contracts/interfaces/IERC1363.sol", "55": "lib/openzeppelin-contracts/contracts/interfaces/IERC165.sol", "56": "lib/openzeppelin-contracts/contracts/interfaces/IERC20.sol", "57": "lib/openzeppelin-contracts/contracts/interfaces/IERC3156FlashBorrower.sol", "58": "lib/openzeppelin-contracts/contracts/interfaces/IERC3156FlashLender.sol", "59": "lib/openzeppelin-contracts/contracts/interfaces/IERC4626.sol", "60": "lib/openzeppelin-contracts/contracts/interfaces/IERC5267.sol", "61": "lib/openzeppelin-contracts/contracts/interfaces/IERC5805.sol", "62": "lib/openzeppelin-contracts/contracts/interfaces/IERC6372.sol", "63": "lib/openzeppelin-contracts/contracts/interfaces/draft-IERC6093.sol", "64": "lib/openzeppelin-contracts/contracts/token/ERC20/ERC20.sol", "65": "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "66": "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20Burnable.sol", "67": "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20FlashMint.sol", "68": "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20Permit.sol", "69": "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20Votes.sol", "70": "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC4626.sol", "71": "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol", "72": "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Permit.sol", "73": "lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol", "74": "lib/openzeppelin-contracts/contracts/utils/Context.sol", "75": "lib/openzeppelin-contracts/contracts/utils/Nonces.sol", "76": "lib/openzeppelin-contracts/contracts/utils/Panic.sol", "77": "lib/openzeppelin-contracts/contracts/utils/Pausable.sol", "78": "lib/openzeppelin-contracts/contracts/utils/ShortStrings.sol", "79": "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol", "80": "lib/openzeppelin-contracts/contracts/utils/Strings.sol", "81": "lib/openzeppelin-contracts/contracts/utils/cryptography/ECDSA.sol", "82": "lib/openzeppelin-contracts/contracts/utils/cryptography/EIP712.sol", "83": "lib/openzeppelin-contracts/contracts/utils/cryptography/MessageHashUtils.sol", "84": "lib/openzeppelin-contracts/contracts/utils/introspection/ERC165.sol", "85": "lib/openzeppelin-contracts/contracts/utils/introspection/IERC165.sol", "86": "lib/openzeppelin-contracts/contracts/utils/math/Math.sol", "87": "lib/openzeppelin-contracts/contracts/utils/math/SafeCast.sol", "88": "lib/openzeppelin-contracts/contracts/utils/math/SignedMath.sol", "89": "lib/openzeppelin-contracts/contracts/utils/structs/Checkpoints.sol", "90": "lib/openzeppelin-contracts/contracts/utils/types/Time.sol", "91": "lib/openzeppelin-contracts/lib/forge-std/src/Base.sol", "92": "lib/openzeppelin-contracts/lib/forge-std/src/StdAssertions.sol", "93": "lib/openzeppelin-contracts/lib/forge-std/src/StdChains.sol", "94": "lib/openzeppelin-contracts/lib/forge-std/src/StdCheats.sol", "95": "lib/openzeppelin-contracts/lib/forge-std/src/StdError.sol", "96": "lib/openzeppelin-contracts/lib/forge-std/src/StdInvariant.sol", "97": "lib/openzeppelin-contracts/lib/forge-std/src/StdJson.sol", "98": "lib/openzeppelin-contracts/lib/forge-std/src/StdMath.sol", "99": "lib/openzeppelin-contracts/lib/forge-std/src/StdStorage.sol", "100": "lib/openzeppelin-contracts/lib/forge-std/src/StdStyle.sol", "101": "lib/openzeppelin-contracts/lib/forge-std/src/StdToml.sol", "102": "lib/openzeppelin-contracts/lib/forge-std/src/StdUtils.sol", "103": "lib/openzeppelin-contracts/lib/forge-std/src/Test.sol", "104": "lib/openzeppelin-contracts/lib/forge-std/src/Vm.sol", "105": "lib/openzeppelin-contracts/lib/forge-std/src/console.sol", "106": "lib/openzeppelin-contracts/lib/forge-std/src/console2.sol", "107": "lib/openzeppelin-contracts/lib/forge-std/src/interfaces/IMulticall3.sol", "108": "lib/openzeppelin-contracts/lib/forge-std/src/safeconsole.sol", "109": "test/InterestTests/InterestFixture.sol", "110": "test/VulnerabilityPOC/OwnerTransferSaturationVulnerabilityPOC.sol", "111": "test/example/PeripheralDelegationContractExample.sol", "112": "test/shared/FactoryPairTestFixture.sol", "113": "test/shared/StubErc20.sol", "114": "test/shared/utilities.sol", "115": "test/utils/DepletedAssetUtils.sol", "116": "test/utils/constants.sol"}, "language": "Solidity"}