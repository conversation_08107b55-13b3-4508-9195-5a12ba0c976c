{"id": "821ef3652c27b97a7eac7f9af7fd6043", "source_id_to_path": {"0": "contracts/interfaces/IAmmalgamPair.sol", "1": "contracts/interfaces/callbacks/ITransferValidator.sol", "2": "contracts/interfaces/tokens/IAmmalgamERC20.sol", "3": "contracts/interfaces/tokens/IERC20DebtToken.sol", "4": "contracts/interfaces/tokens/IPluginRegistry.sol", "5": "contracts/interfaces/tokens/ITokenController.sol", "6": "contracts/libraries/Convert.sol", "7": "contracts/libraries/constants.sol", "8": "contracts/tokens/ERC20Base.sol", "9": "contracts/tokens/ERC20DebtBase.sol", "10": "contracts/tokens/ERC4626DebtToken.sol", "11": "contracts/tokens/ERC4626DepositToken.sol", "12": "lib/1inch/solidity-utils/contracts/libraries/AddressArray.sol", "13": "lib/1inch/solidity-utils/contracts/libraries/AddressSet.sol", "14": "lib/1inch/token-plugins/contracts/ERC20Hooks.sol", "15": "lib/1inch/token-plugins/contracts/interfaces/IERC20Hooks.sol", "16": "lib/1inch/token-plugins/contracts/interfaces/IHook.sol", "17": "lib/1inch/token-plugins/contracts/libs/ReentrancyGuard.sol", "18": "lib/openzeppelin-contracts/contracts/access/Ownable.sol", "19": "lib/openzeppelin-contracts/contracts/interfaces/IERC1363.sol", "20": "lib/openzeppelin-contracts/contracts/interfaces/IERC165.sol", "21": "lib/openzeppelin-contracts/contracts/interfaces/IERC20.sol", "22": "lib/openzeppelin-contracts/contracts/interfaces/IERC4626.sol", "23": "lib/openzeppelin-contracts/contracts/interfaces/IERC5267.sol", "24": "lib/openzeppelin-contracts/contracts/interfaces/draft-IERC6093.sol", "25": "lib/openzeppelin-contracts/contracts/token/ERC20/ERC20.sol", "26": "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "27": "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20Permit.sol", "28": "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC4626.sol", "29": "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol", "30": "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Permit.sol", "31": "lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol", "32": "lib/openzeppelin-contracts/contracts/utils/Context.sol", "33": "lib/openzeppelin-contracts/contracts/utils/Nonces.sol", "34": "lib/openzeppelin-contracts/contracts/utils/Panic.sol", "35": "lib/openzeppelin-contracts/contracts/utils/ShortStrings.sol", "36": "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol", "37": "lib/openzeppelin-contracts/contracts/utils/Strings.sol", "38": "lib/openzeppelin-contracts/contracts/utils/cryptography/ECDSA.sol", "39": "lib/openzeppelin-contracts/contracts/utils/cryptography/EIP712.sol", "40": "lib/openzeppelin-contracts/contracts/utils/cryptography/MessageHashUtils.sol", "41": "lib/openzeppelin-contracts/contracts/utils/introspection/IERC165.sol", "42": "lib/openzeppelin-contracts/contracts/utils/math/Math.sol", "43": "lib/openzeppelin-contracts/contracts/utils/math/SafeCast.sol", "44": "lib/openzeppelin-contracts/contracts/utils/math/SignedMath.sol", "45": "lib/openzeppelin-contracts/lib/forge-std/src/Base.sol", "46": "lib/openzeppelin-contracts/lib/forge-std/src/StdAssertions.sol", "47": "lib/openzeppelin-contracts/lib/forge-std/src/StdChains.sol", "48": "lib/openzeppelin-contracts/lib/forge-std/src/StdCheats.sol", "49": "lib/openzeppelin-contracts/lib/forge-std/src/StdError.sol", "50": "lib/openzeppelin-contracts/lib/forge-std/src/StdInvariant.sol", "51": "lib/openzeppelin-contracts/lib/forge-std/src/StdJson.sol", "52": "lib/openzeppelin-contracts/lib/forge-std/src/StdMath.sol", "53": "lib/openzeppelin-contracts/lib/forge-std/src/StdStorage.sol", "54": "lib/openzeppelin-contracts/lib/forge-std/src/StdStyle.sol", "55": "lib/openzeppelin-contracts/lib/forge-std/src/StdToml.sol", "56": "lib/openzeppelin-contracts/lib/forge-std/src/StdUtils.sol", "57": "lib/openzeppelin-contracts/lib/forge-std/src/Test.sol", "58": "lib/openzeppelin-contracts/lib/forge-std/src/Vm.sol", "59": "lib/openzeppelin-contracts/lib/forge-std/src/console.sol", "60": "lib/openzeppelin-contracts/lib/forge-std/src/console2.sol", "61": "lib/openzeppelin-contracts/lib/forge-std/src/interfaces/IMulticall3.sol", "62": "lib/openzeppelin-contracts/lib/forge-std/src/safeconsole.sol", "63": "test/ERC4626RoundingVulnerabilityPOC.sol"}, "language": "Solidity"}