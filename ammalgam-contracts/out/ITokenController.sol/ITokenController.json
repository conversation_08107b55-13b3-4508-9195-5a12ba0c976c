{"abi": [{"type": "function", "name": "externalLiquidity", "inputs": [], "outputs": [{"name": "", "type": "uint112", "internalType": "uint112"}], "stateMutability": "view"}, {"type": "function", "name": "getReserves", "inputs": [], "outputs": [{"name": "reserveXAssets", "type": "uint112", "internalType": "uint112"}, {"name": "reserveYAssets", "type": "uint112", "internalType": "uint112"}, {"name": "lastTimestamp", "type": "uint32", "internalType": "uint32"}], "stateMutability": "view"}, {"type": "function", "name": "referenceReserves", "inputs": [], "outputs": [{"name": "referenceReserveX", "type": "uint112", "internalType": "uint112"}, {"name": "referenceReserveY", "type": "uint112", "internalType": "uint112"}], "stateMutability": "view"}, {"type": "function", "name": "tokens", "inputs": [{"name": "tokenType", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "address", "internalType": "contract IAmmalgamERC20"}], "stateMutability": "view"}, {"type": "function", "name": "totalAssets", "inputs": [], "outputs": [{"name": "", "type": "uint128[6]", "internalType": "uint128[6]"}], "stateMutability": "view"}, {"type": "function", "name": "underlyingTokens", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract IERC20"}, {"name": "", "type": "address", "internalType": "contract IERC20"}], "stateMutability": "view"}, {"type": "function", "name": "updateExternalLiquidity", "inputs": [{"name": "_externalLiquidity", "type": "uint112", "internalType": "uint112"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "event", "name": "BurnBadDebt", "inputs": [{"name": "borrower", "type": "address", "indexed": true, "internalType": "address"}, {"name": "tokenType", "type": "uint256", "indexed": true, "internalType": "uint256"}, {"name": "badDebtAssets", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "badDebtShares", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "InterestAccrued", "inputs": [{"name": "depositLAssets", "type": "uint128", "indexed": false, "internalType": "uint128"}, {"name": "depositXAssets", "type": "uint128", "indexed": false, "internalType": "uint128"}, {"name": "depositYAssets", "type": "uint128", "indexed": false, "internalType": "uint128"}, {"name": "borrowLAssets", "type": "uint128", "indexed": false, "internalType": "uint128"}, {"name": "borrowXAssets", "type": "uint128", "indexed": false, "internalType": "uint128"}, {"name": "borrowYAssets", "type": "uint128", "indexed": false, "internalType": "uint128"}], "anonymous": false}, {"type": "event", "name": "Sync", "inputs": [{"name": "reserveXAssets", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "reserveYAssets", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "UpdateExternalLiquidity", "inputs": [{"name": "externalLiquidity", "type": "uint112", "indexed": false, "internalType": "uint112"}], "anonymous": false}], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {"externalLiquidity()": "861d4d88", "getReserves()": "0902f1ac", "referenceReserves()": "2c0e7587", "tokens(uint256)": "4f64b2be", "totalAssets()": "01e1d114", "underlyingTokens()": "bd27dc9f", "updateExternalLiquidity(uint112)": "6945e18f"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"borrower\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"tokenType\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"badDebtAssets\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"badDebtShares\",\"type\":\"uint256\"}],\"name\":\"BurnBadDebt\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint128\",\"name\":\"depositLAssets\",\"type\":\"uint128\"},{\"indexed\":false,\"internalType\":\"uint128\",\"name\":\"depositXAssets\",\"type\":\"uint128\"},{\"indexed\":false,\"internalType\":\"uint128\",\"name\":\"depositYAssets\",\"type\":\"uint128\"},{\"indexed\":false,\"internalType\":\"uint128\",\"name\":\"borrowLAssets\",\"type\":\"uint128\"},{\"indexed\":false,\"internalType\":\"uint128\",\"name\":\"borrowXAssets\",\"type\":\"uint128\"},{\"indexed\":false,\"internalType\":\"uint128\",\"name\":\"borrowYAssets\",\"type\":\"uint128\"}],\"name\":\"InterestAccrued\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"reserveXAssets\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"reserveYAssets\",\"type\":\"uint256\"}],\"name\":\"Sync\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint112\",\"name\":\"externalLiquidity\",\"type\":\"uint112\"}],\"name\":\"UpdateExternalLiquidity\",\"type\":\"event\"},{\"inputs\":[],\"name\":\"externalLiquidity\",\"outputs\":[{\"internalType\":\"uint112\",\"name\":\"\",\"type\":\"uint112\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getReserves\",\"outputs\":[{\"internalType\":\"uint112\",\"name\":\"reserveXAssets\",\"type\":\"uint112\"},{\"internalType\":\"uint112\",\"name\":\"reserveYAssets\",\"type\":\"uint112\"},{\"internalType\":\"uint32\",\"name\":\"lastTimestamp\",\"type\":\"uint32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"referenceReserves\",\"outputs\":[{\"internalType\":\"uint112\",\"name\":\"referenceReserveX\",\"type\":\"uint112\"},{\"internalType\":\"uint112\",\"name\":\"referenceReserveY\",\"type\":\"uint112\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"tokenType\",\"type\":\"uint256\"}],\"name\":\"tokens\",\"outputs\":[{\"internalType\":\"contract IAmmalgamERC20\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"totalAssets\",\"outputs\":[{\"internalType\":\"uint128[6]\",\"name\":\"\",\"type\":\"uint128[6]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"underlyingTokens\",\"outputs\":[{\"internalType\":\"contract IERC20\",\"name\":\"\",\"type\":\"address\"},{\"internalType\":\"contract IERC20\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint112\",\"name\":\"_externalLiquidity\",\"type\":\"uint112\"}],\"name\":\"updateExternalLiquidity\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"details\":\"The TokenController provides support to the AmmalgamPair contract for token management.\",\"events\":{\"BurnBadDebt(address,uint256,uint256,uint256)\":{\"details\":\"Emitted when bad debt is burned\",\"params\":{\"badDebtAssets\":\"The amount of bad debt assets being burned\",\"badDebtShares\":\"The amount of bad debt shares being burned\",\"borrower\":\"The address of the borrower\",\"tokenType\":\"The type of token being burned\"}},\"InterestAccrued(uint128,uint128,uint128,uint128,uint128,uint128)\":{\"details\":\"Emitted when Interest gets accrued\",\"params\":{\"borrowLAssets\":\"The amount of total `BORROW_L` assets in the pool after interest accrual\",\"borrowXAssets\":\"The amount of total `BORROW_X` assets in the pool after interest accrual\",\"borrowYAssets\":\"The amount of total `BORROW_Y` assets in the pool after interest accrual\",\"depositLAssets\":\"The amount of total `DEPOSIT_L` assets in the pool after interest accrual\",\"depositXAssets\":\"The amount of total `DEPOSIT_X` assets in the pool after interest accrual\",\"depositYAssets\":\"The amount of total `DEPOSIT_Y` assets in the pool after interest accrual\"}},\"Sync(uint256,uint256)\":{\"details\":\"Emitted when reserves are synchronized\",\"params\":{\"reserveXAssets\":\"The updated reserve for token X\",\"reserveYAssets\":\"The updated reserve for token Y\"}},\"UpdateExternalLiquidity(uint112)\":{\"details\":\"Emitted when external liquidity is updated\",\"params\":{\"externalLiquidity\":\"The updated value for external liquidity\"}}},\"kind\":\"dev\",\"methods\":{\"getReserves()\":{\"returns\":{\"lastTimestamp\":\"The timestamp of the last operation.\",\"reserveXAssets\":\"The current reserve of asset X.\",\"reserveYAssets\":\"The current reserve of asset Y.\"}},\"referenceReserves()\":{\"returns\":{\"referenceReserveX\":\"The reference reserve for asset X.\",\"referenceReserveY\":\"The reference reserve for asset Y.\"}},\"tokens(uint256)\":{\"params\":{\"tokenType\":\"The type of token for which the scaler is being computed.                  Can be one of BORROW_X, DEPOSIT_X, BORROW_Y, DEPOSIT_Y, BORROW_L, or DEPOSIT_L.\"},\"returns\":{\"_0\":\"The IAmmalgamERC20 token\"}},\"totalAssets()\":{\"details\":\"If the last lending state update is outdated (i.e., not matching the current block timestamp),      the function recalculates the assets based on the duration since the last update, the lending state,      and reserve balances. If the timestamp is current, the previous scaler (without recalculation) is returned.\",\"returns\":{\"_0\":\"totalAssets An array of six `uint128` values representing the total assets for each of the 6 amalgam token types.  These values may be adjusted based on the time elapsed since the last update. If the timestamp is up-to-date, the  previously calculated total assets are returned without recalculation.\"}},\"underlyingTokens()\":{\"returns\":{\"_0\":\"The addresses of the underlying tokens.\"}},\"updateExternalLiquidity(uint112)\":{\"details\":\"This function sets the external liquidity to a new value and emits an event with the new value. It can only be called by the fee setter.\",\"params\":{\"_externalLiquidity\":\"The new external liquidity value.\"}}},\"title\":\"ITokenController Interface\",\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{\"getReserves()\":{\"notice\":\"Fetches the current reserves of asset X and asset Y, as well as the block of the last operation.\"},\"referenceReserves()\":{\"notice\":\"Returns the reference reserves for the block, these represent a snapshot of the   reserves at the start of the block weighted for mints, burns, borrow and repayment of   liquidity. These amounts are critical to calculating the correct fees for any swap.\"},\"tokens(uint256)\":{\"notice\":\"Return the IAmmalgamERC20 token corresponding to the token type\"},\"totalAssets()\":{\"notice\":\"Computes the current total Assets.\"},\"underlyingTokens()\":{\"notice\":\"Get the underlying tokens for the AmmalgamERC20Controller.\"},\"updateExternalLiquidity(uint112)\":{\"notice\":\"Updates the external liquidity value.\"}},\"notice\":\"The interface of a ERC20 facade for multiple token types with functionality similar to ERC1155.\",\"version\":1}},\"settings\":{\"compilationTarget\":{\"contracts/interfaces/tokens/ITokenController.sol\":\"ITokenController\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":1inch/=lib/1inch/\",\":@1inch/=lib/1inch/\",\":@mangrovedao/mangrove-core/=lib/mangrove-core/\",\":@mgv/forge-std/=lib/mangrove-core/lib/forge-std/src/\",\":@mgv/lib/=lib/mangrove-core/lib/\",\":@mgv/script/=lib/mangrove-core/script/\",\":@mgv/src/=lib/mangrove-core/src/\",\":@mgv/test/=lib/mangrove-core/test/\",\":@morpho-org/morpho-blue/=lib/morpho-blue/\",\":@openzeppelin/=lib/openzeppelin-contracts/\",\":ExcessivelySafeCall/=lib/ExcessivelySafeCall/src/\",\":core/=lib/mangrove-core/lib/core/\",\":ds-test/=lib/ExcessivelySafeCall/lib/ds-test/src/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":forge-std/=lib/openzeppelin-contracts/lib/forge-std/src/\",\":halmos-cheatcodes/=lib/morpho-blue/lib/halmos-cheatcodes/src/\",\":mangrove-core/=lib/mangrove-core/\",\":morpho-blue/=lib/morpho-blue/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":preprocessed/=lib/mangrove-core/lib/preprocessed/\"]},\"sources\":{\"contracts/interfaces/tokens/IAmmalgamERC20.sol\":{\"keccak256\":\"0x44a376269170b4270ec221ce3cb31a609b394e216cc4d2e27b818361b4369829\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://c48bc7586631f27ede73d3d0b4c1d7a29b1653e6c501c8b7fc9877c125f8f57e\",\"dweb:/ipfs/QmTSLtqnsxr7h7ct524rqYssHUo4qursmCZ7g5q3J1qQPK\"]},\"contracts/interfaces/tokens/ITokenController.sol\":{\"keccak256\":\"0x7778001aaf582fe10005240eb6023b2b6cee3f100b6c2222bf6b9ade93732624\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://91e5c4519207d6a450be1e0a8649157e86d20f8ef6a91ff6512a31cf5561a570\",\"dweb:/ipfs/QmUqZLW27JJZHFPf2fgLDYSWWj5gM158DdaxTTmDVukRAg\"]},\"lib/openzeppelin-contracts/contracts/interfaces/IERC20.sol\":{\"keccak256\":\"0xb5d81383d40f4006d1ce4bbad0064e7a930e17302cbe2a745e09cb403f042733\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3fc4a5681c2f00f41f49260a36ae6bbe1121dd93d470ea24d51d556eff2980be\",\"dweb:/ipfs/QmUBW6TwVWtGP96ka9TfuGivd27kH8CtkXD8RQAAecSFiR\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol\":{\"keccak256\":\"0xfe37358f223eddd65d61bb62b0b7bdb69d7101b5ec8d484292b8c1583a153b8a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://28dd43f30af3c12ae0fc08dd031b1250e906ef3c95f63f30fac6fd15aee2a662\",\"dweb:/ipfs/QmUkSyWsSRx36w1ti7U6qnGnQgJq16wpMhjeJrnyn9AXwG\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol\":{\"keccak256\":\"0xbaffa0bcc92adf28a53cc3b68551fc3632cb8f849a0028cb8d5c06e4677715e9\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://32e6f8f6b2e883c85e6a602c0882d9962ce2f92406961244e86cd974df815912\",\"dweb:/ipfs/Qmahvx6fPpecicq1aUE1JihCxV5ep1bfuPukzrxa8Ub5PS\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Permit.sol\":{\"keccak256\":\"0x093f32ab700c2b05373387263915a75f5455cdb0f09a7630cc621e27b7b50d04\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://d163e6ef21df143969df5557305e8c643a135c7660a678d0c65dca91772114a0\",\"dweb:/ipfs/QmTZUgiwEro5oLRhbJ2iSWyCqu1JTDekoFHALVUn4eHqYK\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "address", "name": "borrower", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "tokenType", "type": "uint256", "indexed": true}, {"internalType": "uint256", "name": "badDebtAssets", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "badDebtShares", "type": "uint256", "indexed": false}], "type": "event", "name": "BurnBadDebt", "anonymous": false}, {"inputs": [{"internalType": "uint128", "name": "depositLAssets", "type": "uint128", "indexed": false}, {"internalType": "uint128", "name": "depositXAssets", "type": "uint128", "indexed": false}, {"internalType": "uint128", "name": "depositYAssets", "type": "uint128", "indexed": false}, {"internalType": "uint128", "name": "borrowLAssets", "type": "uint128", "indexed": false}, {"internalType": "uint128", "name": "borrowXAssets", "type": "uint128", "indexed": false}, {"internalType": "uint128", "name": "borrowYAssets", "type": "uint128", "indexed": false}], "type": "event", "name": "InterestAccrued", "anonymous": false}, {"inputs": [{"internalType": "uint256", "name": "reserveXAssets", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "reserveYAssets", "type": "uint256", "indexed": false}], "type": "event", "name": "Sync", "anonymous": false}, {"inputs": [{"internalType": "uint112", "name": "externalLiquidity", "type": "uint112", "indexed": false}], "type": "event", "name": "UpdateExternalLiquidity", "anonymous": false}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "externalLiquidity", "outputs": [{"internalType": "uint112", "name": "", "type": "uint112"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "getReserves", "outputs": [{"internalType": "uint112", "name": "reserveXAssets", "type": "uint112"}, {"internalType": "uint112", "name": "reserveYAssets", "type": "uint112"}, {"internalType": "uint32", "name": "lastTimestamp", "type": "uint32"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "referenceReserves", "outputs": [{"internalType": "uint112", "name": "referenceReserveX", "type": "uint112"}, {"internalType": "uint112", "name": "referenceReserveY", "type": "uint112"}]}, {"inputs": [{"internalType": "uint256", "name": "tokenType", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "tokens", "outputs": [{"internalType": "contract IAmmalgamERC20", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "totalAssets", "outputs": [{"internalType": "uint128[6]", "name": "", "type": "uint128[6]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "underlyingTokens", "outputs": [{"internalType": "contract IERC20", "name": "", "type": "address"}, {"internalType": "contract IERC20", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "uint112", "name": "_externalLiquidity", "type": "uint112"}], "stateMutability": "nonpayable", "type": "function", "name": "updateExternalLiquidity"}], "devdoc": {"kind": "dev", "methods": {"getReserves()": {"returns": {"lastTimestamp": "The timestamp of the last operation.", "reserveXAssets": "The current reserve of asset X.", "reserveYAssets": "The current reserve of asset Y."}}, "referenceReserves()": {"returns": {"referenceReserveX": "The reference reserve for asset X.", "referenceReserveY": "The reference reserve for asset Y."}}, "tokens(uint256)": {"params": {"tokenType": "The type of token for which the scaler is being computed.                  Can be one of BORROW_X, DEPOSIT_X, BORROW_Y, DEPOSIT_Y, BORROW_L, or DEPOSIT_L."}, "returns": {"_0": "The IAmmalgamERC20 token"}}, "totalAssets()": {"details": "If the last lending state update is outdated (i.e., not matching the current block timestamp),      the function recalculates the assets based on the duration since the last update, the lending state,      and reserve balances. If the timestamp is current, the previous scaler (without recalculation) is returned.", "returns": {"_0": "totalAssets An array of six `uint128` values representing the total assets for each of the 6 amalgam token types.  These values may be adjusted based on the time elapsed since the last update. If the timestamp is up-to-date, the  previously calculated total assets are returned without recalculation."}}, "underlyingTokens()": {"returns": {"_0": "The addresses of the underlying tokens."}}, "updateExternalLiquidity(uint112)": {"details": "This function sets the external liquidity to a new value and emits an event with the new value. It can only be called by the fee setter.", "params": {"_externalLiquidity": "The new external liquidity value."}}}, "version": 1}, "userdoc": {"kind": "user", "methods": {"getReserves()": {"notice": "Fetches the current reserves of asset X and asset Y, as well as the block of the last operation."}, "referenceReserves()": {"notice": "Returns the reference reserves for the block, these represent a snapshot of the   reserves at the start of the block weighted for mints, burns, borrow and repayment of   liquidity. These amounts are critical to calculating the correct fees for any swap."}, "tokens(uint256)": {"notice": "Return the IAmmalgamERC20 token corresponding to the token type"}, "totalAssets()": {"notice": "Computes the current total Assets."}, "underlyingTokens()": {"notice": "Get the underlying tokens for the AmmalgamERC20Controller."}, "updateExternalLiquidity(uint112)": {"notice": "Updates the external liquidity value."}}, "version": 1}}, "settings": {"remappings": ["1inch/=lib/1inch/", "@1inch/=lib/1inch/", "@mangrovedao/mangrove-core/=lib/mangrove-core/", "@mgv/forge-std/=lib/mangrove-core/lib/forge-std/src/", "@mgv/lib/=lib/mangrove-core/lib/", "@mgv/script/=lib/mangrove-core/script/", "@mgv/src/=lib/mangrove-core/src/", "@mgv/test/=lib/mangrove-core/test/", "@morpho-org/morpho-blue/=lib/morpho-blue/", "@openzeppelin/=lib/openzeppelin-contracts/", "ExcessivelySafeCall/=lib/ExcessivelySafeCall/src/", "core/=lib/mangrove-core/lib/core/", "ds-test/=lib/ExcessivelySafeCall/lib/ds-test/src/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "forge-std/=lib/openzeppelin-contracts/lib/forge-std/src/", "halmos-cheatcodes/=lib/morpho-blue/lib/halmos-cheatcodes/src/", "mangrove-core/=lib/mangrove-core/", "morpho-blue/=lib/morpho-blue/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "preprocessed/=lib/mangrove-core/lib/preprocessed/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"contracts/interfaces/tokens/ITokenController.sol": "ITokenController"}, "evmVersion": "cancun", "libraries": {}}, "sources": {"contracts/interfaces/tokens/IAmmalgamERC20.sol": {"keccak256": "0x44a376269170b4270ec221ce3cb31a609b394e216cc4d2e27b818361b4369829", "urls": ["bzz-raw://c48bc7586631f27ede73d3d0b4c1d7a29b1653e6c501c8b7fc9877c125f8f57e", "dweb:/ipfs/QmTSLtqnsxr7h7ct524rqYssHUo4qursmCZ7g5q3J1qQPK"], "license": "GPL-3.0-only"}, "contracts/interfaces/tokens/ITokenController.sol": {"keccak256": "0x7778001aaf582fe10005240eb6023b2b6cee3f100b6c2222bf6b9ade93732624", "urls": ["bzz-raw://91e5c4519207d6a450be1e0a8649157e86d20f8ef6a91ff6512a31cf5561a570", "dweb:/ipfs/QmUqZLW27JJZHFPf2fgLDYSWWj5gM158DdaxTTmDVukRAg"], "license": "GPL-3.0-only"}, "lib/openzeppelin-contracts/contracts/interfaces/IERC20.sol": {"keccak256": "0xb5d81383d40f4006d1ce4bbad0064e7a930e17302cbe2a745e09cb403f042733", "urls": ["bzz-raw://3fc4a5681c2f00f41f49260a36ae6bbe1121dd93d470ea24d51d556eff2980be", "dweb:/ipfs/QmUBW6TwVWtGP96ka9TfuGivd27kH8CtkXD8RQAAecSFiR"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol": {"keccak256": "0xfe37358f223eddd65d61bb62b0b7bdb69d7101b5ec8d484292b8c1583a153b8a", "urls": ["bzz-raw://28dd43f30af3c12ae0fc08dd031b1250e906ef3c95f63f30fac6fd15aee2a662", "dweb:/ipfs/QmUkSyWsSRx36w1ti7U6qnGnQgJq16wpMhjeJrnyn9AXwG"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol": {"keccak256": "0xbaffa0bcc92adf28a53cc3b68551fc3632cb8f849a0028cb8d5c06e4677715e9", "urls": ["bzz-raw://32e6f8f6b2e883c85e6a602c0882d9962ce2f92406961244e86cd974df815912", "dweb:/ipfs/Qmahvx6fPpecicq1aUE1JihCxV5ep1bfuPukzrxa8Ub5PS"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Permit.sol": {"keccak256": "0x093f32ab700c2b05373387263915a75f5455cdb0f09a7630cc621e27b7b50d04", "urls": ["bzz-raw://d163e6ef21df143969df5557305e8c643a135c7660a678d0c65dca91772114a0", "dweb:/ipfs/QmTZUgiwEro5oLRhbJ2iSWyCqu1JTDekoFHALVUn4eHqYK"], "license": "MIT"}}, "version": 1}, "id": 19}