{"abi": [{"type": "constructor", "inputs": [{"name": "_midTermIntervalConfig", "type": "uint24", "internalType": "uint24"}, {"name": "_longTermIntervalConfig", "type": "uint24", "internalType": "uint24"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "accruePenalties", "inputs": [{"name": "account", "type": "address", "internalType": "address"}, {"name": "externalLiquidity", "type": "uint256", "internalType": "uint256"}, {"name": "duration", "type": "uint256", "internalType": "uint256"}, {"name": "allAssetsDepositL", "type": "uint256", "internalType": "uint256"}, {"name": "allAssetsBorrowL", "type": "uint256", "internalType": "uint256"}, {"name": "allSharesBorrowL", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "penaltyInBorrowLShares", "type": "uint112", "internalType": "uint112"}, {"name": "accountPenaltyInBorrowLShares", "type": "uint112", "internalType": "uint112"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "boundTick", "inputs": [{"name": "newTick", "type": "int16", "internalType": "int16"}], "outputs": [{"name": "", "type": "int16", "internalType": "int16"}], "stateMutability": "view"}, {"type": "function", "name": "calcSatChangeRatioBips", "inputs": [{"name": "inputParams", "type": "tuple", "internalType": "struct Validation.InputParams", "components": [{"name": "userAssets", "type": "uint256[6]", "internalType": "uint256[6]"}, {"name": "sqrtPriceMinInQ72", "type": "uint256", "internalType": "uint256"}, {"name": "sqrtPriceMaxInQ72", "type": "uint256", "internalType": "uint256"}, {"name": "activeLiquidityScalerInQ72", "type": "uint256", "internalType": "uint256"}, {"name": "activeLiquidityAssets", "type": "uint256", "internalType": "uint256"}, {"name": "reservesXAssets", "type": "uint256", "internalType": "uint256"}, {"name": "reservesYAssets", "type": "uint256", "internalType": "uint256"}]}, {"name": "liqSqrtPriceInXInQ72", "type": "uint256", "internalType": "uint256"}, {"name": "liqSqrtPriceInYInQ72", "type": "uint256", "internalType": "uint256"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address", "internalType": "address"}, {"name": "account", "type": "address", "internalType": "address"}], "outputs": [{"name": "ratioNetXBips", "type": "uint256", "internalType": "uint256"}, {"name": "ratioNetYBips", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "configLongTermInterval", "inputs": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address", "internalType": "address"}, {"name": "_longTermIntervalConfig", "type": "uint24", "internalType": "uint24"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "getAccount", "inputs": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address", "internalType": "address"}, {"name": "netDebtX", "type": "bool", "internalType": "bool"}, {"name": "accountAddress", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "tuple", "internalType": "struct Saturation.Account", "components": [{"name": "exists", "type": "bool", "internalType": "bool"}, {"name": "lastTranche", "type": "int16", "internalType": "int16"}, {"name": "penaltyInBorrowLShares", "type": "uint112", "internalType": "uint112"}, {"name": "satPairPerTranche", "type": "tuple[]", "internalType": "struct Saturation.SaturationPair[]", "components": [{"name": "satInLAssets", "type": "uint128", "internalType": "uint128"}, {"name": "satRelativeToL", "type": "uint128", "internalType": "uint128"}]}, {"name": "treePenaltyAtOnsetInBorrowLSharesPerSatInQ72PerTranche", "type": "uint256[]", "internalType": "uint256[]"}]}], "stateMutability": "view"}, {"type": "function", "name": "getLeafDetails", "inputs": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address", "internalType": "address"}, {"name": "netDebtX", "type": "bool", "internalType": "bool"}, {"name": "leafIndex", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "saturation", "type": "tuple", "internalType": "struct Saturation.SaturationPair", "components": [{"name": "satInLAssets", "type": "uint128", "internalType": "uint128"}, {"name": "satRelativeToL", "type": "uint128", "internalType": "uint128"}]}, {"name": "currentPenaltyInBorrowLSharesPerSatInQ72", "type": "uint256", "internalType": "uint256"}, {"name": "tranches", "type": "uint16[]", "internalType": "uint16[]"}], "stateMutability": "view"}, {"type": "function", "name": "getLendingStateTick", "inputs": [{"name": "newTick", "type": "int56", "internalType": "int56"}, {"name": "timeElapsedSinceUpdate", "type": "uint32", "internalType": "uint32"}, {"name": "timeElapsedSinceLendingUpdate", "type": "uint32", "internalType": "uint32"}], "outputs": [{"name": "lendingStateTick", "type": "int16", "internalType": "int16"}, {"name": "maxSatInWads", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getLendingStateTickAndCheckpoint", "inputs": [{"name": "timeElapsedSinceUpdate", "type": "uint32", "internalType": "uint32"}, {"name": "timeElapsedSinceLendingUpdate", "type": "uint32", "internalType": "uint32"}], "outputs": [{"name": "lendingStateTick", "type": "int16", "internalType": "int16"}, {"name": "maxSatInWads", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "getObservations", "inputs": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "tuple", "internalType": "struct GeometricTWAP.Observations", "components": [{"name": "isMidTermBufferInitialized", "type": "bool", "internalType": "bool"}, {"name": "isLongTermBufferInitialized", "type": "bool", "internalType": "bool"}, {"name": "midTermIndex", "type": "uint8", "internalType": "uint8"}, {"name": "longTermIndex", "type": "uint8", "internalType": "uint8"}, {"name": "lastTick", "type": "int16", "internalType": "int16"}, {"name": "lastLendingStateTick", "type": "int16", "internalType": "int16"}, {"name": "midTermIntervalConfig", "type": "uint24", "internalType": "uint24"}, {"name": "longTermIntervalConfig", "type": "uint24", "internalType": "uint24"}, {"name": "lendingCumulativeSum", "type": "int56", "internalType": "int56"}, {"name": "midTermCumulativeSum", "type": "int56[51]", "internalType": "int56[51]"}, {"name": "longTermCumulativeSum", "type": "int56[9]", "internalType": "int56[9]"}, {"name": "midTermTimeInterval", "type": "uint32[51]", "internalType": "uint32[51]"}, {"name": "longTermTimeInterval", "type": "uint32[9]", "internalType": "uint32[9]"}]}], "stateMutability": "view"}, {"type": "function", "name": "getObservedMidTermTick", "inputs": [{"name": "isLongTermBufferInitialized", "type": "bool", "internalType": "bool"}], "outputs": [{"name": "", "type": "int16", "internalType": "int16"}], "stateMutability": "view"}, {"type": "function", "name": "getTickRange", "inputs": [{"name": "pair", "type": "address", "internalType": "address"}, {"name": "currentTick", "type": "int16", "internalType": "int16"}, {"name": "includeLongTermTick", "type": "bool", "internalType": "bool"}], "outputs": [{"name": "", "type": "int16", "internalType": "int16"}, {"name": "", "type": "int16", "internalType": "int16"}], "stateMutability": "view"}, {"type": "function", "name": "getTrancheDetails", "inputs": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address", "internalType": "address"}, {"name": "netDebtX", "type": "bool", "internalType": "bool"}, {"name": "tranche", "type": "int16", "internalType": "int16"}], "outputs": [{"name": "leaf", "type": "uint16", "internalType": "uint16"}, {"name": "saturation", "type": "tuple", "internalType": "struct Saturation.SaturationPair", "components": [{"name": "satInLAssets", "type": "uint128", "internalType": "uint128"}, {"name": "satRelativeToL", "type": "uint128", "internalType": "uint128"}]}], "stateMutability": "view"}, {"type": "function", "name": "getTreeDetails", "inputs": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address", "internalType": "address"}, {"name": "netDebtX", "type": "bool", "internalType": "bool"}], "outputs": [{"name": "", "type": "uint16", "internalType": "uint16"}, {"name": "", "type": "uint128", "internalType": "uint128"}], "stateMutability": "view"}, {"type": "function", "name": "init", "inputs": [{"name": "firstTick", "type": "int16", "internalType": "int16"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "liquidationCheckHardPremiums", "inputs": [{"name": "inputParams", "type": "tuple", "internalType": "struct Validation.InputParams", "components": [{"name": "userAssets", "type": "uint256[6]", "internalType": "uint256[6]"}, {"name": "sqrtPriceMinInQ72", "type": "uint256", "internalType": "uint256"}, {"name": "sqrtPriceMaxInQ72", "type": "uint256", "internalType": "uint256"}, {"name": "activeLiquidityScalerInQ72", "type": "uint256", "internalType": "uint256"}, {"name": "activeLiquidityAssets", "type": "uint256", "internalType": "uint256"}, {"name": "reservesXAssets", "type": "uint256", "internalType": "uint256"}, {"name": "reservesYAssets", "type": "uint256", "internalType": "uint256"}]}, {"name": "borrower", "type": "address", "internalType": "address"}, {"name": "hardLiquidationParams", "type": "tuple", "internalType": "struct Liquidation.HardLiquidationParams", "components": [{"name": "depositLToBeTransferredInLAssets", "type": "uint256", "internalType": "uint256"}, {"name": "depositXToBeTransferredInXAssets", "type": "uint256", "internalType": "uint256"}, {"name": "depositYToBeTransferredInYAssets", "type": "uint256", "internalType": "uint256"}, {"name": "repayLXInXAssets", "type": "uint256", "internalType": "uint256"}, {"name": "repayLYInYAssets", "type": "uint256", "internalType": "uint256"}, {"name": "repayXInXAssets", "type": "uint256", "internalType": "uint256"}, {"name": "repayYInYAssets", "type": "uint256", "internalType": "uint256"}]}, {"name": "actualRepaidLiquidityAssets", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "badDebt", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "longTermIntervalConfig", "inputs": [], "outputs": [{"name": "", "type": "uint24", "internalType": "uint24"}], "stateMutability": "view"}, {"type": "function", "name": "midTermIntervalConfig", "inputs": [], "outputs": [{"name": "", "type": "uint24", "internalType": "uint24"}], "stateMutability": "view"}, {"type": "function", "name": "owner", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "recordObservation", "inputs": [{"name": "newTick", "type": "int16", "internalType": "int16"}, {"name": "timeElapsed", "type": "uint32", "internalType": "uint32"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "renounceOwnership", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setNewPositionSaturation", "inputs": [{"name": "pair", "type": "address", "internalType": "address"}, {"name": "maxDesiredSaturationMag2", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "transferOwnership", "inputs": [{"name": "new<PERSON>wner", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "update", "inputs": [{"name": "inputParams", "type": "tuple", "internalType": "struct Validation.InputParams", "components": [{"name": "userAssets", "type": "uint256[6]", "internalType": "uint256[6]"}, {"name": "sqrtPriceMinInQ72", "type": "uint256", "internalType": "uint256"}, {"name": "sqrtPriceMaxInQ72", "type": "uint256", "internalType": "uint256"}, {"name": "activeLiquidityScalerInQ72", "type": "uint256", "internalType": "uint256"}, {"name": "activeLiquidityAssets", "type": "uint256", "internalType": "uint256"}, {"name": "reservesXAssets", "type": "uint256", "internalType": "uint256"}, {"name": "reservesYAssets", "type": "uint256", "internalType": "uint256"}]}, {"name": "account", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "event", "name": "OwnershipTransferred", "inputs": [{"name": "previousOwner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "new<PERSON>wner", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "UpdateLendingTick", "inputs": [{"name": "lendingStateTick", "type": "int16", "indexed": false, "internalType": "int16"}], "anonymous": false}, {"type": "error", "name": "AmmalgamMaxSlippage", "inputs": []}, {"type": "error", "name": "CannotUpdateZeroAddress", "inputs": []}, {"type": "error", "name": "InvalidIntervalConfig", "inputs": []}, {"type": "error", "name": "InvalidUserConfiguration", "inputs": []}, {"type": "error", "name": "LiquidationPremiumTooHigh", "inputs": []}, {"type": "error", "name": "MaxTrancheOverSaturated", "inputs": []}, {"type": "error", "name": "OwnableInvalidOwner", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "OwnableUnauthorizedAccount", "inputs": [{"name": "account", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "PairAlreadyExists", "inputs": []}, {"type": "error", "name": "PairDoesNotExist", "inputs": []}, {"type": "error", "name": "PriceOutOfBounds", "inputs": []}, {"type": "error", "name": "SafeCastOverflowedUintDowncast", "inputs": [{"name": "bits", "type": "uint8", "internalType": "uint8"}, {"name": "value", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "TickOutOfBounds", "inputs": []}], "bytecode": {"object": "0x60c060405234801561000f575f5ffd5b50604051615e56380380615e5683398101604081905261002e916100d6565b338061005357604051631e4fbdf760e01b81525f600482015260240160405180910390fd5b61005c81610070565b5062ffffff9182166080521660a052610107565b5f80546001600160a01b038381166001600160a01b0319831681178455604051919092169283917f8be0079c531659141344cd1fd0a4f28419497f9722a3daafe3b4186f6b6457e09190a35050565b805162ffffff811681146100d1575f5ffd5b919050565b5f5f604083850312156100e7575f5ffd5b6100f0836100bf565b91506100fe602084016100bf565b90509250929050565b60805160a051615d206101365f395f81816102d7015261063b01525f818161032d015261061a0152615d205ff3fe608060405234801561000f575f5ffd5b5060043610610147575f3560e01c8063668243a7116100bf5780638ecc5eda116100795780638ecc5eda14610369578063b001680914610389578063beca5a67146103be578063df31e2cc146103d1578063ecabd7aa146103e4578063f2fde38b14610411575f5ffd5b8063668243a7146102b15780636c8c3191146102d25780636ef9c9681461030d578063715018a61461032057806388205b40146103285780638da5cb5b1461034f575f5ffd5b8063159e7cd711610110578063159e7cd7146101ed57806315f0cc2f146102105780631b060ac7146102235780633d011d65146102365780634d003c0e1461025c5780635cb5fb0a1461027e575f5ffd5b8062ba55ec1461014b57806301cbfeb21461017d578063********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", "sourceMap": "505:15989:1:-:0;;;1101:216;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;1184:10;;1269:95:53;;1322:31;;-1:-1:-1;;;1322:31:53;;1350:1;1322:31;;;624:51:190;597:18;;1322:31:53;;;;;;;1269:95;1373:32;1392:12;1373:18;:32::i;:::-;-1:-1:-1;1206:46:1::1;::::0;;::::1;;::::0;1262:48:::1;;::::0;505:15989;;2912:187:53;2985:16;3004:6;;-1:-1:-1;;;;;3020:17:53;;;-1:-1:-1;;;;;;3020:17:53;;;;;;3052:40;;3004:6;;;;;;;3052:40;;2985:16;3052:40;2975:124;2912:187;:::o;14:165:190:-;92:13;;145:8;134:20;;124:31;;114:59;;169:1;166;159:12;114:59;14:165;;;:::o;184:289::-;261:6;269;322:2;310:9;301:7;297:23;293:32;290:52;;;338:1;335;328:12;290:52;361:39;390:9;361:39;:::i;:::-;351:49;;419:48;463:2;452:9;448:18;419:48;:::i;:::-;409:58;;184:289;;;;;:::o;478:203::-;505:15989:1;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "0x608060405234801561000f575f5ffd5b5060043610610147575f3560e01c8063668243a7116100bf5780638ecc5eda116100795780638ecc5eda14610369578063b001680914610389578063beca5a67146103be578063df31e2cc146103d1578063ecabd7aa146103e4578063f2fde38b14610411575f5ffd5b8063668243a7146102b15780636c8c3191146102d25780636ef9c9681461030d578063715018a61461032057806388205b40146103285780638da5cb5b1461034f575f5ffd5b8063159e7cd711610110578063159e7cd7146101ed57806315f0cc2f146102105780631b060ac7146102235780633d011d65146102365780634d003c0e1461025c5780635cb5fb0a1461027e575f5ffd5b8062ba55ec1461014b57806301cbfeb21461017d578063********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", "sourceMap": "505:15989:1:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;14408:562;;;;;;:::i;:::-;;:::i;:::-;;;;899:1:190;888:21;;;;870:40;;941:2;926:18;;919:34;;;;843:18;14408:562:1;;;;;;;;4615:242;;;;;;:::i;:::-;;:::i;:::-;;;;;;;:::i;1664:623::-;;;;;;:::i;:::-;;:::i;:::-;;8889:214;;;;;;:::i;:::-;;:::i;7303:734::-;;;;;;:::i;:::-;;:::i;:::-;;;;7085:25:190;;;7141:2;7126:18;;7119:34;;;;7058:18;7303:734:1;6911:248:190;14996:1496:1;;;;;;:::i;:::-;;:::i;:::-;;;8789:14:190;;8782:22;8764:41;;8752:2;8737:18;14996:1496:1;8624:187:190;12345:489:1;;;;;;:::i;:::-;;:::i;2312:419::-;;;;;;:::i;:::-;;:::i;13104:230::-;;;;;;:::i;:::-;;:::i;:::-;;;9738:1:190;9727:21;;;;9709:40;;9697:2;9682:18;13104:230:1;9567:188:190;3396:594:1;;;;;;:::i;:::-;;:::i;:::-;;;;;;;;;:::i;6074:656::-;;;;;;:::i;:::-;;:::i;:::-;;;;-1:-1:-1;;;;;11981:43:190;;;11963:62;;12061:43;;;;12056:2;12041:18;;12034:71;11936:18;6074:656:1;11789:322:190;4242:367:1;;;;;;:::i;:::-;;:::i;:::-;;;;;;;;:::i;667:46::-;;;;;;;;13070:8:190;13058:21;;;13040:40;;13028:2;13013:18;667:46:1;12896:190:190;10048:208:1;;;;;;:::i;:::-;;:::i;2293:101:53:-;;;:::i;616:45:1:-;;;;;1638:85:53;1684:7;1710:6;1638:85;;-1:-1:-1;;;;;1710:6:53;;;13496:51:190;;13484:2;13469:18;1638:85:53;13350:203:190;8068:172:1;;;;;;:::i;:::-;;:::i;:::-;;;;;;;:::i;3996:240::-;;;;;;:::i;:::-;;:::i;:::-;;;;17443:6:190;17431:19;;;17413:38;;-1:-1:-1;;;;;17487:47:190;;;17482:2;17467:18;;17460:75;17386:18;3996:240:1;17241:300:190;13667:165:1;;;;;;:::i;:::-;;:::i;5267:139::-;;;;;;:::i;:::-;;:::i;10982:233::-;;;;;;:::i;:::-;;:::i;:::-;;;;18387:1:190;18376:21;;;18358:40;;18434:21;;;;18429:2;18414:18;;18407:49;18331:18;10982:233:1;18192:270:190;2543:215:53;;;;;;:::i;:::-;;:::i;14408:562:1:-;14784:10;14574:22;14766:29;;;:17;:29;;;;;14574:22;;14719:156;;14797:7;14806:22;14830:29;14861:4;14719:33;:156::i;:::-;-1:-1:-1;14951:10:1;14934:28;;;;:16;:28;;;;;14697:178;;-1:-1:-1;14900:63:1;;:33;:63::i;:::-;14885:78;;14408:562;;;;;;:::o;4615:242::-;-1:-1:-1;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4792:30:1;4800:11;4813:8;4792:7;:30::i;:::-;-1:-1:-1;;;;;4792:58:1;;;;;;:42;;;;;:58;;;;;;;;4785:65;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;4785:65:1;;;;;;;;;;;;;;;;;;;;;;;;;;4792:58;;4785:65;;;;;;;;4792:58;4785:65;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;4785:65:1;;;;;-1:-1:-1;;;4785:65:1;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4615:242;;;;;;:::o;1664:623::-;1750:10;1732:29;;;;:17;:29;;;;;;;;1728:61;;;1770:19;;-1:-1:-1;;;1770:19:1;;;;;;;;;;;1728:61;1864:10;1799:45;1847:28;;;:16;:28;;;;;;;;1935:17;:29;;;;;;1975:48;1847:28;1975:37;:48::i;:::-;2033:102;2075:12;2089:21;2112:22;2033:41;:102::i;:::-;2163:10;2145:29;;;;:17;:29;;;;;;;;:36;;-1:-1:-1;;2145:36:1;2177:4;2145:36;;;2239:17;:29;;;;;2192:88;;2270:9;2192:46;:88::i;:::-;1718:569;;1664:623;:::o;8889:214::-;1531:13:53;:11;:13::i;:::-;-1:-1:-1;;;;;9040:30:1;::::1;;::::0;;;:17:::1;:30;::::0;;;;9003:93:::1;::::0;9072:23;9003:36:::1;:93::i;:::-;8889:214:::0;;:::o;7303:734::-;7567:21;7590;1358:16;:14;:16::i;:::-;-1:-1:-1;;;;;7671:29:1;::::1;7623:45;7671:29:::0;;;:16:::1;:29;::::0;;;;;7744:46:::1;7688:11:::0;7782:7;7744:24:::1;:46::i;:::-;7710:80;;7873:157;7920:9;7931:11;7944:20;7966;7988:7;7997:23;7873:33;:157::i;:::-;7866:164;;;;;;7303:734:::0;;;;;;;;:::o;14996:1496::-;15407:29;;;;;15478;;;;;15446:61;;;15517:60;;;;15257:12;;;;;15674:123;15407:11;15746:21;15769:27;15674:58;:123::i;:::-;15932:10;15809:24;15915:28;;;:16;:28;;;;;15588:209;;-1:-1:-1;15588:209:1;;-1:-1:-1;15588:209:1;-1:-1:-1;15809:24:1;;;15859:239;;15957:11;15982:8;15588:209;;;15859:42;:239::i;:::-;15808:290;;;;16109:23;16147:99;16177:22;16201:26;16229:16;16147:29;:99::i;:::-;16109:137;;16267:18;:37;;;;;16289:15;16267:37;16386:29;;;;;16354;;;;:61;;;;16425:60;;;;-1:-1:-1;16257:47:1;;14996:1496;-1:-1:-1;;;;;;;;;14996:1496:1:o;12345:489::-;12510:22;12534:20;1358:16;:14;:16::i;:::-;12663:10:::1;12645:29;::::0;;;:17:::1;:29;::::0;;;;12585:154:::1;::::0;12676:22;12700:29;12585:46:::1;:154::i;:::-;12815:10;12798:28;::::0;;;:16:::1;:28;::::0;;;;12566:173;;-1:-1:-1;12764:63:1::1;::::0;:33:::1;:63::i;:::-;12749:78;;12345:489:::0;;;;;:::o;2312:419::-;-1:-1:-1;;;;;2418:23:1;;;;;;:17;:23;;;;;;;;2413:55;;2450:18;;-1:-1:-1;;;2450:18:1;;;;;;;;;;;2413:55;6863:2:25;2482:24:1;:65;:98;;;-1:-1:-1;2551:29:1;;2482:98;2478:162;;;2603:26;;-1:-1:-1;;;2603:26:1;;;;;;;;;;;2478:162;-1:-1:-1;;;;;2649:36:1;;;;;;;:30;:36;;;;;;;;2686:10;2649:48;;;;;;;:75;2312:419::o;13104:230::-;13286:10;13207:5;13268:29;;;:17;:29;;;;;13231:96;;13299:27;13231:36;:96::i;:::-;13224:103;13104:230;-1:-1:-1;;13104:230:1:o;3396:594::-;-1:-1:-1;;;;;;;;;;;;;;;;;3622:48:1;3684:24;3733:28;3764:30;3772:11;3785:8;3764:7;:30::i;:::-;:36;;3801:9;3764:47;;;;;;;:::i;:::-;3821:29;;;;;;;;3764:47;;;;;;;;;3834:16;;;3821:29;-1:-1:-1;;;;;3821:29:1;;;;;-1:-1:-1;;;3821:29:1;;;;;;;;;;;;3903:38;;;;3821:29;3962:21;;3951:32;;;;;;;;;;;;;;;;;3821:29;;-1:-1:-1;3903:38:1;;-1:-1:-1;3764:47:1;;-1:-1:-1;3962:21:1;;3951:32;;;3962:21;3951:32;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3723:267;3396:594;;;;;;;:::o;6074:656::-;6326:30;6358:37;1358:16;:14;:16::i;:::-;6537:10:::1;6520:28;::::0;;;:16:::1;:28;::::0;;;;6480:243:::1;::::0;6562:7;6583:17;6614:8;6636:17;6667:16;6697;6480:26:::1;:243::i;:::-;6473:250;;;;1384:1;6074:656:::0;;;;;;;;;:::o;4242:367::-;4373:11;4386:43;-1:-1:-1;;;;;;;;;;;;;;;;;;;4386:43:1;4441:28;4472:30;4480:11;4493:8;4472:7;:30::i;:::-;4519:27;;;;;;;;;:18;;;:27;;;;;;;;;4569:24;;;;:33;;;;;;4556:46;;;;;;;;;-1:-1:-1;;;;;4556:46:1;;;;;-1:-1:-1;;;4556:46:1;;;;;;;;4519:27;;;;;4556:46;;-1:-1:-1;4242:367:1;;-1:-1:-1;;;;4242:367:1:o;10048:208::-;10142:4;1358:16;:14;:16::i;:::-;10215:10:::1;10197:29;::::0;;;:17:::1;:29;::::0;;;;10165:84:::1;::::0;10228:7;10237:11;10165:31:::1;:84::i;2293:101:53:-:0;1531:13;:11;:13::i;:::-;2357:30:::1;2384:1;2357:18;:30::i;:::-;2293:101::o:0;8068:172:1:-;8151:33;;:::i;:::-;-1:-1:-1;;;;;8203:30:1;;;;;;:17;:30;;;;;;;;8196:37;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;8196:37:1;;;;;;;;;;-1:-1:-1;;;8196:37:1;;;;;;;;;-1:-1:-1;;;8196:37:1;;;;;;;;-1:-1:-1;;;8196:37:1;;;;;;;-1:-1:-1;;;8196:37:1;;;;;;;;;;;;;;;;;;;;;;;8203:30;;8196:37;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;8196:37:1;;;-1:-1:-1;;8196:37:1;;;;;;;;;;;;;;;;-1:-1:-1;8196:37:1;;;;;;;-1:-1:-1;8196:37:1;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;8196:37:1;;;-1:-1:-1;;8196:37:1;;;;;;;;;;;;;;;;-1:-1:-1;8196:37:1;;;;;;;-1:-1:-1;8196:37:1;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;8196:37:1;;;-1:-1:-1;;8196:37:1;;;;;;;;;;;;;;;;-1:-1:-1;8196:37:1;;;;;;;-1:-1:-1;8196:37:1;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8068:172;;;:::o;3996:240::-;4079:6;4087:7;4106:28;4137:30;4145:11;4158:8;4137:7;:30::i;:::-;4185:19;;;;;;;4206:22;;;;-1:-1:-1;;;;;4206:22:1;;-1:-1:-1;3996:240:1;-1:-1:-1;;;;3996:240:1:o;13667:165::-;13804:10;13738:5;13786:29;;;:17;:29;;;;;13762:63;;13817:7;13762:23;:63::i;5267:139::-;5370:29;5378:11;5391:7;5370;:29::i;10982:233::-;11124:5;11131;11155:53;11169:4;11175:11;11188:19;11155:13;:53::i;:::-;11148:60;;;;10982:233;;;;;;;:::o;2543:215:53:-;1531:13;:11;:13::i;:::-;-1:-1:-1;;;;;2627:22:53;::::1;2623:91;;2672:31;::::0;-1:-1:-1;;;2672:31:53;;2700:1:::1;2672:31;::::0;::::1;13496:51:190::0;13469:18;;2672:31:53::1;;;;;;;;2623:91;2723:28;2742:8;2723:18;:28::i;:::-;2543:215:::0;:::o;18563:909:21:-;18873:17;;18792:5;;;;;;18845:46;;18873:17;;;;;;;18845:21;:46::i;:::-;18816:75;;18906:13;:69;;;;-1:-1:-1;18949:26:21;;-1:-1:-1;;;18949:26:21;;;;18923:52;;;;;18906:69;18902:284;;;19130:30;;;19107:54;;19083:78;18902:284;19218:34;;;;19217:204;;19364:25;;19316:105;;19342:20;;-1:-1:-1;;;19364:25:21;;;;19391:29;19316:25;:105::i;:::-;19217:204;;;19272:25;;-1:-1:-1;;;19272:25:21;;;;19217:204;19196:269;19435:20;;-1:-1:-1;18563:909:21;-1:-1:-1;;;;;;18563:909:21:o;82009:993:25:-;82191:33;;82226:18;;;:33;82114:28;;;;82182:78;;82191:33;;;;;;;;;82226;;;;;82182:8;:78::i;:::-;82275:17;;;;82154:107;;-1:-1:-1;82275:17:25;;:22;:42;;;;-1:-1:-1;82301:16:25;;;;82275:42;82271:81;;;-1:-1:-1;82340:1:25;;82009:993;-1:-1:-1;;82009:993:25:o;82271:81::-;82376:17;;;;82361:12;;82376:31;;82396:11;;82376:17;;:31;:::i;:::-;82361:46;;82429:1;82421:5;:9;;;82417:48;;;-1:-1:-1;82453:1:25;;82009:993;-1:-1:-1;;;82009:993:25:o;82417:48::-;82505:5;82528:1;82523:61;;;;82602:1;82597:61;;;;82676:1;82671:61;;;;82750:1;82745:61;;;;82824:1;82819:61;;;;82927:32;82903:56;;82498:463;;82523:61;82556:26;82532:50;;82523:61;;82597;82630:26;82606:50;;82597:61;;82671;82704:26;82680:50;;82671:61;;82745;82778:26;82754:50;;82745:61;;82819;82852:26;82828:50;;82498:463;;82484:512;;82009:993;;;:::o;3129:261:1:-;-1:-1:-1;;;;;3287:29:1;;3204:23;3287:29;;;:16;:29;;;;;3333:8;:50;;3365:9;:18;;3333:50;;;3344:9;3333:50;3326:57;3129:261;-1:-1:-1;;;;3129:261:1:o;14206:354:25:-;14344:28;14353:9;14344:8;:28::i;:::-;14415;14424:9;:18;;14415:8;:28::i;:::-;14523:30;;-1:-1:-1;;14523:30:25;14549:4;14523:30;;;14206:354::o;4643:571:21:-;4815:54;;-1:-1:-1;;4815:54:21;-1:-1:-1;;;4815:54:21;;;4897:25;;;;;:125;;-1:-1:-1;4966:56:21;;;;1418:2;4966:56;:::i;:::-;4942:21;:80;;;4897:125;4880:208;;;5054:23;;-1:-1:-1;;;5054:23:21;;;;;;;;;;;4880:208;5097:49;;-1:-1:-1;;5156:51:21;-1:-1:-1;;;5097:49:21;;;;;-1:-1:-1;;;;5156:51:21;;-1:-1:-1;;;5156:51:21;;;;;;;;4643:571::o;5220:277::-;5398:47;5417:4;5423:9;5434:1;5437;5440:4;5398:18;:47::i;:::-;5455:35;5471:4;5477:9;5488:1;5455:15;:35::i;1796:162:53:-;1684:7;1710:6;-1:-1:-1;;;;;1710:6:53;735:10:77;1855:23:53;1851:101;;1901:40;;-1:-1:-1;;;1901:40:53;;735:10:77;1901:40:53;;;13496:51:190;13469:18;;1901:40:53;13350:203:190;5864:493:21;6004:26;;-1:-1:-1;;;6004:26:21;;;;6081:57;6004:26;1418:2;6081:57;:::i;:::-;6057:21;:81;;;:127;;;-1:-1:-1;6158:26:21;;6057:127;6040:250;;;6256:23;;-1:-1:-1;;;6256:23:21;;;;;;;;;;;6040:250;-1:-1:-1;6299:51:21;;;;;;-1:-1:-1;;;6299:51:21;-1:-1:-1;;;;6299:51:21;;;;;;5864:493::o;1398:118:1:-;1471:10;1453:29;;;;:17;:29;;;;;;;;1448:61;;1491:18;;-1:-1:-1;;;1491:18:1;;;;;;;;;;;2737:386;-1:-1:-1;;;;;2922:36:1;;;2847:34;2922:36;;;:30;:36;;;;;;;;:45;;;;;;;;;;;;;2981:31;;;2977:140;;-1:-1:-1;6799:2:25;2737:386:1;;;;:::o;77143:3530:25:-;77439:21;;77579:24;;77575:1500;;77622:35;77677:92;77706:11;77719:20;77741:21;77764:4;77677:28;:92::i;:::-;-1:-1:-1;;;;;77856:39:25;;77783:33;77856:39;;;:30;;;:39;;;;;:57;;:64;77619:150;;-1:-1:-1;77783:33:25;-1:-1:-1;77783:33:25;77934:848;77981:1;77950:13;:28;;;-1:-1:-1;;;;;77950:32:25;;:54;;;;;77990:14;77986:1;:18;77950:54;77934:848;;;-1:-1:-1;;;;;78207:39:25;;78112:29;78207:39;;;:30;;;:39;;;;;:57;;:60;;78173:185;;78207:57;78265:1;;78207:60;;;;;;:::i;:::-;;;;;;;;;;;:75;78308:28;;;;-1:-1:-1;;;;;;;;78207:75:25;;;;;;78173:185;:8;:185::i;:::-;78112:264;;78427:21;78395:13;:28;;:53;;;;;;;:::i;:::-;-1:-1:-1;;;;;78395:53:25;;;;;;78466:50;;-1:-1:-1;78466:50:25;;;;:::i;:::-;;;78692:74;78707:13;:28;;;-1:-1:-1;;;;;78692:74:25;-1:-1:-1;;;1513:21:30;78760:5:25;78692:14;:74::i;:::-;-1:-1:-1;;;;;78633:134:25;:28;;;:134;-1:-1:-1;78006:3:25;;;;:::i;:::-;;;;77934:848;;;-1:-1:-1;78799:28:25;;;;-1:-1:-1;;;;;78799:32:25;;78795:270;;78867:183;9875:6;78933:25;78902:13;:28;;;-1:-1:-1;;;;;78902:56:25;;;;;:::i;:::-;78901:84;;;;:::i;:::-;79007:25;78867:12;:183::i;:::-;78851:199;;78795:270;77605:1470;;;77575:1500;79169:24;;79165:1502;;79212:35;79267:93;79296:11;79309:20;79331:21;79354:5;79267:28;:93::i;:::-;-1:-1:-1;;;;;79447:39:25;;79375:33;79447:39;;;:30;;;:39;;;;;:57;;:64;79209:151;;-1:-1:-1;79375:33:25;-1:-1:-1;79375:33:25;79526:848;79573:1;79542:13;:28;;;-1:-1:-1;;;;;79542:32:25;;:54;;;;;79582:14;79578:1;:18;79542:54;79526:848;;;-1:-1:-1;;;;;79799:39:25;;79704:29;79799:39;;;:30;;;:39;;;;;:57;;:60;;79765:185;;79799:57;79857:1;;79799:60;;;;;;:::i;79765:185::-;79704:264;;80019:21;79987:13;:28;;:53;;;;;;;:::i;:::-;-1:-1:-1;;;;;79987:53:25;;;;;;80058:50;;-1:-1:-1;80058:50:25;;;;:::i;:::-;;;80284:74;80299:13;:28;;;-1:-1:-1;;;;;80284:74:25;-1:-1:-1;;;1513:21:30;80352:5:25;80284:14;:74::i;:::-;-1:-1:-1;;;;;80225:134:25;:28;;;:134;-1:-1:-1;79598:3:25;;;;:::i;:::-;;;;79526:848;;;-1:-1:-1;80391:28:25;;;;-1:-1:-1;;;;;80391:32:25;;80387:270;;80459:183;9875:6;80525:25;80494:13;:28;;;-1:-1:-1;;;;;80494:56:25;;;;;:::i;80459:183::-;80443:199;;80387:270;79195:1472;;;77143:3530;;;;;;;;;:::o;2812:1161:23:-;3040:24;3066:30;3098:13;3187:31;3221:11;:22;;;3187:56;;3253:392;;;;;;;;3292:21;:54;;;3253:392;;;;3360:21;:54;;;3253:392;;;;3428:21;:54;;;3253:392;;;;3496:27;3253:392;;;;3547:21;:37;;;3253:392;;;;3598:21;:37;;;3253:392;;;:11;:22;;:392;;;;3656:47;3706:41;3735:11;3706:28;:41::i;:::-;3656:91;;3812:48;3845:14;3812:32;:48::i;:::-;3928:38;;;;3757:103;;;-1:-1:-1;2812:1161:23;;-1:-1:-1;;;;;2812:1161:23:o;58200:970:25:-;58598:22;;58506:24;;;;;58672:106;58711:9;58598:11;58735:8;58745:22;58769:8;58672:38;:106::i;:::-;58630:148;;58808:49;58845:11;58808:36;:49::i;:::-;58789:68;-1:-1:-1;59085:29:25;:23;59111:3;59085:29;:::i;:::-;59057:24;:19;59079:2;59057:24;:::i;:::-;59125:38;;;;-1:-1:-1;58200:970:25;;59057:57;;;;;;-1:-1:-1;;;;;;58200:970:25:o;2272:534:23:-;2435:23;2470:21;2494:62;2516:13;2531:24;2494:21;:62::i;:::-;2470:86;;2590:13;2571:16;:32;2567:72;;;2612:27;;-1:-1:-1;;;2612:27:23;;;;;;;;;;;2567:72;1345:6;2737:13;:35;2733:66;;;2795:4;2774:25;;2733:66;2460:346;2272:534;;;;;:::o;17342:648:21:-;17528:5;17721:22;17745:26;17787:90;17807:4;17813:1;17816:22;17840:29;17871:5;17787:19;:90::i;:::-;17720:157;;;;17888:61;17904:4;17910:16;17928:20;17888:15;:61::i;:::-;-1:-1:-1;17967:16:21;17342:648;-1:-1:-1;;;;17342:648:21:o;11300:1403::-;11497:17;;11438;;11497;;;;;11438;11569:60;11497:17;815:25;839:1;637:2;815:25;:::i;:::-;11569:12;:60::i;:::-;11539:90;;11639:31;11673:27;:62;;;-1:-1:-1;11704:31:21;;;;11673:62;11639:96;;11751:26;11750:27;:55;;;;-1:-1:-1;11781:24:21;;11750:55;11746:951;;;11835:13;;-1:-1:-1;;;11835:13:21;;;;;-1:-1:-1;11746:951:21;;;12129:543;12171:4;:25;;12197:19;12171:46;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;12240:26;:86;;12324:1;12240:86;;;12269:4;:25;;12295:19;12269:46;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;12240:86;12460:26;:168;;12601:24;;;:27;;;12460:168;;;12521:4;:24;;12546:19;12521:45;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;12460:168;12349:305;;12357:4;:24;;12382:19;12357:45;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;12349:54;;:305;12129:20;:543::i;:::-;12115:557;;11746:951;11457:1246;;;11300:1403;;;;:::o;49671:1554:25:-;49953:30;;50038:12;;50034:995;;50084:34;50136:45;50199:34;50251:45;50314:21;50352:141;50386:9;50397:17;50416:8;50426:17;50445:16;50463;50352;:141::i;:::-;50066:427;;;;;;;;;;50532:75;50580:26;50551;:55;;;;:::i;:::-;50532:18;:75::i;:::-;50507:100;-1:-1:-1;50672:41:25;;50668:169;;50733:89;50749:9;50769:13;50784:37;50733:15;:89::i;:::-;50854:41;;50850:169;;50915:89;50931:9;:18;;50951:13;50966:37;50915:15;:89::i;:::-;50052:977;;;;;50034:995;-1:-1:-1;;;;;51088:23:25;;;51084:135;;51159:49;51189:9;51200:7;51159:29;:49::i;:::-;51127:81;;51084:135;49671:1554;;;;;;;;;;:::o;7377:845:21:-;7662:26;;7514:12;;-1:-1:-1;;;7662:26:21;;;;7647:41;;;;7643:573;;7734:17;;;;;;;7776:24;7734:17;7792:7;7776:9;:24::i;:::-;7766:34;;7876:27;7906:48;7928:4;7934:19;7906:21;:48::i;:::-;8021:14;;;;8044:19;;;8021:43;7996:68;;-1:-1:-1;8093:84:21;8112:4;8027:7;7996:68;8150:19;8171:5;8093:18;:84::i;:::-;-1:-1:-1;8201:4:21;;7377:845;-1:-1:-1;;;;;7377:845:21:o;2912:187:53:-;2985:16;3004:6;;-1:-1:-1;;;;;3020:17:53;;;-1:-1:-1;;;;;;3020:17:53;;;;;;3052:40;;3004:6;;;;;;;3052:40;;2985:16;3052:40;2975:124;2912:187;:::o;25619:909:21:-;25814:32;;25703:5;;3685:1:30;;25814:32:21;;;;;;25857:186;;25973:58;25997:4;26003:27;25973:23;:58::i;:::-;901:26;926:1;696;901:26;:::i;:::-;25944:87;;;;:::i;:::-;25905:127;;25857:186;26069:13;;-1:-1:-1;;;26069:13:21;;;;;;;3542:2:30;26138:30:21;;;26204:23;;;;26263;;;;26305:22;;;;-1:-1:-1;26301:186:21;;;26363:12;26347:29;;26301:186;;;26411:12;26401:7;:22;;;26397:90;;;26459:12;26443:29;;26397:90;-1:-1:-1;26514:7:21;;25619:909;-1:-1:-1;;;;;;;25619:909:21:o;5412:255:1:-;1358:16;:14;:16::i;:::-;5570:10:::1;5553:28;::::0;;;:16:::1;:28;::::0;;;;5522:138:::1;::::0;5583:11;;5596:7;;5605:45:::1;::::0;5596:7;5605:24:::1;:45::i;:::-;5522:17;:138::i;11221:419::-:0;11356:5;11363;11454:19;:179;;-1:-1:-1;;;;;11609:23:1;;;;;;:17;:23;;;;;11567:66;;:41;:66::i;:::-;11454:179;;;-1:-1:-1;;;;;11515:23:1;;;;;;:17;:23;;;;;11488:64;;11540:11;11488:26;:64::i;16009:199:21:-;16104:5;16128:4;:25;;16154:46;16167:5;839:1;637:2;815:25;;;;:::i;16154:46::-;16128:73;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;16121:80;;16009:199;;;;:::o;20023:379::-;20198:5;20219:29;:34;;20252:1;20219:34;20215:73;;-1:-1:-1;;;20255:33:21;;20215:73;20306:89;20327:13;20342:21;20365:29;20306:89;;:20;:89::i;5435:111:89:-;5493:7;5312:5;;;5527;;;5311:36;5306:42;;5519:20;5071:294;14684:231:25;14770:16;;;14784:1;14770:16;;;;;;;;;;;;;;;;;;;-1:-1:-1;;14754:32:25;;;;:10;;;;-1:-1:-1;14754:32:25;;;;:::i;:::-;-1:-1:-1;14812:32:25;;;8548:2;14812:32;;;;;;;;;;;;;;;;;;-1:-1:-1;;14796:48:25;;;;:13;;;;-1:-1:-1;14796:48:25;;;;:::i;:::-;-1:-1:-1;14870:38:25;;;8626:3;14870:38;;;;;;;;;;;;;;;;;;-1:-1:-1;;14854:54:25;;;;:13;;;;-1:-1:-1;14854:54:25;;;;:::i;21208:1913:21:-;26839:21;:15;:21;21571;21522:25;;;21548:19;21522:46;;;;;;;:::i;:::-;;;;;;;;;;;;:70;;;;;;;;;;;;;;;;;;;;21650:16;21602:4;:24;;21627:19;21602:45;;;;;;;:::i;:::-;;;;;;;;;;;;:64;;;;;;;;;;;;;;;;;;21724:17;21764:56;21777:19;637:2;21764:12;:56::i;:::-;21744:76;;;;;;;;;-1:-1:-1;;21744:76:21;;;;;;;;-1:-1:-1;21836:31:21;;;;;21835:32;:50;;;;-1:-1:-1;21871:14:21;;21835:50;21831:119;;;21901:38;;-1:-1:-1;;21901:38:21;21935:4;21901:38;;;21831:119;22067:27;;-1:-1:-1;;;22067:27:21;;;;;22036:28;;22216:25;;;;22242:60;;22255:18;;;;;901:26;22242:12;:60::i;:::-;22216:87;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;22177:16;:126;22166:137;;22347:21;22335:33;;:8;:33;;;;:48;;;;22372:11;22335:48;22331:689;;;22434:18;;;;;;;22578:21;22527:26;;;22434:18;22527:48;;;;;;;:::i;:::-;;;;;;;;;;;;:72;;;;;;;;;;;;;;;;;;;;22667:16;22617:4;:25;;22643:20;22617:47;;;;;;;:::i;:::-;;;;;;;;;;;;:66;;;;;;;;;;;;;;;;;;22792:58;22805:20;696:1;22792:12;:58::i;:::-;22771:79;;-1:-1:-1;;22771:79:21;;;;;;;;;;;;;;;;;;-1:-1:-1;22771:79:21;22874:32;;;22873:33;:51;;;;-1:-1:-1;22910:14:21;;22873:51;22869:137;;;22948:39;;-1:-1:-1;;22948:39:21;;;;;22869:137;22385:635;22331:689;-1:-1:-1;;23091:23:21;;;;;;-1:-1:-1;;;23091:23:21;-1:-1:-1;;23091:23:21;;;;;;;;;;-1:-1:-1;;;;;21208:1913:21:o;20408:282::-;20530:44;;-1:-1:-1;;20584:48:21;-1:-1:-1;;;20530:44:21;;;;-1:-1:-1;;;;20584:48:21;;-1:-1:-1;;;20584:48:21;;;;;;;20648:35;;-1:-1:-1;9727:21:190;;;9709:40;;20648:35:21;;9697:2:190;9682:18;20648:35:21;;;;;;;20408:282;;;:::o;86122:1376:25:-;86339:30;86371:32;-1:-1:-1;;;;;;;;;;;;;;;;;;;86371:32:25;86416:25;86443:24;86469:26;86511:68;86535:11;86548:20;86570:8;86511:23;:68::i;:::-;86415:164;;;;;;86615:36;86634:16;86615:18;:36::i;:::-;-1:-1:-1;;;;;86589:62:25;;;:23;9611:2;86697:38;;:291;;86932:56;2194:7:30;86956:25:25;86980:1;86956:20;:25;:::i;:::-;:31;;;;:::i;:::-;86932:23;:56::i;:::-;86697:291;;;;;86750:167;86799:17;86818:11;:33;;;86853:18;86873:20;86895:8;86750:31;:167::i;:::-;86661:327;;86999:34;87085:18;87037:8;:37;;8073:1;87037:37;;;-1:-1:-1;;87037:37:25;87036:68;;;;:::i;:::-;86999:105;-1:-1:-1;87141:56:25;86999:105;87141:26;:56;:::i;:::-;87115:82;;87208:22;87233:62;87267:26;87233:27;:62::i;:::-;87208:87;;87387:104;87406:84;87426:17;87445:14;87461:18;87481:8;87406:19;:84::i;:::-;87387:18;:104::i;:::-;-1:-1:-1;;;;;87347:144:25;:25;;;:144;-1:-1:-1;86122:1376:25;;87347:10;;-1:-1:-1;86122:1376:25;;-1:-1:-1;;;;;;;;86122:1376:25:o;5617:111:89:-;5675:7;5312:5;;;5709;;;5311:36;5306:42;;5701:20;5071:294;1908:204:20;1997:14;2032:5;2036:1;2032;:5;:::i;:::-;2023:14;;2056:10;:49;;2095:10;2104:1;2095:6;:10;:::i;:::-;2056:49;;;2069:23;2082:6;2090:1;2069:12;:23::i;:::-;2047:58;1908:204;-1:-1:-1;;;;;1908:204:20:o;6215:704:89:-;6277:7;6300:1;6305;6300:6;6296:150;;6400:35;1035:4:79;6400:11:89;:35::i;:::-;6896:1;6891;6887;:5;6886:11;;;;;:::i;:::-;;6900:1;6886:15;6876:5;;;6860:22;:42;;6215:704;-1:-1:-1;;;6215:704:89:o;3450:452:29:-;3546:36;-1:-1:-1;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3546:36:29;3628:22;;:33;3594:31;;;:67;3750:27;3628:22;3750:14;:27::i;:::-;3710:36;;;3671:106;3672:36;;;3671:106;3868:27;3883:11;3868:14;:27::i;:::-;3827:37;;;3787:108;;;3788:14;3450:452;-1:-1:-1;3450:452:29:o;13211:1441::-;13478:36;;;;13438:37;;13596:36;;;;13556:37;;;;13317:27;;;;;;13438:76;-1:-1:-1;13438:76:29;13556;13647:25;;13438:76;13647:54;;;13677:24;13676:25;13647:54;13643:1003;;;13906:14;:37;;;13867:14;:36;;;:76;13806:14;:37;;;13767:14;:36;;;:76;:177;13745:199;;13643:1003;;;13980:24;13975:671;;14109:14;:37;;;14070:14;:36;;;:76;14048:98;;14232:14;:36;;;14192:14;:37;;;:76;14164:104;;14307:4;14296:15;;13975:671;;;14333:24;14328:318;;14462:14;:37;;;14423:14;:36;;;:76;14401:98;;14585:14;:36;;;14545:14;:37;;;:76;14517:104;;14328:318;13396:1256;;13211:1441;;;;;:::o;59574:1832:25:-;59846:27;59885:24;59933:47;59983:41;60012:11;59983:28;:41::i;:::-;59933:91;;60082:48;60115:14;60082:32;:48::i;:::-;-1:-1:-1;60039:91:25;-1:-1:-1;60039:91:25;-1:-1:-1;60152:21:25;;-1:-1:-1;60152:21:25;;;60256:99;60301:9;60312:8;60322:22;60346:8;60256:44;:99::i;:::-;60151:204;;;;;;;;60383:1;60369:11;:15;60365:476;;;60466:22;;60451:83;;342:1:19;60466:32:25;;;;;60500:13;60515:11;60528:5;60451:14;:83::i;:::-;60400:22;;:32;;:134;60614:22;;60599:83;;373:1:19;60614:32:25;;60599:83;60548:22;;:32;;:134;60762:22;;60747:83;;404:1:19;60762:32:25;;60747:83;60696:22;;:32;;:134;60365:476;60854:16;;60850:550;;60996:22;;60981:92;;60996:22;:33;;;;;61031:14;61047:12;475:4:19;60981:14:25;:92::i;:::-;60929:22;;:144;61154:22;;61139:92;;279:1:19;61154:33:25;;61139:92;61087:22;;:33;;:144;61312:22;;61297:92;;311:1:19;61312:33:25;;61297:92;61245:22;;:33;;:144;60850:550;59875:1531;;;;;59574:1832;;;;;;;:::o;9935:764:23:-;10049:24;10132;10158:30;10192:42;10222:11;10192:29;:42::i;:::-;10131:103;;;;10529:153;10570:22;10566:1;:26;:102;;10667:1;10529:19;:153::i;10566:102::-;10595:69;10610:16;2789:6:30;10634:22:23;10658:5;10595:14;:69::i;:::-;10529:19;:153::i;11159:500::-;11292:21;11418:13;11435:1;11418:18;11414:73;;-1:-1:-1;;;11452:24:23;;11414:73;11574:68;11589:24;2789:6:30;11621:13:23;11636:5;11574:14;:68::i;16214:179:21:-;16292:7;16342:10;;:34;;16375:1;16367:5;:9;16342:34;;;-1:-1:-1;16355:9:21;16214:179;-1:-1:-1;16214:179:21:o;23736:342::-;23897:10;24047:12;24015:21;23992:20;:44;23991:69;;;;;;;:::i;:::-;;;23736:342;-1:-1:-1;;;;23736:342:21:o;52040:1840:25:-;52339:34;;;;;52727:139;4401:3:30;6799:2:25;52791:16;52751:37;52771:17;52751;:37;:::i;:::-;:56;;;;:::i;:::-;52750:99;;;;:::i;:::-;:106;;;;:::i;:::-;52727:9;:139::i;:::-;52711:155;;52877:37;52917:66;52947:16;52965:17;52917:29;:66::i;:::-;52877:106;;53062:34;53099:33;53122:9;53099:22;:33::i;:::-;53062:70;-1:-1:-1;53213:290:25;53252:9;53284:13;53311:8;53333:29;53062:70;53416:17;53447:16;53477;53213:25;:290::i;:::-;53143:360;;-1:-1:-1;53143:360:25;-1:-1:-1;53583:290:25;53622:18;;;53654:13;53681:8;53703:29;53746:26;53786:17;53817:16;53847;53583:25;:290::i;:::-;52040:1840;;;;-1:-1:-1;53513:360:25;-1:-1:-1;53513:360:25;;-1:-1:-1;52040:1840:25;;-1:-1:-1;52040:1840:25;;-1:-1:-1;;;;;;52040:1840:25:o;10282:218:90:-;10339:7;-1:-1:-1;;;;;10362:25:90;;10358:105;;;10410:42;;-1:-1:-1;;;10410:42:90;;10441:3;10410:42;;;22052:36:190;22104:18;;;22097:34;;;22025:18;;10410:42:90;21870:267:190;10358:105:90;-1:-1:-1;10487:5:90;10282:218::o;42741:507:25:-;42932:19;;42903:26;;42932:23;;:19;;;;;;:23;:::i;:::-;42903:52;;;;42985:18;42969:13;:34;42965:277;;;43044:13;43019:213;43071:18;43059:9;:30;43019:213;;;43181:36;43122:4;:10;;43133:9;43122:21;;;;;;;:::i;:::-;;;;:55;;;:95;;;;;;;:::i;:::-;;;;-1:-1:-1;;43091:11:25;;43019:213;;;;42965:277;42893:355;42741:507;;;:::o;57031:512::-;57163:30;57314:69;57333:49;57354:9;:18;;57374:7;57333:20;:49::i;57314:69::-;57230;57249:49;57270:9;57290:7;57249:20;:49::i;57230:69::-;:153;;;;:::i;:::-;-1:-1:-1;;;;;57394:39:25;;;57459:1;57394:39;;;:30;;;:39;;;;;;;;:66;;-1:-1:-1;;57394:66:25;;;;;;57470:30;;;;:39;;;;;:66;;;;;;;;57205:178;57031:512;-1:-1:-1;57031:512:25:o;24491:496:21:-;24629:14;901:26;926:1;696;901:26;:::i;:::-;24846:35;;24896:27;24891:90;;24948:18;;:22;;24969:1;;24948:18;;;;;:22;:::i;:::-;24939:31;;;24491:496;-1:-1:-1;;;24491:496:21:o;15394:3441:25:-;-1:-1:-1;;;;;15600:23:25;;15596:61;;15632:25;;-1:-1:-1;;;15632:25:25;;;;;;;;;;;15596:61;15764:11;:33;;;15801:1;15764:38;15760:3069;;-1:-1:-1;;;;;15903:39:25;;:18;:39;;;:30;;;:39;;;;;:46;;;15899:143;;;15969:58;15990:9;16010:7;8073:1;15969:20;:58::i;:::-;;15899:143;-1:-1:-1;;;;;16059:39:25;;;;;;:30;;;:39;;;;;:46;;;16055:152;;;16125:67;16146:9;:18;;16166:7;-1:-1:-1;;16125:20:25;:67::i;:::-;;16055:152;15760:3069;;;16321:32;16355;16407:43;16427:11;:22;;;16407:19;:43::i;:::-;16493:29;;;;16564;;;;16320:130;;-1:-1:-1;16320:130:25;;-1:-1:-1;16493:29:25;16666:28;;16662:719;;16714:29;;;:56;;;16788:29;;;:56;;;-1:-1:-1;;16951:98:25;16714:11;16746:24;17019:23;17044:4;16951:28;:98::i;:::-;16862:187;;;;17067:299;17124:9;:18;;17164:10;17196:7;17225:23;17270:11;:33;;;17325:23;17067:35;:299::i;:::-;16696:685;;16662:719;17453:28;;17449:720;;17501:29;;;:56;;;17575:29;;;:56;;;-1:-1:-1;;17738:99:25;17501:11;17533:24;17806:23;-1:-1:-1;17738:28:25;:99::i;:::-;17649:188;;;;17855:299;17912:9;:18;;17952:10;17984:7;18013:23;18058:11;:33;;;18113:23;17855:35;:299::i;:::-;17483:686;;17449:720;18247:29;;;:49;;;18310:29;;;:49;;;18402:33;;;;-1:-1:-1;;18392:44:25;;:9;:44::i;:::-;18563:33;;18598:18;;;:33;18374:62;;-1:-1:-1;9559:1:25;;18554:78;;18563:33;;;;;;;;;18598;;;;;18554:8;:78::i;:::-;:135;;;;:::i;:::-;18524:7;:165;18503:266;;;18729:25;;-1:-1:-1;;;18729:25:25;;;;;;;;;;;18503:266;18783:17;;;:35;;;;;;-1:-1:-1;;18783:35:25;;;;;;;;;-1:-1:-1;;;;15394:3441:25;;;;:::o;9767:452:21:-;9868:13;9883;9908:17;9928:35;9951:4;9957:5;9928:22;:35::i;:::-;9991:13;;9908:55;;-1:-1:-1;;;;9991:13:21;;;;10136:54;9908:55;9991:13;;10136:19;:54::i;:::-;10115:75;;-1:-1:-1;10115:75:21;-1:-1:-1;10200:12:21;10211:1;10115:75;10200:12;:::i;:::-;;;9898:321;;9767:452;;;:::o;8850:490::-;9000:32;;8941:5;;;;9000:32;;;;;8941:5;;;9101:51;9000:32;;9101:16;:51::i;:::-;9042:110;;;;;;9162:14;9179:58;9203:4;9209:27;9179:23;:58::i;:::-;9162:75;;9254:79;9275:12;9289:11;9302:9;9313:11;9326:6;9254:20;:79::i;:::-;9247:86;;;;;;;;;8850:490;;;;;:::o;16399:191::-;16486:5;16561:11;16541:12;16556:1;16541:16;16540:32;;;;;:::i;:::-;;;16399:191;-1:-1:-1;;;16399:191:21:o;87891:1193:25:-;88065:25;88092:22;88116:26;88154:47;88204:41;88233:11;88204:28;:41::i;:::-;88154:91;;88255:28;88335:48;88368:14;88335:32;:48::i;:::-;88293:90;;;;;;;;;88415:142;88454:14;88470:20;88492:11;:33;;;88527:20;88415:25;:142::i;:::-;88394:163;;88747:8;:330;;88931:146;88971:14;88987:11;:29;;;89018:11;:38;;;89058:5;88931:22;:146::i;:::-;88747:330;;;88770:146;88810:14;88826:11;:29;;;88857:11;:38;;;88897:5;88770:22;:146::i;:::-;88727:350;;88144:940;;87891:1193;;;;;;;:::o;9264:218:90:-;9321:7;-1:-1:-1;;;;;9344:25:90;;9340:105;;;9392:42;;-1:-1:-1;;;9392:42:90;;9423:3;9392:42;;;22052:36:190;22104:18;;;22097:34;;;22025:18;;9392:42:90;21870:267:190;1966:3501:26;2048:5;821:7;2069:11;:31;:66;;;;2124:11;-1:-1:-1;;;2104:31:26;2069:66;2065:97;;;2144:18;;-1:-1:-1;;;2144:18:26;;;;;;;;;;;2065:97;-1:-1:-1;;;;;2271:41:26;;2268:1;2264:49;2361:9;;;2434:18;2428:25;;2425:1;2421:33;2502:9;;;2575:10;2569:17;;2566:1;2562:25;2635:9;;;2708:6;2702:13;;2699:1;2695:21;2764:9;;;2837:4;2831:11;;2828:1;2824:19;;;2891:9;;;2964:3;2958:10;;2955:1;2951:18;3017:9;;;3084:10;;;3081:1;3077:18;;;3143:9;;;;3203:10;;;2474;;2607;;;2736;;;2863;2989;;;3115;3233;2173:9;3323:3;3316:10;;3312:95;;3354:3;3348;:9;3332:11;:26;;3328:30;;3312:95;;;3403:3;3397;:9;3381:11;:26;;3377:30;;3312:95;-1:-1:-1;3515:9:26;;;3510:3;3506:19;;;3547:11;;;;3625:9;;;;3690;;3681:19;;;3722:11;;;3800:9;3865;;3856:19;;;3897:11;;;3975:9;4040;;4031:19;;;4072:11;;;4150:9;4215;;4206:19;;;4247:11;;;4325:9;4390;;4381:19;;;4422:11;;;4500:9;4565;;4556:19;;;4597:11;;;4675:9;4740;;4731:19;;;4772:11;;;4850:9;4915;;4906:19;;;;4947:11;;;;5025:9;;;;;3515;-1:-1:-1;;3433:17:26;;3455:2;3432:25;3596:10;;;;;-1:-1:-1;;;3596:10:26;3583:24;3771:10;;;;;;;3758:24;;;;3946:10;;;;;;;3933:24;;;;4121:10;;;;;;;4108:24;;;;4296:10;;;;;;;4283:24;4471:10;;;;;;;4458:24;4646:10;;;;;;;4633:24;4821:10;;;;;;;4808:24;4996:10;;;;;;;4983:24;550:20;5092:39;;-1:-1:-1;;5168:40:26;;3447:3;5167:49;;;;734:34;5253:39;;5252:48;;5319:17;;;;;;;;;5315:37;;-1:-1:-1;5345:7:26;1966:3501;-1:-1:-1;;;;;;1966:3501:26:o;5315:37::-;5396:11;5370:22;5385:6;5370:14;:22::i;:::-;:37;5366:56;;5416:6;1966:3501;-1:-1:-1;;;;;;;1966:3501:26:o;5366:56::-;-1:-1:-1;5443:7:26;1966:3501;-1:-1:-1;;;;;;1966:3501:26:o;94522:1554:25:-;94763:36;;;-1:-1:-1;;;94763:36:25;94938;9662:2;94938:18;:36;:::i;:::-;94919:55;-1:-1:-1;94993:9:25;94988:232;95012:8;95008:1;:12;94988:232;;;95062:72;95077:18;1843:21:30;-1:-1:-1;;;95128:5:25;95062:14;:72::i;:::-;95041:93;-1:-1:-1;95184:3:25;;94988:232;;;;-1:-1:-1;;;95476:272:25;6371:3;95512:17;:50;-1:-1:-1;;;95584:18:25;:24;95683:20;95659:21;-1:-1:-1;;;95631:18:25;:24;95630:50;:73;95725:5;95476:14;:272::i;:::-;:278;95262:492;;95801:8;:37;;-1:-1:-1;;95801:37:25;;;8073:1;95801:37;95783:56;-1:-1:-1;95860:11:25;;-1:-1:-1;95874:38:25;95898:13;95910:1;95898:8;:13;:::i;95874:38::-;95860:52;;;;95922:14;-1:-1:-1;;95940:4:25;:9;95939:38;;;;95962:1;95955:4;:8;:21;;;;;95968:8;95967:9;95955:21;95939:61;;8166:1;95939:61;;;95980:9;95939:61;;;95922:78;-1:-1:-1;95922:78:25;96043:16;;;;;:4;:16;:::i;:::-;:26;;;;:::i;:::-;96011:58;94522:1554;-1:-1:-1;;;;;;;;;;;94522:1554:25:o;1452:464:26:-;1529:22;-1:-1:-1;;1567:15:26;;;;;;:34;;-1:-1:-1;1586:15:26;;;;1234:6;1586:15;1567:34;1563:64;;;1610:17;;-1:-1:-1;;;1610:17:26;;;;;;;;;;;1563:64;1655:12;;;;1638:14;1703:11;;;:32;;1728:7;1703:32;;;1717:8;1718:7;1717:8;:::i;:::-;1677:59;;1797:2;1764:29;1785:7;1764:20;:29::i;:::-;:35;;1747:52;;1835:4;1831:8;;:1;:8;1827:65;;;1858:34;1878:14;1858:17;:34;:::i;1827:65::-;1553:363;;1452:464;;;:::o;90619:1387:25:-;90806:18;-1:-1:-1;;;90806:18:25;90900:49;90909:36;9662:2;90909:18;:36;:::i;:::-;90947:1;90900:8;:49::i;:::-;90881:68;-1:-1:-1;90964:9:25;90959:218;90983:8;90979:1;:12;90959:218;;;91032:75;91047:21;1843::30;-1:-1:-1;;;91101:5:25;91032:14;:75::i;:::-;91008:99;-1:-1:-1;91149:3:25;;90959:218;;;;91547:8;:188;;91664:71;91687:17;91706;-1:-1:-1;;;91730:4:25;91664:22;:71::i;:::-;91547:188;;;91574:71;91597:17;91616;-1:-1:-1;;;91640:4:25;91574:22;:71::i;:::-;91520:225;-1:-1:-1;91808:191:25;91836:72;91849:34;91862:21;91520:225;91849:34;:::i;:::-;91885:22;-1:-1:-1;;;1513:21:30;91885:22:25;:::i;:::-;91836:12;:72::i;:::-;6371:3;4401::30;91984:5:25;91808:14;:191::i;:::-;91795:204;90619:1387;-1:-1:-1;;;;;;;90619:1387:25:o;1776:194:79:-;1881:10;1875:4;1868:24;1918:4;1912;1905:18;1949:4;1943;1936:18;9114:996:29;9302:22;;:32;;;;9415;;;;;9302;;9415:36;9411:342;;9532:22;;9492:250;;373:1:19;9532:32:29;;;;9582:11;:29;;;9668:11;:38;;;9724:4;9492:22;:250::i;:::-;9467:275;;;;:::i;:::-;;;9411:342;9766:22;;:32;;;:36;9762:342;;9883:22;;9843:250;;404:1:19;9883:32:29;;;;9933:11;:29;;;10019:11;:38;;;10075:4;9843:22;:250::i;:::-;9818:275;;;;:::i;:::-;;;9762:342;9114:996;;;:::o;8096:1012::-;8287:22;;:33;;;8407;;;;8287;;8403:37;8399:347;;8522:22;;8482:253;;279:1:19;8522:33:29;;;;8573:11;:29;;;8660:11;:38;;;8716:5;8482:22;:253::i;:::-;8456:279;;;;:::i;:::-;;;8399:347;8763:22;;:33;;;8759:37;8755:347;;8878:22;;8838:253;;311:1:19;8878:33:29;;;;8929:11;:29;;;9016:11;:38;;;9072:5;8838:22;:253::i;61626:1779:25:-;61866:21;61889:19;61910:22;61934:20;61970:23;62008:8;:94;;-1:-1:-1;;;;;62062:40:25;;;;;;:30;;;:40;;;;;62008:94;;;-1:-1:-1;;;;;62019:40:25;;:18;:40;;;:30;;;:40;;;;;62008:94;62135:25;;;;:32;61970:132;;-1:-1:-1;62235:17:25;;;62231:67;;62276:1;62279;62282;62285;62268:19;;;;;;;;;;;;62231:67;-1:-1:-1;;;62332:36:25;62452:12;62435:954;62466:5;;62435:954;;62496:37;62536:7;:25;;62566:1;62562;:5;62536:32;;;;;;;;:::i;:::-;;;;;;;;;62496:72;;;;;;;;;62536:32;;62496:72;-1:-1:-1;;;;;62496:72:25;;;;;;-1:-1:-1;;;62496:72:25;;;;;;;;;;62666:51;;;;;62496:72;;-1:-1:-1;62496:72:25;62795:78;;62496:72;;-1:-1:-1;;;2443:21:30;62837:28:25;;62795:14;:78::i;:::-;62780:93;;;;62907:78;62922:20;62944:28;-1:-1:-1;;;62979:5:25;62907:14;:78::i;:::-;62891:94;;;;63030:22;63008:19;:44;63004:228;;;63151:11;63135:27;;63201:12;63184:29;;63004:228;63300:74;63315:28;1513:21:30;-1:-1:-1;;;63368:5:25;63300:14;:74::i;:::-;63249:125;-1:-1:-1;;;;;62473:3:25;62435:954;;;;62308:1091;;61960:1445;;61626:1779;;;;;;;;;;:::o;13338:846:23:-;13457:24;13483:30;13571:47;13621:41;13650:11;13621:28;:41::i;:::-;13571:91;;13719:48;13752:14;13719:32;:48::i;:::-;13673:94;;;;;;;;;14038:139;14082:16;14136:14;:31;;;14100:11;:33;;;:67;;;;:::i;:::-;14038:30;:139::i;:::-;14019:158;;13515:669;13338:846;;;:::o;12402:930::-;12485:24;774:4;12525:7;:41;12521:805;;;844:4;12586:7;:32;12582:734;;;1005:6;12833:68;923:6;12880:7;2789:6:30;12895:5:23;12833:14;:68::i;:::-;:125;;;;:::i;12582:734::-;1205:4;13176:68;1121:4;13223:7;2789:6:30;13238:5:23;13176:14;:68::i;:::-;:125;;;;:::i;12582:734::-;12402:930;;;:::o;83157:920:25:-;83233:12;7887:3;83290:10;:38;83286:785;;7720:34;83348:10;:40;83344:62;;83397:9;83405:1;8477:4;83397:9;:::i;83344:62::-;83522:17;83542;-1:-1:-1;;;83542:10:25;:17;:::i;:::-;83522:37;;83721:10;7655:4;83734:34;83758:9;83734:23;:34::i;:::-;:48;;;;:::i;:::-;83721:61;-1:-1:-1;7393:35:25;7492;-1:-1:-1;;;83968:25:25;83980:12;;;;83968:1;:25;:::i;:::-;:32;;;;:::i;:::-;:65;;;;:::i;:::-;83967:93;;;;:::i;11621:455:22:-;11755:19;11790:24;;11786:284;;11984:61;93:4:50;11997:19:22;:25;12024:20;11984:12;:61::i;54603:2180:25:-;54944:30;54976:41;55105:32;55140:51;55171:4;55177:13;55140:30;:51::i;:::-;55105:86;;55263:24;-1:-1:-1;;;;;55263:29:25;55291:1;55263:29;55259:48;;55302:1;55305;55294:13;;;;;;;55259:48;55360:34;55397:174;55455:29;55486:26;55514:24;55540:17;55397:40;:174::i;:::-;55360:211;;55586:38;55627:152;55684:8;55694:24;-1:-1:-1;;;;;55627:152:25;55720:17;55739:26;55627:39;:152::i;:::-;55586:193;;55885:30;55934:84;55949:24;-1:-1:-1;;;;;55934:84:25;55975:30;93:4:50;56012:5:25;55934:14;:84::i;:::-;55885:133;;56078:41;56138:68;-1:-1:-1;;;56151:22:25;:28;56181:24;-1:-1:-1;;;;;56138:68:25;:12;:68::i;:::-;56078:128;;56306:100;56323:33;56358:16;56376;475:4:19;56306:16:25;:100::i;:::-;56254:152;;56533:87;56548:33;56583:24;-1:-1:-1;;;;;56533:87:25;-1:-1:-1;;;56614:5:25;56533:14;:87::i;:::-;56492:128;-1:-1:-1;56676:90:25;56492:128;56717:16;56735;56753:12;56676:16;:90::i;:::-;56635:131;;55029:1748;;;;;54603:2180;;;;;;;;;;;;:::o;44154:1836::-;-1:-1:-1;;;;;44376:25:25;;44260:30;44376:25;;;:16;;;:25;;;;;:37;44503:9;;44376:37;;;;;;;44260:30;;44503:9;;:24;;-1:-1:-1;;44503:24:25;;;8073:1;44503:24;-1:-1:-1;;;;;44567:25:25;;44542:22;44567:25;;;:16;;;:25;;;;;:43;;:50;44477;;-1:-1:-1;44670:1199:25;44716:14;44701:12;:29;44670:1199;;;-1:-1:-1;;;;;44898:25:25;;44827:48;44898:25;;;:16;;;:25;;;;;:43;;:57;;44942:12;;44898:57;;;;;;:::i;:::-;;;;;;;;;;44827:128;;;;;;;;;44898:57;;44827:128;-1:-1:-1;;;;;44827:128:25;;;;;;-1:-1:-1;;;44827:128:25;;;;;;;;;;;;-1:-1:-1;44977:43:25;44973:805;;45115:34;;;;45100:12;45115:34;;;:18;;;:34;;;;;;45598:39;;45115:34;;;;;45100:12;45539:143;;45115:4;;:34;;-1:-1:-1;;;;;45539:143:25;45639:7;45648:12;45539:21;:143::i;:::-;-1:-1:-1;;;;;45420:25:25;;;;;;:16;;;:25;;;;;:80;;:94;;45501:12;;45420:94;;;;;;:::i;:::-;;;;;;;;;;45339:343;45704:55;;;;;-1:-1:-1;;44973:805:25;-1:-1:-1;45827:27:25;;;;44732:14;;44670:1199;;;;44302:1577;;;45941:42;45960:22;45941:18;:42::i;:::-;-1:-1:-1;;;;;45889:25:25;;;;;;:16;;;:25;;;;;:94;;:48;;:94;;;;;;;-1:-1:-1;;;;;45889:94:25;;:::i;:::-;;;;;;;;-1:-1:-1;;;;;45889:94:25;;;;;-1:-1:-1;;;;;45889:94:25;;;;;;44154:1836;;;;:::o;21285:1938::-;-1:-1:-1;;;;;21508:25:25;;21424:26;21508:25;;;:16;;;:25;;;;;:37;;;21580:43;;;:50;21508:37;;;;;;;;21424:26;21772:1314;21818:14;21803:12;:29;21772:1314;;;21959:7;9225:3;21945:21;:46;;;;-1:-1:-1;;21970:7:25;:21;21945:46;21993:5;21941:57;-1:-1:-1;;;;;22083:25:25;;22013:51;22083:25;;;:16;;;:25;;;;;:43;;:57;;22127:12;;22083:57;;;;;;:::i;:::-;;;;;;;;;;22013:127;;;;;;;;;22083:57;;22013:127;-1:-1:-1;;;;;22013:127:25;;;;;-1:-1:-1;;;22013:127:25;;;;;;;;;;;-1:-1:-1;22237:48:25;22233:723;;22380:34;;;;22362:15;22380:34;;;:18;;;:34;;;;;;;;22504:148;22380:4;22564:29;22405:7;22380:34;22613:7;22622:12;22504:32;:148::i;:::-;22696:19;;;;;;;22671:22;22752:52;22768:10;;;22696:19;22768:26;;;;;;;:::i;:::-;;;;2022:12:28;;:19;;1920:129;22752:52:25;:57;;;;-1:-1:-1;22831:25:25;;;:40;;;;;22860:11;22831:40;22827:115;;;22919:4;22895:28;;22827:115;22287:669;;;22233:723;-1:-1:-1;23034:27:25;;;;21834:14;;21772:1314;;;-1:-1:-1;;;;;;23191:25:25;;;;;;:16;;;:25;;;;;23184:32;;-1:-1:-1;;23184:32:25;;;23191:25;23184:32;;;;23191:25;23184:32;:::i;:::-;;;;;;;:::i;:::-;;;21452:1771;;21285:1938;;;;;:::o;68070:5273::-;68405:20;;;;68373:21;;68492:20;;;;68405;68460:21;;;68579:20;;;;68547:21;;;;68166:34;;;;68366:60;;68453;;;;68540;4401:3:30;68997:26:25;68166:34;;;69112:15;;;;:42;;69144:10;69143:11;;69112:42;;;69130:10;69112:42;69088:67;-1:-1:-1;69170:17:25;69190:15;-1:-1:-1;69190:15:25;;69243:39;;69272:10;69271:11;;69243:39;;;69258:10;69243:39;7233:3;69313:53;;-1:-1:-1;69313:53:25;;-1:-1:-1;69433:12:25;69428:43;;69461:10;69460:11;;69447:24;;69428:43;69486:17;69506:15;-1:-1:-1;69506:15:25;;69559:39;;69588:10;69587:11;;69559:39;;;69574:10;69559:39;6988:2;69629:44;;-1:-1:-1;69629:44:25;;-1:-1:-1;69740:12:25;69735:43;;69768:10;69767:11;;69754:24;;69735:43;68748:1041;;69827:13;69844:1;69827:18;69823:2707;;70050:26;70079:56;70094:13;-1:-1:-1;;;70114:13:25;70129:5;70079:14;:56::i;:::-;70199:20;;;;70050:85;;-1:-1:-1;70223:1:25;70199:25;70195:270;;70277:18;70248:47;;70195:270;;;70428:18;70399:47;;70195:270;69847:632;69823:2707;;;70526:13;70543:1;70526:18;70522:2008;;70701:26;70730:56;70745:13;-1:-1:-1;;;70765:13:25;70780:5;70730:14;:56::i;:::-;70850:20;;;;70701:85;;-1:-1:-1;70874:1:25;70850:25;70846:270;;70928:18;70899:47;;70846:270;;;71079:18;-1:-1:-1;70522:2008:25;;;71177:13;71194:1;71177:18;71173:1357;;71534:21;;;;71530:25;71526:116;;71587:21;;;;71583:25;71579:44;;-1:-1:-1;71618:1:25;;;;-1:-1:-1;68070:5273:25;-1:-1:-1;;;;;;;68070:5273:25:o;71579:44::-;71675:26;71704:68;71714:57;71729:13;-1:-1:-1;;;71750:13:25;71765:5;71714:14;:57::i;:::-;71704:9;:68::i;:::-;71840:21;;;;71675:97;;-1:-1:-1;71836:25:25;71832:270;;71914:18;71885:47;;71832:270;;71173:1357;72255:260;72315:182;;;;;;;;72382:10;72315:182;;;;72394:10;72315:182;;;;72406:10;72315:182;;;;72418:13;72315:182;;;;72433:10;373:1:19;72433:20:25;;;;;;;:::i;:::-;;;;;72315:182;;;;72455:10;404:1:19;72455:20:25;;;;;;;:::i;:::-;;;;;72315:182;;72255:38;:260::i;:::-;72196:319;;-1:-1:-1;72196:319:25;-1:-1:-1;71173:1357:25;1323:6:26;72590:26:25;:59;:142;;;;72706:26;1385:34:26;72673:59:25;72590:142;72569:241;;;72794:1;72765:30;;72569:241;1323:6:26;72844:26:25;:59;:142;;;;72960:26;1385:34:26;72927:59:25;72844:142;72823:241;;;73048:1;73019:30;;72823:241;73128:30;;73124:203;;73227:85;73242:26;4401:3:30;6988:2:25;73306:5;73227:14;:85::i;:::-;73178:134;;73124:203;68238:5105;;;;;;68070:5273;;;:::o;19376:1474::-;19750:9;;19724:23;;19750:9;;:38;;-1:-1:-1;;19750:38:25;;;8073:1;19750:38;-1:-1:-1;;;;;19987:25:25;;19854:26;19987:25;;;:16;;;:25;;;;;:32;19724:64;;-1:-1:-1;19854:26:25;;;19987:32;;19983:140;;;20059:53;20080:4;20086:7;20095:16;20059:20;:53::i;:::-;20035:77;;19983:140;20196:28;;;;-1:-1:-1;;;;;20192:32:25;;20188:353;;20262:268;20295:4;20317:7;20342:16;20376:26;20420:13;20451:24;20493:23;20262:15;:268::i;:::-;20240:290;;20188:353;20588:21;:45;;;;;20614:19;20613:20;20588:45;20584:260;;;20778:19;;20726:92;;20778:19;;8410:1;;8548:2;;20778:19;;;;;:39;;20726:25;:92::i;:::-;20677:142;;;;;;;;;-1:-1:-1;;20677:142:25;;;;;;20584:260;19659:1191;;;19376:1474;;;;;;:::o;15745:258:21:-;15823:9;15834;15872:1;15868:5;;:1;:5;;;:23;;15886:1;15889;15868:23;;;15877:1;15880;15868:23;15855:36;;-1:-1:-1;15855:36:21;-1:-1:-1;15905:7:21;;;;;;;;;15901:96;;;15934:1;15928:7;;15901:96;;;15962:1;15956:7;;:3;:7;;;15952:45;;;-1:-1:-1;15745:258:21;;;;:::o;10630:357::-;10762:5;10769;10776;10814:58;10838:4;10844:27;10814:23;:58::i;:::-;10886:57;10909:4;10915:27;10886:22;:57::i;:::-;10957:13;;10793:187;;-1:-1:-1;;;10957:13:21;;;;;;-1:-1:-1;10630:357:21;-1:-1:-1;;;10630:357:21:o;14247:1265::-;14437:13;14452;14498:69;14517:12;14531:11;14544:9;14555:11;14498:18;:69::i;:::-;14477:90;;-1:-1:-1;14477:90:21;-1:-1:-1;14771:25:21;;;;:15;;;;:25;14747:13;14880:154;752:3;901:26;14942:35;;;;14747:13;14880:14;:154::i;:::-;14852:5;:182;14812:236;;-1:-1:-1;;15256:25:21;;15247:6;15234:9;15227:17;;:26;:54;:102;;-1:-1:-1;;15227:102:21;;;15302:6;15290:9;:18;;;15227:102;15201:128;-1:-1:-1;1234:6:26;15386:25:21;15353:17;;;:26;;:30;:58;:142;;1234:6:26;15353:142:21;;;15448:6;15436:9;:18;;;15457:1;15436:22;15353:142;15343:152;;14676:830;;14247:1265;;;;;;;;:::o;92515:1531:25:-;92706:26;92744:12;9973:19;92931:617;92959:339;92991:88;93006:4;7153:7;4520:9:30;93073:5:25;92991:14;:88::i;:::-;93097:10;93236:25;93260:1;93236:20;:25;:::i;:::-;93212:49;;:21;:49;:::i;:::-;93279:5;92959:14;:339::i;:::-;10227:22;93498:21;93533:5;92931:14;:617::i;:::-;:627;;;;:::i;:::-;92744:814;-1:-1:-1;93625:16:25;93688:1;93652:32;-1:-1:-1;;;93662:9:25;93688:1;92744:814;93662:9;:::i;:::-;:21;;;;:::i;93652:32::-;93645:39;;:4;:39;:::i;:::-;93644:45;;;;:::i;:::-;93625:64;-1:-1:-1;93869:10:25;93882:83;93906:58;93921:13;93933:1;93625:64;93921:13;:::i;:::-;10344:21;-1:-1:-1;;;93958:5:25;93906:14;:58::i;93882:83::-;93869:96;-1:-1:-1;94005:16:25;;;;;:33;;94034:4;94005:33;;;;;8073:1;94005:33;93976:63;92515:1531;-1:-1:-1;;;;;;;;92515:1531:25:o;12871:334:29:-;13041:21;13078:6;13088:1;13078:11;13074:25;;-1:-1:-1;13098:1:29;13091:8;;13074:25;13125:73;13140:26;13168:6;13176:12;13190:7;13125:14;:73::i;11407:381::-;11577:21;11614:6;11624:1;11614:11;11610:25;;-1:-1:-1;11634:1:29;11627:8;;11610:25;11673:108;11688:64;11703:26;11731:6;-1:-1:-1;;;11744:7:29;11688:14;:64::i;:::-;11754:12;-1:-1:-1;;;11773:7:29;11673:14;:108::i;5473:602:26:-;5546:19;-1:-1:-1;;5581:15:26;;;;;;:34;;-1:-1:-1;5600:15:26;;;;1234:6;5600:15;5581:34;5577:64;;;5624:17;;-1:-1:-1;;;5624:17:26;;;;;;;;;;;5577:64;5651:21;;;;:14;5708:11;;;:32;;5733:7;5708:32;;;5722:8;5723:7;5722:8;:::i;:::-;5682:59;-1:-1:-1;5848:12:26;5859:1;5682:59;5848:12;:::i;:::-;;;5884:29;5905:7;5884:20;:29::i;:::-;5870:43;-1:-1:-1;5937:6:26;5927:16;;:21;5923:75;;5995:3;5965:25;:11;5979;5965:25;:::i;:::-;5964:34;;5950:48;;5923:75;6017:4;6013:8;;:1;:8;6009:59;;;6037:31;6057:11;-1:-1:-1;;6037:31:26;:::i;6081:2078::-;6164:19;6233:7;6243:3;6233:13;6250:1;6233:18;:93;;-1:-1:-1;;;6233:93:26;;;-1:-1:-1;;;6233:93:26;6219:107;;;-1:-1:-1;6354:3:26;6344:13;;:18;6340:95;;-1:-1:-1;;;6379:48:26;6432:3;6378:57;6340:95;6463:3;6453:13;;:18;6449:95;;-1:-1:-1;;;6488:48:26;6541:3;6487:57;6449:95;6572:3;6562:13;;:18;6558:95;;6611:34;6597:48;6650:3;6596:57;6558:95;6681:4;6671:14;;:19;6667:129;;6739:34;6725:48;6778:3;6724:57;6667:129;6823:4;6813:14;;:19;6809:129;;6881:34;6867:48;6920:3;6866:57;6809:129;6965:4;6955:14;;:19;6951:129;;7023:34;7009:48;7062:3;7008:57;6951:129;7107:4;7097:14;;:19;7093:129;;7165:34;7151:48;7204:3;7150:57;7093:129;7249:5;7239:15;;:20;7235:130;;7308:34;7294:48;7347:3;7293:57;7235:130;7392:5;7382:15;;:20;7378:130;;7451:34;7437:48;7490:3;7436:57;7378:130;7535:5;7525:15;;:20;7521:130;;7594:34;7580:48;7633:3;7579:57;7521:130;7678:5;7668:15;;:20;7664:129;;7737:33;7723:47;7775:3;7722:56;7664:129;7820:6;7810:16;;:21;7806:129;;7880:32;7866:46;7917:3;7865:55;7806:129;7962:6;7952:16;;:21;7948:93;;8004:29;7990:43;8038:3;7989:52;7948:93;8069:6;8059:16;;:21;8055:87;;8111:23;8097:37;8139:3;8096:46;6081:2078;;;:::o;12472:393:29:-;12674:23;12713:15;12732:1;12713:20;12709:34;;-1:-1:-1;12742:1:29;12735:8;;12709:34;12771:87;12786:15;12803:17;12822:26;12850:7;12771:14;:87::i;10973:428::-;11157:21;11194:15;11213:1;11194:20;11190:34;;-1:-1:-1;11223:1:29;11216:8;;11190:34;11250:144;11278:64;11293:15;-1:-1:-1;;;11315:17:29;11334:7;11278:14;:64::i;:::-;-1:-1:-1;;;11349:26:29;11377:7;11250:14;:144::i;18393:383::-;18527:7;18573:21;18550:19;:44;18546:103;;18617:21;;-1:-1:-1;;;18617:21:29;;;;;;;;;;;18546:103;18665:104;18678:43;18702:19;18678:21;:43;:::i;:::-;18724;18748:19;18724:21;:43;:::i;81159:592:25:-;81345:19;;81286:29;;81345:19;;;;;81378:23;;;81374:92;;;81424:1;81417:8;;;;;81374:92;81501:13;81476:269;81529:7;81516:9;:20;81476:269;;81624:15;81642:4;:10;;81653:9;81642:21;;;;;;;:::i;:::-;;;;:33;;:46;-1:-1:-1;;;;;81642:46:25;;-1:-1:-1;81702:32:25;81642:46;81702:32;;:::i;:::-;;;81551:194;81538:11;;;;;:::i;:::-;;;;81476:269;;;;81317:434;81159:592;;;;:::o;98076:1952::-;98320:34;;98493:35;98499:29;93:4:50;98493:35:25;:::i;:::-;98452:76;-1:-1:-1;98539:28:25;98570:58;98602:26;5211:7:30;98570:58:25;:::i;:::-;98539:89;;98639:38;98692:106;98707:30;98739:20;5211:7:30;98792:5:25;98692:14;:106::i;:::-;98639:159;;98809:31;5342:6:30;98854:30:25;:63;98850:421;;;-1:-1:-1;99088:1:25;98850:421;;;99197:63;99230:30;5342:6:30;99197:63:25;:::i;:::-;99171:89;;98850:421;99411:45;5046:1:30;99459:92:25;99518:23;99459:45;:92::i;:::-;:132;;;;:::i;:::-;99411:180;;99731:35;99781:97;99796:30;99828:37;93:4:50;99872:5:25;99781:14;:97::i;:::-;99731:147;;99929:92;99944:27;99973:17;99992:21;-1:-1:-1;;;;;99929:92:25;100015:5;99929:14;:92::i;14048:638:22:-;14236:7;14459:210;14485:91;14500:47;14526:10;14538:8;14500:25;:47::i;:::-;14549:14;93:4:50;14570:5:22;14485:14;:91::i;:::-;14614:41;14623:15;14640:14;14614:8;:41::i;:::-;-1:-1:-1;;;;;14594:61:22;14459:8;:210::i;1541:361:20:-;1695:15;1726:11;1741:1;1726:16;1722:105;;-1:-1:-1;1765:6:20;1758:13;;1722:105;1843:52;1850:6;1858:11;1871;1884:10;1843:6;:52::i;46655:833:25:-;46864:30;46896:52;47081:42;47112:4;47118;47081:30;:42::i;:::-;-1:-1:-1;;;;;47300:25:25;;;;;;:16;;;:25;;;;;:80;;:94;;47034:89;;-1:-1:-1;47209:272:25;;47381:12;;47300:94;;;;;;:::i;:::-;;;;;;;;;47237:44;:157;;;;:::i;:::-;47408:28;-1:-1:-1;;;47467:4:25;47209:14;:272::i;:::-;47184:297;;46655:833;;;;;;;;:::o;23718:3050::-;24081:40;;;;24036:42;24081:40;;;:24;;;:40;;;;;;;;;24036:85;;;;;;;;;-1:-1:-1;;;;;24036:85:25;;;;;-1:-1:-1;;;24036:85:25;;;;;;;;24241:42;-1:-1:-1;;;;;;;;;;;;;;;;;;;24241:42:25;24409:44;;;;;24371:35;;;;:82;-1:-1:-1;;;;;24317:136:25;;;:35;;;:136;;;24555:42;;24519:33;;:78;24467:130;;;-1:-1:-1;;24697:46:25;;:9;:46::i;:::-;24810:40;;;;;;;;:24;;;:40;;;;;;;:63;;;;;;-1:-1:-1;;;;;24810:63:25;;;-1:-1:-1;;;24810:63:25;;;;;;;;;24679:64;;-1:-1:-1;24888:40:25;24884:1829;;25054:65;25074:4;25080:20;25102:7;25111;25054:19;:65::i;:::-;25331:102;25362:4;25368:29;25399:7;25408;25417:12;25431:1;25331:30;:102::i;:::-;24884:1829;;;25464:7;25454;:17;25450:1263;;;25555:144;25610:4;25616:7;25625:20;25647;25669:7;25678;25555:37;:144::i;:::-;25843:256;25891:4;25913:29;25960:7;25985;26010:12;26040:45;26071:4;26077:7;26040:30;:45::i;:::-;25843:30;:256::i;25450:1263::-;26323:33;;26275:10;;;26286:7;26275:19;;;;;;;:::i;:::-;;;;:31;;:81;;:44;;:81;;;;-1:-1:-1;;;;;26275:81:25;;:::i;:::-;;;;;;;;-1:-1:-1;;;;;26275:81:25;;;;;-1:-1:-1;;;;;26275:81:25;;;;;;26420:20;:35;;;26370:4;:10;;26381:7;26370:19;;;;;;;:::i;:::-;;;;:31;;:46;;;:85;;;;;;;;;;-1:-1:-1;;;;;26370:85:25;;;;;:::i;:::-;;;;;;;;-1:-1:-1;;;;;26370:85:25;;;;;-1:-1:-1;;;;;26370:85:25;;;;;;26565:74;26581:4;26603:20;:33;;;-1:-1:-1;;;;;26595:42:25;26587:51;;26565:15;:74::i;20567:5181:89:-;20615:7;20733:1;20728;:6;20724:53;;-1:-1:-1;20761:1:89;20567:5181::o;20724:53::-;21717:1;21745;-1:-1:-1;;;21765:16:89;;21761:92;;21808:3;21801:10;;;;;21836:2;21829:9;21761:92;-1:-1:-1;;;21870:2:89;:15;21866:90;;21912:2;21905:9;;;;;21939:2;21932:9;21866:90;-1:-1:-1;;;21973:2:89;:15;21969:90;;22015:2;22008:9;;;;;22042:2;22035:9;21969:90;22083:7;22076:2;:15;22072:89;;22118:2;22111:9;;;;;22145:1;22138:8;22072:89;22185:6;22178:2;:14;22174:87;;22219:1;22212:8;;;;;22245:1;22238:8;22174:87;22285:6;22278:2;:14;22274:87;;22319:1;22312:8;;;;;22345:1;22338:8;22274:87;22385:6;22378:2;:14;22374:61;;22419:1;22412:8;22374:61;22861:1;:6;22872:1;22860:13;;;;;24771:1;22860:13;24771:6;;;;:::i;:::-;;24766:2;:11;24765:18;;24760:23;;24891:1;24884:2;24880:1;:6;;;;;:::i;:::-;;24875:2;:11;24874:18;;24869:23;;25002:1;24995:2;24991:1;:6;;;;;:::i;:::-;;24986:2;:11;24985:18;;24980:23;;25111:1;25104:2;25100:1;:6;;;;;:::i;:::-;;25095:2;:11;25094:18;;25089:23;;25221:1;25214:2;25210:1;:6;;;;;:::i;:::-;;25205:2;:11;25204:18;;25199:23;;25331:1;25324:2;25320:1;:6;;;;;:::i;:::-;;25315:2;:11;25314:18;;25309:23;;25703:28;25728:2;25724:1;:6;;;;;:::i;:::-;;25719:11;;;34795:145:90;25703:28:89;25698:33;;;20567:5181;-1:-1:-1;;;20567:5181:89:o;73662:2777:25:-;74039:16;;74194;;;;74175;;;;73803:34;;;;;;;;74039:16;74171:39;:1;:39;74145:23;;;:65;74228:17;;;74224:36;;;-1:-1:-1;74255:1:25;;;;-1:-1:-1;73662:2777:25;-1:-1:-1;;;;;73662:2777:25:o;74224:36::-;74489:13;74506:1;74489:18;74485:37;;-1:-1:-1;74517:1:25;;;;-1:-1:-1;73662:2777:25;-1:-1:-1;;;;;73662:2777:25:o;74485:37::-;74565:24;74599:33;74617:13;74599:9;:33::i;:::-;74844:19;;;;74669:30;;;;-1:-1:-1;74736:30:25;;;-1:-1:-1;74820:21:25;;-1:-1:-1;74820:21:25;;-1:-1:-1;74820:21:25;75071:24;;;:71;;75122:20;75071:71;;;75099:20;75098:21;;75071:71;75013:130;;75182:202;75244:1;75222:19;:23;:68;;75271:19;75222:68;;;75249:19;75248:20;;75222:68;-1:-1:-1;;;75334:13:25;75330:1;:17;75365:5;75182:14;:202::i;:::-;75157:227;;75424:70;75439:23;-1:-1:-1;;;75473:13:25;75469:1;:17;75488:5;75424:14;:70::i;:::-;75398:96;;74989:516;75519:5;:21;;;75544:1;75519:26;75515:918;;75565:5;:21;;;75590:1;75565:26;75561:390;;75690:22;75661:51;;75759:23;75730:52;;75515:918;;75561:390;75914:22;75885:51;;75515:918;;;76064:5;:21;;;76089:1;76064:26;76060:363;;76176:23;76147:52;;76060:363;;;76316:22;76287:51;;76385:23;76356:52;;76060:363;73875:2564;;;;;73662:2777;;;:::o;28199:4207::-;28504:24;28540:16;28566:17;28607:24;8838:3;28664:26;28660:1;:30;:89;;28722:27;28723:26;28722:27;:::i;:::-;28660:89;;;28693:26;28660:89;28659:131;;;;:::i;:::-;28607:197;;28926:1;28897:26;:30;:55;;;;-1:-1:-1;28931:21:25;;;28897:55;:85;;8166:1;28897:85;;;-1:-1:-1;;28897:85:25;28831:46;8838:3;28831:26;:46;:::i;:::-;:152;;;;:::i;:::-;28818:165;-1:-1:-1;29008:21:25;;:79;;29053:34;9662:2;29053:16;:34;:::i;:::-;29008:79;;;9716:1;29008:79;28997:90;;28593:505;29112:8;29124:1;29112:13;29108:399;;29296:74;29311:13;:28;;;-1:-1:-1;;;;;29296:74:25;-1:-1:-1;;;1513:21:30;29364:5:25;29296:14;:74::i;:::-;-1:-1:-1;;;;;29241:130:25;:28;;;:130;29422:30;29436:16;29422:30;;:::i;:::-;;;9716:1;29467:29;;29108:399;-1:-1:-1;;;;;29517:25:25;;;;;;:16;;;:25;;;;;:57;;-1:-1:-1;;29517:57:25;;;;;;;;;29653:2655;29664:28;;;;-1:-1:-1;;;;;29660:32:25;;29653:2655;;29803:10;9225:3;29789:24;:52;;;;-1:-1:-1;;29817:10:25;:24;29789:52;29843:5;29785:63;29923:74;30012:169;30066:4;30072:10;30084:13;30099:24;30125:7;30134:23;30159:8;30012:36;:169::i;:::-;30391:51;;;;:66;;;9716:1;;-1:-1:-1;29923:258:25;;-1:-1:-1;;;;;;30387:70:25;;30383:1167;;-1:-1:-1;;;;;30523:25:25;;;;;;:16;;;:25;;;;;;;;30593:250;;;;;;;;30648:51;;;;;:64;-1:-1:-1;;;;;30593:250:25;;;;;30754:51;;:66;;;30593:250;;;;;;;;30523:43;;;;:338;;;;;;;;;;;;;;;;;;-1:-1:-1;;;30523:338:25;;;;;;;;;;;;30951:25;;;;;;:80;;31058:68;30523:4;30648:33;31058:27;:68::i;:::-;30951:193;;;;;;;-1:-1:-1;30951:193:25;;;;;;;;;;31333:41;;;31311:19;;;;;;;:63;31307:229;;;31427:41;;;;31398:71;;;;;;;;-1:-1:-1;;31398:71:25;;;;;;:19;;-1:-1:-1;31307:229:25;31685:51;;;;;:66;;;;;31653:28;;;:98;;-1:-1:-1;;;;;31653:98:25;;;;;;;;;31799:51;;:64;31769:94;;;;;;32119:74;;-1:-1:-1;;;1513:21:30;-1:-1:-1;32119:14:25;:74::i;:::-;-1:-1:-1;;;;;32060:134:25;:28;;;:134;-1:-1:-1;32253:30:25;;29653:2655;;;-1:-1:-1;;;;;;;32360:25:25;;;;;;;:16;;;;:25;;-1:-1:-1;;32360:25:25;;;;:39;;-1:-1:-1;;32360:39:25;32395:4;32360:39;;;-1:-1:-1;28199:4207:25;;;-1:-1:-1;;28199:4207:25:o;65919:499::-;66059:22;66121:47;66139:4;:10;;66150:5;66139:17;;;;;;;:::i;:::-;;66157:9;66139:28;;;;;;;;:::i;:::-;;;;;;;;;9387:6;97310:22;;97199:140;66121:47;66172:1;66121:52;66117:213;;66197:5;66206:1;66197:10;66193:24;;-1:-1:-1;66216:1:25;66209:8;;66193:24;66242:73;66268:4;-1:-1:-1;;66274:9:25;;8548:2;66285:9;:29;;66242:73;66235:80;;;;66117:213;66350:51;66378:4;66384:5;66391:9;66350:27;:51::i;13019:1164:21:-;13218:18;;13157;;13218;;;;;13157;13277:62;13218:18;901:26;926:1;696;901:26;:::i;13277:62::-;13246:93;;13355:27;13354:28;:57;;;;-1:-1:-1;13386:25:21;;13354:57;13350:827;;;13442:13;;-1:-1:-1;;;13442:13:21;;;;;-1:-1:-1;13350:827:21;;;13600:552;13642:4;:26;;13669:20;13642:48;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;13712:27;:89;;13799:1;13712:89;;;13742:4;:26;;13769:20;13742:48;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;13712:89;13936:27;:172;;14080:25;;;:28;;;13936:172;;;13998:4;:25;;14024:20;13998:47;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;13936:172;13823:311;;13831:4;:25;;13857:20;13831:47;;;;;;;:::i;15518:221::-;15604:9;15615;15649:28;15669:1;15672;15675;15649:19;:28::i;:::-;15636:41;;-1:-1:-1;15636:41:21;-1:-1:-1;15700:32:21;15720:1;15636:41;;15700:19;:32::i;:::-;15687:45;;;;-1:-1:-1;15518:221:21;-1:-1:-1;;;;;15518:221:21:o;15292:614:22:-;15402:20;1395:6;15438:40;;15434:425;;15509:34;:17;1520:6;15509:26;:34::i;:::-;15494:49;;15434:425;;;1462:8;15564:39;;15560:299;;1676:7;15634:58;1567:4;15635:39;1395:6;15635:17;:39;:::i;:::-;15634:50;;:58::i;:::-;:85;;;;:::i;15560:299::-;1747:7;15765:57;1612:5;15766:38;1462:8;15766:17;:38;:::i;15765:57::-;:83;;;;:::i;:::-;15750:98;;15560:299;15868:31;5393:8:30;15868:31:22;;:::i;1311:319:50:-;1383:7;;1422:5;1426:1;1422;:5;:::i;:::-;1402:25;-1:-1:-1;1437:18:50;1458:41;1402:25;;1491:7;93:4;1491:1;:7;:::i;:::-;1458:10;:41::i;:::-;1437:62;-1:-1:-1;1509:17:50;1529:42;1437:62;1552:9;1563:7;93:4;1563:1;:7;:::i;1529:42::-;1509:62;-1:-1:-1;1509:62:50;1589:22;1601:10;1589:9;:22;:::i;:::-;:34;;;;:::i;43577:242:25:-;43694:41;43754:4;:10;;43772:4;43754:24;;;;;;;;;:::i;:::-;;;;:58;;;43747:65;;43577:242;;;;:::o;39159:1150::-;39378:34;;;;39415:1;39378:34;;;:18;;;:34;;;;;:38;;-1:-1:-1;;39378:38:25;;;39571:30;;39518:10;;;39378:38;39518:24;;;;;;;;;:::i;:::-;;;;:36;;:83;;-1:-1:-1;;;;;;39518:83:25;;-1:-1:-1;;;;;39518:83:25;;;;;;;;;;;;;;39670:32;;;;39615:10;;;:24;;;;;;;;;;:::i;:::-;;;;:36;;:87;;-1:-1:-1;;;;;39615:87:25;;;-1:-1:-1;;;39615:87:25;;;;;;;;;;;;;;;;;;-1:-1:-1;39615:51:25;39788:4;:24;39768:44;;39875:95;39892:4;:10;;39910:4;39892:24;;;;;;;;;:::i;:::-;;;;39942:25;;;39875:16;:95::i;:::-;39870:229;;39990:94;40019:4;8410:1;40045:9;8548:2;40056:4;:24;40082:1;39990:28;:94::i;:::-;40221:71;40237:4;40259:17;:30;;;-1:-1:-1;;;;;40251:39:25;40243:48;;40221:15;:71::i;48082:927::-;48571:30;48618:103;48640:4;48646:7;48655:29;:42;;;-1:-1:-1;;;;;48618:103:25;48699:7;48708:12;48618:21;:103::i;:::-;48570:151;;;48784:42;48803:22;48784:18;:42::i;:::-;-1:-1:-1;;;;;48732:25:25;;;;;;:16;;;:25;;;;;:94;;:48;;:94;;;;;;;-1:-1:-1;;;;;48732:94:25;;:::i;:::-;;;;;;;;-1:-1:-1;;;;;48732:94:25;;;;;-1:-1:-1;;;;;48732:94:25;;;;;;48945:57;48836:4;:16;;:25;48853:7;-1:-1:-1;;;;;48836:25:25;-1:-1:-1;;;;;48836:25:25;;;;;;;;;;;;:80;;48917:12;48836:94;;;;;;;;:::i;:::-;;;;;;;;;;:166;-1:-1:-1;;;;;;;48082:927:25:o;38302:536::-;38639:65;38659:4;38665:20;38687:7;38696;38639:19;:65::i;:::-;38769:62;38786:4;38792:20;38814:7;38823;38769:16;:62::i;:::-;38302:536;;;;;;:::o;42040:375::-;42191:22;;42149:83;;42176:54;;42218:12;;42191:22;;;-1:-1:-1;;;;;42191:22:25;42176:54;:::i;42149:83::-;42124:108;;-1:-1:-1;;;;;42124:108:25;;;;;;-1:-1:-1;;42124:108:25;;;;;;42272:136;42124:22;42351:16;-1:-1:-1;42351:45:25;;-1:-1:-1;;42351:45:25;;;8073:1;42351:45;42319:4;:27;;;42312:85;;;;:::i;42272:136::-;-1:-1:-1;;;;;42242:166:25;:4;:27;;:166;;;;42040:375;;:::o;33010:2544::-;33315:74;;:::i;:::-;33446:40;;;;33401:42;33446:40;;;:24;;;:40;;;;;;;;33401:85;;;;;;;;;-1:-1:-1;;;;;33401:85:25;;;;;-1:-1:-1;;;33401:85:25;;;;;;;;;;33674:28;;;;33401:85;;:42;33592:228;;33636:24;;33765:23;33802:8;33592:30;:228::i;:::-;33553:267;;33916:34;33996:13;:28;;;-1:-1:-1;;;;;33964:60:25;:28;-1:-1:-1;;;;;33964:60:25;;33960:700;;-1:-1:-1;34130:26:25;;33960:700;;;34452:197;34488:147;34524:28;-1:-1:-1;;;;;34488:147:25;34554:13;:26;;;-1:-1:-1;;;;;34488:147:25;34582:13;:28;;;-1:-1:-1;;;;;34488:147:25;34612:5;34488:14;:147::i;34452:197::-;34423:226;;33960:700;-1:-1:-1;;;;;;;;;;;;;;;;;34758:33:25;;:62;;34794:26;;34758:62;:::i;:::-;-1:-1:-1;;;;;34722:98:25;;;34868:35;;;;:66;;34906:28;;34868:66;:::i;:::-;-1:-1:-1;;;;;34830:104:25;:35;;;:104;;;35006:15;;35024:46;;:9;:46::i;:::-;35117:430;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;35356:150:25;;;;;;;;-1:-1:-1;;;;;35356:150:25;;;;;;;;;;;35117:430;;;;-1:-1:-1;;;;;;;;35117:430:25;;;;;-1:-1:-1;35117:430:25;;33010:2544;-1:-1:-1;;33010:2544:25:o;35830:1968::-;36044:14;;36113:27;;;;36187:24;;;;:37;36252:14;;;;;36330:34;;;;35975:7;36330:34;;;:18;;;:34;;;;;;;36408:24;;;:40;;;;;:63;;;;;;-1:-1:-1;;;;;36408:63:25;;;-1:-1:-1;;;36408:63:25;;;;;;;;;;35975:7;;36044:14;36113:27;36150:74;;;;;36330:34;;36527:12;;:127;;;;;36564:90;36581:4;:10;;36592:7;36581:19;;;;;;;:::i;:::-;;;;36626:25;;;36564:16;:90::i;:::-;36563:91;36527:127;36506:1213;;;36752:62;36769:4;36775:20;36797:7;36806;36752:16;:62::i;:::-;36506:1213;;;36849:7;36839;:17;36835:884;;;36947:159;37006:4;37012:7;37021:6;:27;;;37050:20;37072:7;37081;36947:37;:159::i;36835:884::-;37231:34;;;;;;;;:18;;;:34;;;;;:52;;-1:-1:-1;;37231:52:25;;;;;;;37416:24;;;;:37;37368:10;;;37231:52;37368:19;;;;;;;:::i;:::-;;;;:31;;:85;;-1:-1:-1;;;;;;37368:85:25;;-1:-1:-1;;;;;37368:85:25;;;;;;;;;;;;;;37521:24;;;;:39;;;37471:10;;;37482:7;37471:19;;;;;;;:::i;:::-;;;;:31;;:89;;-1:-1:-1;;;;;37471:89:25;;;-1:-1:-1;;;37471:89:25;;;;;;;;;;;;;;;;;;37647:57;37663:4;37676:26;37647:15;:57::i;:::-;37746:45;37777:4;37783:7;37746:30;:45::i;66796:581::-;66939:12;67082:1;67037:42;67050:4;:10;;67061:5;67050:17;;;;;;;:::i;:::-;;67068:9;67050:28;;;;;;;;:::i;:::-;;;;;;;;;669:66:49;362:18;356:25;;;479:6;;487:1;475:14;468:22;;;;539:10;536:17;-1:-1:-1;533:1:49;529:25;420:9;;418:1;414:16;524:31;;;;621:9;;;-1:-1:-1;;;617:36:49;612:3;608:46;603:133;597:140;;219:536;67037:42:25;67020:9;67032:1;67020:13;8548:2;66999:35;:80;:84;66987:96;;8410:1;67196:5;:27;67192:49;;-1:-1:-1;67232:9:25;67225:16;;67192:49;67305:55;67333:4;67339:5;67347:1;67339:9;67350;67305:27;:55::i;314:117:50:-;377:7;403:21;414:1;417;93:4;840:120;916:7;952:1;943:5;947:1;943;:5;:::i;1322:500:28:-;1386:19;1422:17;1429:4;1435:3;1422:6;:17::i;:::-;1417:35;;-1:-1:-1;1448:4:28;1441:11;;1417:35;1462:11;1490:1;1476:11;1482:4;2022:12;;:19;;1920:129;1476:11;:15;;;;:::i;:::-;1523:21;;;;1501:19;1523:21;;;;;;;;;;;1462:29;;-1:-1:-1;1523:21:28;;;;1558:20;;;;1554:196;;1594:16;1613:4;:12;;1626:4;1613:18;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1645:27;;;;;;;;;;;:42;;-1:-1:-1;;1645:42:28;;;;;;;;;-1:-1:-1;1701:12:28;;:26;;1613:18;;-1:-1:-1;1613:18:28;;1701:12;;:26;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;:38;;;;;;;;;;;;;;;;;;1580:170;1554:196;1766:21;;;:16;:21;;;;;;;;;;1759:28;;-1:-1:-1;;1759:28:28;;;;1797:12;;:18;;;;;;;:::i;:::-;;;;;;;;;-1:-1:-1;;1797:18:28;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;1322:500:28;;;;:::o;63870:1618:25:-;64190:21;:36;;;64159:28;64263:10;;;64274:5;64263:17;;;;;;;:::i;:::-;;64281:9;64263:28;;;;;;;;:::i;:::-;;;;;;;;;;;;-1:-1:-1;96565:1:25;:11;;96646:15;;96639:23;96632:31;64666:17;;;64662:30;;64685:7;;;;;64662:30;97008:1;:11;;97039:13;;64775:75;;64958:11;64927:4;:10;;64938:5;64927:17;;;;;;;:::i;:::-;;64945:9;64927:28;;;;;;;;:::i;:::-;;;;;;;;:42;;;;65038:5;65047:1;65038:10;65034:23;;65050:7;;;;;65034:23;65075:3;65082:1;65075:8;65071:185;;9387:6;97310:22;;65198:35;65194:48;;65235:7;;;;;65194:48;65329:142;65375:4;-1:-1:-1;;65381:9:25;;8548:2;65392:9;:29;8548:2;65423:9;:29;65454:3;65329:28;:142::i;:::-;64061:1421;;;63870:1618;;;;;:::o;40628:1163::-;40872:34;;;;;;;;:18;;;:34;;;;;:49;;-1:-1:-1;;40872:49:25;;;;;;;;;;41056:30;;;41003:10;;;;:24;;;;;;;:::i;:::-;;;;:36;;:83;;-1:-1:-1;;;;;;41003:83:25;;-1:-1:-1;;;;;41003:83:25;;;;;;;;;;;;;;41155:32;;;;41100:10;;;:24;;;;;;;;;;:::i;:::-;;;;:36;;:87;;-1:-1:-1;;;;;41100:87:25;;;-1:-1:-1;;;41100:87:25;;;;;;;;;;;;;;;;;;-1:-1:-1;41100:51:25;41273:4;:24;41253:44;;41358:95;41375:4;:10;;41393:4;41375:24;;;;;;;;;:::i;:::-;;;;41425:25;;;41358:16;:95::i;:::-;41353:229;;41473:94;41502:4;8410:1;41528:9;8548:2;41539:4;:24;41565:1;41473:28;:94::i;:::-;41704:70;41720:4;41741:17;:30;;;-1:-1:-1;;;;;41733:39:25;41704:15;:70::i;84508:1148::-;84771:34;84844:78;84859:24;84885:23;4401:3:30;84916:5:25;84844:14;:78::i;:::-;84817:105;;84985:32;85060:24;85031:26;-1:-1:-1;;;;;85031:53:25;;85027:164;;;85127:53;-1:-1:-1;;;;;85127:53:25;;:24;:53;:::i;:::-;85100:80;;85027:164;85543:95;85552:23;-1:-1:-1;;;;;85543:95:25;85577:60;85592:24;85618:8;85628:1;85631:5;85577:14;:60::i;:::-;85543:8;:95::i;2258:193:28:-;2347:12;;;:19;2327:4;;2347:24;;2343:42;;-1:-1:-1;2380:5:28;2373:12;;2343:42;2402;;;;2415:16;:21;;;;;;;;;;;2402:12;;;:35;;:42;;:12;;2415:21;;;2402:35;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;:42;;2258:193;-1:-1:-1;;;2258:193:28:o;835:235::-;899:21;936:17;943:4;949:3;936:6;:17::i;:::-;932:34;;;-1:-1:-1;962:4:28;955:11;;932:34;976:12;;;;:22;;;;;;;-1:-1:-1;976:22:28;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1039:19;1032:31;;976:12;1032:31;:::i;:::-;1008:21;;;;:16;:21;;;;;;;;;;;;:55;;-1:-1:-1;;1008:55:28;;;;;;;;;;;835:235;:::o;-1:-1:-1:-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;:::i;:::-;;;;:::o;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::o;:::-;;;;;;;;;;;;;;;;;;;;;;;;:::o;:::-;;;;;;;;;;;;;;;;;;;;;;;;:::o;:::-;;;;;;;;;;;;;;;14:163:190;81:20;;141:10;130:22;;120:33;;110:61;;167:1;164;157:12;182:417;255:6;263;271;324:2;312:9;303:7;299:23;295:32;292:52;;;340:1;337;330:12;292:52;379:9;366:23;432:5;429:1;418:20;411:5;408:31;398:59;;453:1;450;443:12;398:59;476:5;-1:-1:-1;500:37:190;533:2;518:18;;500:37;:::i;:::-;490:47;;556:37;589:2;578:9;574:18;556:37;:::i;:::-;546:47;;182:417;;;;;:::o;964:173::-;1032:20;;-1:-1:-1;;;;;1081:31:190;;1071:42;;1061:70;;1127:1;1124;1117:12;1142:160;1207:20;;1263:13;;1256:21;1246:32;;1236:60;;1292:1;1289;1282:12;1307:328;1381:6;1389;1397;1450:2;1438:9;1429:7;1425:23;1421:32;1418:52;;;1466:1;1463;1456:12;1418:52;1489:29;1508:9;1489:29;:::i;:::-;1479:39;;1537:35;1568:2;1557:9;1553:18;1537:35;:::i;:::-;1527:45;;1591:38;1625:2;1614:9;1610:18;1591:38;:::i;1978:420::-;2031:3;2069:5;2063:12;2096:6;2091:3;2084:19;2128:4;2123:3;2119:14;2112:21;;2167:4;2160:5;2156:16;2190:1;2200:173;2214:6;2211:1;2208:13;2200:173;;;2275:13;;2263:26;;2318:4;2309:14;;;;2346:17;;;;2236:1;2229:9;2200:173;;;-1:-1:-1;2389:3:190;;1978:420;-1:-1:-1;;;;1978:420:190:o;2403:1152::-;2582:2;2571:9;2564:21;2545:4;2623:3;2612:9;2608:19;2683:6;2677:13;2670:21;2663:29;2658:2;2647:9;2643:18;2636:57;2761:2;2753:6;2749:15;2743:22;2740:1;2729:37;2724:2;2713:9;2709:18;2702:65;-1:-1:-1;;;;;2825:2:190;2817:6;2813:15;2807:22;2803:59;2798:2;2787:9;2783:18;2776:87;2910:2;2902:6;2898:15;2892:22;2951:4;2945:3;2934:9;2930:19;2923:33;2976:6;3011:12;3005:19;3048:6;3040;3033:22;3086:3;3075:9;3071:19;3064:26;;3131:2;3117:12;3113:21;3099:35;;3152:1;3143:10;;3162:195;3176:6;3173:1;3170:13;3162:195;;;3225:52;3273:3;3264:6;3258:13;1820:12;;-1:-1:-1;;;;;1816:53:190;;;1804:66;;1923:4;1912:16;;;1906:23;1902:64;1886:14;;1879:88;1736:237;3225:52;3306:2;3301:3;3297:12;3290:19;;3344:2;3336:6;3332:15;3322:25;;3198:1;3195;3191:9;3186:14;;3162:195;;;-1:-1:-1;3406:3:190;3394:16;;3388:23;3453:19;;;-1:-1:-1;;3449:33:190;3442:4;3427:20;;3420:63;3388:23;-1:-1:-1;3500:49:190;3457:3;3388:23;3500:49;:::i;3560:160::-;3626:20;;3686:1;3675:20;;;3665:31;;3655:59;;3710:1;3707;3700:12;3725:182;3782:6;3835:2;3823:9;3814:7;3810:23;3806:32;3803:52;;;3851:1;3848;3841:12;3803:52;3874:27;3891:9;3874:27;:::i;3912:348::-;3979:6;3987;4040:2;4028:9;4019:7;4015:23;4011:32;4008:52;;;4056:1;4053;4046:12;4008:52;4079:29;4098:9;4079:29;:::i;:::-;4069:39;;4158:2;4147:9;4143:18;4130:32;4202:8;4195:5;4191:20;4184:5;4181:31;4171:59;;4226:1;4223;4216:12;4171:59;4249:5;4239:15;;;3912:348;;;;;:::o;4265:127::-;4326:10;4321:3;4317:20;4314:1;4307:31;4357:4;4354:1;4347:15;4381:4;4378:1;4371:15;4397:253;4469:2;4463:9;4511:4;4499:17;;4546:18;4531:34;;4567:22;;;4528:62;4525:88;;;4593:18;;:::i;:::-;4629:2;4622:22;4397:253;:::o;4655:247::-;4722:2;4716:9;4764:3;4752:16;;4798:18;4783:34;;4819:22;;;4780:62;4777:88;;;4845:18;;:::i;4907:1371::-;4965:5;5013:6;5001:9;4996:3;4992:19;4988:32;4985:52;;;5033:1;5030;5023:12;4985:52;5055:22;;:::i;:::-;5046:31;;5122:3;5115:4;5104:9;5100:20;5096:30;5086:58;;5140:1;5137;5130:12;5086:58;5164:17;;:::i;:::-;5203:3;5244;5233:9;5229:19;5271:3;5263:6;5260:15;5257:35;;;5288:1;5285;5278:12;5257:35;5312:9;5330:206;5346:6;5341:3;5338:15;5330:206;;;5442:17;;5472:20;;5521:4;5512:14;;;;5363;5330:206;;;-1:-1:-1;5545:20:190;;;5610;5657:4;5646:16;;5639:33;-1:-1:-1;5745:4:190;5730:20;;5717:34;5778:4;5767:16;;5760:33;5866:3;5851:19;;5838:33;5898:4;5887:16;;5880:33;5986:3;5971:19;;5958:33;6018:4;6007:16;;6000:33;6106:3;6091:19;;6078:33;6138:4;6127:16;;6120:33;6226:3;6211:19;;;6198:33;6258:3;6247:15;;6240:32;5552:5;4907:1371;-1:-1:-1;4907:1371:190:o;6283:623::-;6408:6;6416;6424;6432;6440;6493:3;6481:9;6472:7;6468:23;6464:33;6461:53;;;6510:1;6507;6500:12;6461:53;6533:49;6574:7;6563:9;6533:49;:::i;:::-;6523:59;-1:-1:-1;6651:3:190;6636:19;;6623:33;;-1:-1:-1;6753:3:190;6738:19;;6725:33;;-1:-1:-1;6803:39:190;6837:3;6822:19;;6803:39;:::i;:::-;6793:49;;6861:39;6895:3;6884:9;6880:19;6861:39;:::i;:::-;6851:49;;6283:623;;;;;;;;:::o;7164:1455::-;7319:6;7327;7335;7343;7387:9;7378:7;7374:23;7417:3;7413:2;7409:12;7406:32;;;7434:1;7431;7424:12;7406:32;7457:49;7498:7;7487:9;7457:49;:::i;:::-;7447:59;;7525:39;7559:3;7548:9;7544:19;7525:39;:::i;:::-;7515:49;-1:-1:-1;7599:4:190;-1:-1:-1;;7580:17:190;;7576:28;7573:48;;;7617:1;7614;7607:12;7573:48;;7643:22;;:::i;:::-;7738:3;7723:19;;7710:33;7752:22;;7847:3;7832:19;;7819:33;7879:2;7868:14;;7861:31;7965:3;7950:19;;7937:33;7997:2;7986:14;;7979:31;8083:3;8068:19;;8055:33;8115:2;8104:14;;8097:31;8201:3;8186:19;;8173:33;8233:3;8222:15;;8215:32;8320:3;8305:19;;8292:33;8352:3;8341:15;;8334:32;8439:3;8424:19;;8411:33;8471:3;8460:15;;8453:32;7164:1455;;;;-1:-1:-1;7759:5:190;;8582:3;8567:19;8554:33;;-1:-1:-1;;7164:1455:190:o;8816:256::-;8882:6;8890;8943:2;8931:9;8922:7;8918:23;8914:32;8911:52;;;8959:1;8956;8949:12;8911:52;8982:28;9000:9;8982:28;:::i;:::-;8972:38;;9029:37;9062:2;9051:9;9047:18;9029:37;:::i;9077:300::-;9145:6;9153;9206:2;9194:9;9185:7;9181:23;9177:32;9174:52;;;9222:1;9219;9212:12;9174:52;9245:29;9264:9;9245:29;:::i;:::-;9235:39;9343:2;9328:18;;;;9315:32;;-1:-1:-1;;;9077:300:190:o;9382:180::-;9438:6;9491:2;9479:9;9470:7;9466:23;9462:32;9459:52;;;9507:1;9504;9497:12;9459:52;9530:26;9546:9;9530:26;:::i;9760:368::-;9834:6;9842;9850;9903:2;9891:9;9882:7;9878:23;9874:32;9871:52;;;9919:1;9916;9909:12;9871:52;9942:29;9961:9;9942:29;:::i;:::-;9932:39;;9990:35;10021:2;10010:9;10006:18;9990:35;:::i;:::-;9760:368;;9980:45;;-1:-1:-1;;;10094:2:190;10079:18;;;;10066:32;;9760:368::o;10133:863::-;10393:4;10441:3;10430:9;10426:19;10454:51;10495:9;10487:6;1820:12;;-1:-1:-1;;;;;1816:53:190;;;1804:66;;1923:4;1912:16;;;1906:23;1902:64;1886:14;;1879:88;1736:237;10454:51;10536:2;10521:18;;10514:34;;;10584:3;10579:2;10564:18;;10557:31;10637:13;;10659:22;;;;10751:4;10739:17;;;10712:3;10697:19;;;-1:-1:-1;10784:186:190;10798:6;10795:1;10792:13;10784:186;;;10863:13;;10878:6;10859:26;10847:39;;10915:4;10943:17;;;;10906:14;;;;10820:1;10813:9;10784:186;;;-1:-1:-1;10987:3:190;;10133:863;-1:-1:-1;;;;;;;10133:863:190:o;11001:783::-;11105:6;11113;11121;11129;11137;11145;11198:3;11186:9;11177:7;11173:23;11169:33;11166:53;;;11215:1;11212;11205:12;11166:53;11238:29;11257:9;11238:29;:::i;:::-;11228:39;11336:2;11321:18;;11308:32;;-1:-1:-1;11437:2:190;11422:18;;11409:32;;11540:2;11525:18;;11512:32;;-1:-1:-1;11643:3:190;11628:19;;11615:33;;-1:-1:-1;11747:3:190;11732:19;11719:33;;-1:-1:-1;11001:783:190;-1:-1:-1;;;11001:783:190:o;12116:324::-;12188:6;12196;12204;12257:2;12245:9;12236:7;12232:23;12228:32;12225:52;;;12273:1;12270;12263:12;12225:52;12296:29;12315:9;12296:29;:::i;:::-;12286:39;;12344:35;12375:2;12364:9;12360:18;12344:35;:::i;:::-;12334:45;;12398:36;12430:2;12419:9;12415:18;12398:36;:::i;12445:349::-;12711:6;12699:19;;12681:38;;12669:2;12654:18;;12728:60;12784:2;12769:18;;12761:6;1820:12;;-1:-1:-1;;;;;1816:53:190;;;1804:66;;1923:4;1912:16;;;1906:23;1902:64;1886:14;;1879:88;1736:237;13091:254;13156:6;13164;13217:2;13205:9;13196:7;13192:23;13188:32;13185:52;;;13233:1;13230;13223:12;13185:52;13256:27;13273:9;13256:27;:::i;13558:186::-;13617:6;13670:2;13658:9;13649:7;13645:23;13641:32;13638:52;;;13686:1;13683;13676:12;13638:52;13709:29;13728:9;13709:29;:::i;13925:327::-;14027:5;14050:1;14060:186;14074:4;14071:1;14068:11;14060:186;;;14147:13;;14144:1;14133:28;14121:41;;14191:4;14182:14;;;;14219:17;;;;14094:1;14087:9;14060:186;;14257:316;14348:5;14371:1;14381:186;14395:4;14392:1;14389:11;14381:186;;;14468:13;;14465:1;14454:28;14442:41;;14512:4;14503:14;;;;14540:17;;;;14415:1;14408:9;14381:186;;14578:330;14681:5;14704:1;14714:188;14728:4;14725:1;14722:11;14714:188;;;14791:13;;14806:10;14787:30;14775:43;;14847:4;14838:14;;;;14875:17;;;;14748:1;14741:9;14714:188;;14913:319;15005:5;15028:1;15038:188;15052:4;15049:1;15046:11;15038:188;;;15115:13;;15130:10;15111:30;15099:43;;15171:4;15162:14;;;;15199:17;;;;15072:1;15065:9;15038:188;;15237:1740;15461:13;;1710;1703:21;1691:34;;15431:4;15416:20;;15533:4;15525:6;15521:17;15515:24;15548:51;15593:4;15582:9;15578:20;15564:12;1710:13;1703:21;1691:34;;1640:91;15548:51;;15648:4;15640:6;15636:17;15630:24;15663:54;15711:4;15700:9;15696:20;15680:14;13816:4;13805:16;13793:29;;13749:75;15663:54;;15766:4;15758:6;15754:17;15748:24;15781:54;15829:4;15818:9;15814:20;15798:14;13816:4;13805:16;13793:29;;13749:75;15781:54;;15884:4;15876:6;15872:17;15866:24;15899:54;15947:4;15936:9;15932:20;15916:14;679:1;668:20;656:33;;604:91;15899:54;;16002:4;15994:6;15990:17;15984:24;16017:54;16065:4;16054:9;16050:20;16034:14;679:1;668:20;656:33;;604:91;16017:54;;16120:4;16112:6;16108:17;16102:24;16135:55;16184:4;16173:9;16169:20;16153:14;12875:8;12864:20;12852:33;;12799:92;16135:55;;16239:4;16231:6;16227:17;16221:24;16254:55;16303:4;16292:9;16288:20;16272:14;12875:8;12864:20;12852:33;;12799:92;16254:55;;16358:6;16350;16346:19;16340:26;16375:56;16423:6;16412:9;16408:22;16392:14;13904:1;13893:20;13881:33;;13829:91;16375:56;;16480:6;16472;16468:19;16462:26;16497:73;16562:6;16551:9;16547:22;16531:14;16497:73;:::i;:::-;;16619:6;16611;16607:19;16601:26;16636:62;16690:6;16679:9;16675:22;16659:14;16636:62;:::i;:::-;;16748:6;16740;16736:19;16730:26;16765:75;16832:6;16821:9;16817:22;16800:15;16765:75;:::i;:::-;;16890:6;16882;16878:19;16872:26;16907:64;16963:6;16952:9;16948:22;16931:15;16907:64;:::i;:::-;;15237:1740;;;;:::o;16982:254::-;17047:6;17055;17108:2;17096:9;17087:7;17083:23;17079:32;17076:52;;;17124:1;17121;17114:12;17076:52;17147:29;17166:9;17147:29;:::i;:::-;17137:39;;17195:35;17226:2;17215:9;17211:18;17195:35;:::i;17546:312::-;17644:6;17652;17705:3;17693:9;17684:7;17680:23;17676:33;17673:53;;;17722:1;17719;17712:12;17673:53;17745:49;17786:7;17775:9;17745:49;:::i;:::-;17735:59;;17813:39;17847:3;17836:9;17832:19;17813:39;:::i;17863:324::-;17935:6;17943;17951;18004:2;17992:9;17983:7;17979:23;17975:32;17972:52;;;18020:1;18017;18010:12;17972:52;18043:29;18062:9;18043:29;:::i;:::-;18033:39;;18091:36;18123:2;18112:9;18108:18;18091:36;:::i;:::-;18081:46;;18146:35;18177:2;18166:9;18162:18;18146:35;:::i;18467:127::-;18528:10;18523:3;18519:20;18516:1;18509:31;18559:4;18556:1;18549:15;18583:4;18580:1;18573:15;18599:127;18660:10;18655:3;18651:20;18648:1;18641:31;18691:4;18688:1;18681:15;18715:4;18712:1;18705:15;18731:158;18824:6;18817:14;;;18801;;;18797:35;;18844:16;;18841:42;;;18863:18;;:::i;18894:168::-;18967:9;;;18998;;19015:15;;;19009:22;;18995:37;18985:71;;19036:18;;:::i;19067:243::-;-1:-1:-1;;;;;19182:42:190;;;19138;;;19134:91;;19237:44;;19234:70;;;19284:18;;:::i;19315:125::-;19380:9;;;19401:10;;;19398:36;;;19414:18;;:::i;19445:135::-;19484:3;19505:17;;;19502:43;;19525:18;;:::i;:::-;-1:-1:-1;19572:1:190;19561:13;;19445:135::o;19585:128::-;19652:9;;;19673:11;;;19670:37;;;19687:18;;:::i;19718:375::-;19806:1;19824:5;19838:249;19859:1;19849:8;19846:15;19838:249;;;19909:4;19904:3;19900:14;19894:4;19891:24;19888:50;;;19918:18;;:::i;:::-;19968:1;19958:8;19954:16;19951:49;;;19982:16;;;;19951:49;20065:1;20061:16;;;;;20021:15;;19838:249;;20098:902;20147:5;20177:8;20167:80;;-1:-1:-1;20218:1:190;20232:5;;20167:80;20266:4;20256:76;;-1:-1:-1;20303:1:190;20317:5;;20256:76;20348:4;20366:1;20361:59;;;;20434:1;20429:174;;;;20341:262;;20361:59;20391:1;20382:10;;20405:5;;;20429:174;20466:3;20456:8;20453:17;20450:43;;;20473:18;;:::i;:::-;-1:-1:-1;;20529:1:190;20515:16;;20588:5;;20341:262;;20687:2;20677:8;20674:16;20668:3;20662:4;20659:13;20655:36;20649:2;20639:8;20636:16;20631:2;20625:4;20622:12;20618:35;20615:77;20612:203;;;-1:-1:-1;20724:19:190;;;20800:5;;20612:203;20847:42;-1:-1:-1;;20872:8:190;20866:4;20847:42;:::i;:::-;20925:6;20921:1;20917:6;20913:19;20904:7;20901:32;20898:58;;;20936:18;;:::i;21005:140::-;21063:5;21092:47;21133:4;21123:8;21119:19;21113:4;21092:47;:::i;21150:127::-;21211:10;21206:3;21202:20;21199:1;21192:31;21242:4;21239:1;21232:15;21266:4;21263:1;21256:15;21282:120;21322:1;21348;21338:35;;21353:18;;:::i;:::-;-1:-1:-1;21387:9:190;;21282:120::o;21407:237::-;21479:9;;;21446:7;21504:9;;-1:-1:-1;;;21515:18:190;;21500:34;21497:60;;;21537:18;;:::i;:::-;21610:1;21601:7;21596:16;21593:1;21590:23;21586:1;21579:9;21576:38;21566:72;;21618:18;;:::i;21649:216::-;21713:9;;;21741:11;;;21688:3;21771:9;;21799:10;;21795:19;;21824:10;;21816:19;;21792:44;21789:70;;;21839:18;;:::i;22142:155::-;22233:6;22210:14;;;22226;;;22206:35;;22253:15;;22250:41;;;22271:18;;:::i;22302:228::-;-1:-1:-1;;;;;22371:38:190;;;22411;;;22367:83;;22462:39;;22459:65;;;22504:18;;:::i;22535:151::-;22625:4;22618:12;;;22604;;;22600:31;;22643:14;;22640:40;;;22660:18;;:::i;22691:185::-;22787:1;22758:16;;;22776;;;;22754:39;22839:5;22808:16;;-1:-1:-1;;22826:20:190;;22805:42;22802:68;;;22850:18;;:::i;23153:136::-;23188:3;-1:-1:-1;;;23209:22:190;;23206:48;;23234:18;;:::i;:::-;-1:-1:-1;23274:1:190;23270:13;;23153:136::o;23294:240::-;-1:-1:-1;;;;;23363:42:190;;;23407;;;23359:91;;23462:43;;23459:69;;;23508:18;;:::i;23539:112::-;23570:1;23596;23586:35;;23601:18;;:::i;:::-;-1:-1:-1;23635:10:190;;23539:112::o;23656:193::-;23695:1;23721;23711:35;;23726:18;;:::i;:::-;-1:-1:-1;;;23762:18:190;;-1:-1:-1;;23782:13:190;;23758:38;23755:64;;;23799:18;;:::i;:::-;-1:-1:-1;23833:10:190;;23656:193::o;23854:127::-;23915:10;23910:3;23906:20;23903:1;23896:31;23946:4;23943:1;23936:15;23970:4;23967:1;23960:15", "linkReferences": {}, "immutableReferences": {"3142": [{"start": 813, "length": 32}, {"start": 1562, "length": 32}], "3144": [{"start": 727, "length": 32}, {"start": 1595, "length": 32}]}}, "methodIdentifiers": {"accruePenalties(address,uint256,uint256,uint256,uint256,uint256)": "5cb5fb0a", "boundTick(int16)": "beca5a67", "calcSatChangeRatioBips((uint256[6],uint256,uint256,uint256,uint256,uint256,uint256),uint256,uint256,address,address)": "13ed39d9", "configLongTermInterval(address,uint24)": "08c91120", "getAccount(address,bool,address)": "01cbfeb2", "getLeafDetails(address,bool,uint256)": "4d003c0e", "getLendingStateTick(int56,uint32,uint32)": "00ba55ec", "getLendingStateTickAndCheckpoint(uint32,uint32)": "15f0cc2f", "getObservations(address)": "8ecc5eda", "getObservedMidTermTick(bool)": "3d011d65", "getTickRange(address,int16,bool)": "ecabd7aa", "getTrancheDetails(address,bool,int16)": "668243a7", "getTreeDetails(address,bool)": "b0016809", "init(int16)": "********", "liquidationCheckHardPremiums((uint256[6],uint256,uint256,uint256,uint256,uint256,uint256),address,(uint256,uint256,uint256,uint256,uint256,uint256,uint256),uint256)": "159e7cd7", "longTermIntervalConfig()": "6c8c3191", "midTermIntervalConfig()": "88205b40", "owner()": "8da5cb5b", "recordObservation(int16,uint32)": "6ef9c968", "renounceOwnership()": "715018a6", "setNewPositionSaturation(address,uint256)": "1b060ac7", "transferOwnership(address)": "f2fde38b", "update((uint256[6],uint256,uint256,uint256,uint256,uint256,uint256),address)": "df31e2cc"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"uint24\",\"name\":\"_midTermIntervalConfig\",\"type\":\"uint24\"},{\"internalType\":\"uint24\",\"name\":\"_longTermIntervalConfig\",\"type\":\"uint24\"}],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"inputs\":[],\"name\":\"AmmalgamMaxSlippage\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"CannotUpdateZeroAddress\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidIntervalConfig\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidUserConfiguration\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"LiquidationPremiumTooHigh\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"MaxTrancheOverSaturated\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"}],\"name\":\"OwnableInvalidOwner\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"OwnableUnauthorizedAccount\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"PairAlreadyExists\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"PairDoesNotExist\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"PriceOutOfBounds\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"uint8\",\"name\":\"bits\",\"type\":\"uint8\"},{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"SafeCastOverflowedUintDowncast\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"TickOutOfBounds\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"previousOwner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"OwnershipTransferred\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"int16\",\"name\":\"lendingStateTick\",\"type\":\"int16\"}],\"name\":\"UpdateLendingTick\",\"type\":\"event\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"externalLiquidity\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"duration\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"allAssetsDepositL\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"allAssetsBorrowL\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"allSharesBorrowL\",\"type\":\"uint256\"}],\"name\":\"accruePenalties\",\"outputs\":[{\"internalType\":\"uint112\",\"name\":\"penaltyInBorrowLShares\",\"type\":\"uint112\"},{\"internalType\":\"uint112\",\"name\":\"accountPenaltyInBorrowLShares\",\"type\":\"uint112\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"int16\",\"name\":\"newTick\",\"type\":\"int16\"}],\"name\":\"boundTick\",\"outputs\":[{\"internalType\":\"int16\",\"name\":\"\",\"type\":\"int16\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"components\":[{\"internalType\":\"uint256[6]\",\"name\":\"userAssets\",\"type\":\"uint256[6]\"},{\"internalType\":\"uint256\",\"name\":\"sqrtPriceMinInQ72\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"sqrtPriceMaxInQ72\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"activeLiquidityScalerInQ72\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"activeLiquidityAssets\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"reservesXAssets\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"reservesYAssets\",\"type\":\"uint256\"}],\"internalType\":\"struct Validation.InputParams\",\"name\":\"inputParams\",\"type\":\"tuple\"},{\"internalType\":\"uint256\",\"name\":\"liqSqrtPriceInXInQ72\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"liqSqrtPriceInYInQ72\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"pairAddress\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"calcSatChangeRatioBips\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"ratioNetXBips\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"ratioNetYBips\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"pairAddress\",\"type\":\"address\"},{\"internalType\":\"uint24\",\"name\":\"_longTermIntervalConfig\",\"type\":\"uint24\"}],\"name\":\"configLongTermInterval\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"pairAddress\",\"type\":\"address\"},{\"internalType\":\"bool\",\"name\":\"netDebtX\",\"type\":\"bool\"},{\"internalType\":\"address\",\"name\":\"accountAddress\",\"type\":\"address\"}],\"name\":\"getAccount\",\"outputs\":[{\"components\":[{\"internalType\":\"bool\",\"name\":\"exists\",\"type\":\"bool\"},{\"internalType\":\"int16\",\"name\":\"lastTranche\",\"type\":\"int16\"},{\"internalType\":\"uint112\",\"name\":\"penaltyInBorrowLShares\",\"type\":\"uint112\"},{\"components\":[{\"internalType\":\"uint128\",\"name\":\"satInLAssets\",\"type\":\"uint128\"},{\"internalType\":\"uint128\",\"name\":\"satRelativeToL\",\"type\":\"uint128\"}],\"internalType\":\"struct Saturation.SaturationPair[]\",\"name\":\"satPairPerTranche\",\"type\":\"tuple[]\"},{\"internalType\":\"uint256[]\",\"name\":\"treePenaltyAtOnsetInBorrowLSharesPerSatInQ72PerTranche\",\"type\":\"uint256[]\"}],\"internalType\":\"struct Saturation.Account\",\"name\":\"\",\"type\":\"tuple\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"pairAddress\",\"type\":\"address\"},{\"internalType\":\"bool\",\"name\":\"netDebtX\",\"type\":\"bool\"},{\"internalType\":\"uint256\",\"name\":\"leafIndex\",\"type\":\"uint256\"}],\"name\":\"getLeafDetails\",\"outputs\":[{\"components\":[{\"internalType\":\"uint128\",\"name\":\"satInLAssets\",\"type\":\"uint128\"},{\"internalType\":\"uint128\",\"name\":\"satRelativeToL\",\"type\":\"uint128\"}],\"internalType\":\"struct Saturation.SaturationPair\",\"name\":\"saturation\",\"type\":\"tuple\"},{\"internalType\":\"uint256\",\"name\":\"currentPenaltyInBorrowLSharesPerSatInQ72\",\"type\":\"uint256\"},{\"internalType\":\"uint16[]\",\"name\":\"tranches\",\"type\":\"uint16[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"int56\",\"name\":\"newTick\",\"type\":\"int56\"},{\"internalType\":\"uint32\",\"name\":\"timeElapsedSinceUpdate\",\"type\":\"uint32\"},{\"internalType\":\"uint32\",\"name\":\"timeElapsedSinceLendingUpdate\",\"type\":\"uint32\"}],\"name\":\"getLendingStateTick\",\"outputs\":[{\"internalType\":\"int16\",\"name\":\"lendingStateTick\",\"type\":\"int16\"},{\"internalType\":\"uint256\",\"name\":\"maxSatInWads\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint32\",\"name\":\"timeElapsedSinceUpdate\",\"type\":\"uint32\"},{\"internalType\":\"uint32\",\"name\":\"timeElapsedSinceLendingUpdate\",\"type\":\"uint32\"}],\"name\":\"getLendingStateTickAndCheckpoint\",\"outputs\":[{\"internalType\":\"int16\",\"name\":\"lendingStateTick\",\"type\":\"int16\"},{\"internalType\":\"uint256\",\"name\":\"maxSatInWads\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"pairAddress\",\"type\":\"address\"}],\"name\":\"getObservations\",\"outputs\":[{\"components\":[{\"internalType\":\"bool\",\"name\":\"isMidTermBufferInitialized\",\"type\":\"bool\"},{\"internalType\":\"bool\",\"name\":\"isLongTermBufferInitialized\",\"type\":\"bool\"},{\"internalType\":\"uint8\",\"name\":\"midTermIndex\",\"type\":\"uint8\"},{\"internalType\":\"uint8\",\"name\":\"longTermIndex\",\"type\":\"uint8\"},{\"internalType\":\"int16\",\"name\":\"lastTick\",\"type\":\"int16\"},{\"internalType\":\"int16\",\"name\":\"lastLendingStateTick\",\"type\":\"int16\"},{\"internalType\":\"uint24\",\"name\":\"midTermIntervalConfig\",\"type\":\"uint24\"},{\"internalType\":\"uint24\",\"name\":\"longTermIntervalConfig\",\"type\":\"uint24\"},{\"internalType\":\"int56\",\"name\":\"lendingCumulativeSum\",\"type\":\"int56\"},{\"internalType\":\"int56[51]\",\"name\":\"midTermCumulativeSum\",\"type\":\"int56[51]\"},{\"internalType\":\"int56[9]\",\"name\":\"longTermCumulativeSum\",\"type\":\"int56[9]\"},{\"internalType\":\"uint32[51]\",\"name\":\"midTermTimeInterval\",\"type\":\"uint32[51]\"},{\"internalType\":\"uint32[9]\",\"name\":\"longTermTimeInterval\",\"type\":\"uint32[9]\"}],\"internalType\":\"struct GeometricTWAP.Observations\",\"name\":\"\",\"type\":\"tuple\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bool\",\"name\":\"isLongTermBufferInitialized\",\"type\":\"bool\"}],\"name\":\"getObservedMidTermTick\",\"outputs\":[{\"internalType\":\"int16\",\"name\":\"\",\"type\":\"int16\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"pair\",\"type\":\"address\"},{\"internalType\":\"int16\",\"name\":\"currentTick\",\"type\":\"int16\"},{\"internalType\":\"bool\",\"name\":\"includeLongTermTick\",\"type\":\"bool\"}],\"name\":\"getTickRange\",\"outputs\":[{\"internalType\":\"int16\",\"name\":\"\",\"type\":\"int16\"},{\"internalType\":\"int16\",\"name\":\"\",\"type\":\"int16\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"pairAddress\",\"type\":\"address\"},{\"internalType\":\"bool\",\"name\":\"netDebtX\",\"type\":\"bool\"},{\"internalType\":\"int16\",\"name\":\"tranche\",\"type\":\"int16\"}],\"name\":\"getTrancheDetails\",\"outputs\":[{\"internalType\":\"uint16\",\"name\":\"leaf\",\"type\":\"uint16\"},{\"components\":[{\"internalType\":\"uint128\",\"name\":\"satInLAssets\",\"type\":\"uint128\"},{\"internalType\":\"uint128\",\"name\":\"satRelativeToL\",\"type\":\"uint128\"}],\"internalType\":\"struct Saturation.SaturationPair\",\"name\":\"saturation\",\"type\":\"tuple\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"pairAddress\",\"type\":\"address\"},{\"internalType\":\"bool\",\"name\":\"netDebtX\",\"type\":\"bool\"}],\"name\":\"getTreeDetails\",\"outputs\":[{\"internalType\":\"uint16\",\"name\":\"\",\"type\":\"uint16\"},{\"internalType\":\"uint128\",\"name\":\"\",\"type\":\"uint128\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"int16\",\"name\":\"firstTick\",\"type\":\"int16\"}],\"name\":\"init\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"components\":[{\"internalType\":\"uint256[6]\",\"name\":\"userAssets\",\"type\":\"uint256[6]\"},{\"internalType\":\"uint256\",\"name\":\"sqrtPriceMinInQ72\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"sqrtPriceMaxInQ72\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"activeLiquidityScalerInQ72\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"activeLiquidityAssets\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"reservesXAssets\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"reservesYAssets\",\"type\":\"uint256\"}],\"internalType\":\"struct Validation.InputParams\",\"name\":\"inputParams\",\"type\":\"tuple\"},{\"internalType\":\"address\",\"name\":\"borrower\",\"type\":\"address\"},{\"components\":[{\"internalType\":\"uint256\",\"name\":\"depositLToBeTransferredInLAssets\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"depositXToBeTransferredInXAssets\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"depositYToBeTransferredInYAssets\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"repayLXInXAssets\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"repayLYInYAssets\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"repayXInXAssets\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"repayYInYAssets\",\"type\":\"uint256\"}],\"internalType\":\"struct Liquidation.HardLiquidationParams\",\"name\":\"hardLiquidationParams\",\"type\":\"tuple\"},{\"internalType\":\"uint256\",\"name\":\"actualRepaidLiquidityAssets\",\"type\":\"uint256\"}],\"name\":\"liquidationCheckHardPremiums\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"badDebt\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"longTermIntervalConfig\",\"outputs\":[{\"internalType\":\"uint24\",\"name\":\"\",\"type\":\"uint24\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"midTermIntervalConfig\",\"outputs\":[{\"internalType\":\"uint24\",\"name\":\"\",\"type\":\"uint24\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"owner\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"int16\",\"name\":\"newTick\",\"type\":\"int16\"},{\"internalType\":\"uint32\",\"name\":\"timeElapsed\",\"type\":\"uint32\"}],\"name\":\"recordObservation\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"renounceOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"pair\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"maxDesiredSaturationMag2\",\"type\":\"uint256\"}],\"name\":\"setNewPositionSaturation\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"transferOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"components\":[{\"internalType\":\"uint256[6]\",\"name\":\"userAssets\",\"type\":\"uint256[6]\"},{\"internalType\":\"uint256\",\"name\":\"sqrtPriceMinInQ72\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"sqrtPriceMaxInQ72\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"activeLiquidityScalerInQ72\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"activeLiquidityAssets\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"reservesXAssets\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"reservesYAssets\",\"type\":\"uint256\"}],\"internalType\":\"struct Validation.InputParams\",\"name\":\"inputParams\",\"type\":\"tuple\"},{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"update\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"errors\":{\"OwnableInvalidOwner(address)\":[{\"details\":\"The owner is not a valid owner account. (eg. `address(0)`)\"}],\"OwnableUnauthorizedAccount(address)\":[{\"details\":\"The caller account is not authorized to perform an operation.\"}],\"SafeCastOverflowedUintDowncast(uint8,uint256)\":[{\"details\":\"Value doesn't fit in an uint of `bits` size.\"}]},\"events\":{\"UpdateLendingTick(int16)\":{\"details\":\"Emitted when `lendingStateTick` is updated\",\"params\":{\"lendingStateTick\":\"The updated value for lending state tick\"}}},\"kind\":\"dev\",\"methods\":{\"accruePenalties(address,uint256,uint256,uint256,uint256,uint256)\":{\"params\":{\"allAssetsBorrowL\":\"allAsset[BORROW_L]\",\"allAssetsDepositL\":\"allAsset[DEPOSIT_L]\",\"allSharesBorrowL\":\"allShares[BORROW_L]\",\"duration\":\"since last accrual of penalties\",\"externalLiquidity\":\"Swap liquidity outside this pool\"}},\"boundTick(int16)\":{\"details\":\"The function ensures that `newTick` stays within the bounds      determined by `lastTick` and a dynamically calculated factor.\",\"params\":{\"newTick\":\"The proposed new tick value to be adjusted within valid bounds.\"},\"returns\":{\"_0\":\"The adjusted tick value constrained within the allowable range.\"}},\"calcSatChangeRatioBips((uint256[6],uint256,uint256,uint256,uint256,uint256,uint256),uint256,uint256,address,address)\":{\"params\":{\"account\":\"The account for which we are calculating the saturation change ratio.\",\"inputParams\":\"The params containing the position of `account`.\",\"liqSqrtPriceInXInQ72\":\"The liquidation price.\",\"pairAddress\":\"The address of the pair\"},\"returns\":{\"ratioNetXBips\":\"The ratio representing the change in netX saturation for account.\",\"ratioNetYBips\":\"The ratio representing the change in netY saturation for account.\"}},\"configLongTermInterval(address,uint24)\":{\"details\":\"This function is used to set the long-term interval between observations for the long-term buffer.\",\"params\":{\"_longTermIntervalConfig\":\"The desired duration for each long-term period.      The size is set as a factor of the mid-term interval to ensure a sufficient buffer, requiring      at least 16 * 12 = 192 seconds per period, resulting in a total of ~25 minutes (192 * 8 = 1536 seconds)      for the long-term buffer.\",\"pairAddress\":\"The address of the pair for which the long-term interval is being configured.\"}},\"getLendingStateTick(int56,uint32,uint32)\":{\"params\":{\"newTick\":\"The new tick value to be recorded, representing the most recent update of reserveXAssets and reserveYAssets.\",\"timeElapsedSinceLendingUpdate\":\"The time elapsed since the last lending update.\",\"timeElapsedSinceUpdate\":\"The time elapsed since the last price update.\"},\"returns\":{\"lendingStateTick\":\"The tick value representing the TWAP since the last lending update.\",\"maxSatInWads\":\"The maximum saturation in WADs.\"}},\"getLendingStateTickAndCheckpoint(uint32,uint32)\":{\"details\":\"See `getLendingStateTick` for implementation details which was      separated to allow view access without any state updates.\",\"params\":{\"timeElapsedSinceLendingUpdate\":\"The time elapsed since the last lending update.\",\"timeElapsedSinceUpdate\":\"The time elapsed since the last price update.\"},\"returns\":{\"lendingStateTick\":\"The tick value representing the TWAP since the last lending update.\"}},\"getObservedMidTermTick(bool)\":{\"details\":\"Retrieves the mid-term tick value based on the stored observations.\",\"params\":{\"isLongTermBufferInitialized\":\"Boolean value which represents whether long-term buffer is filled or not.\"},\"returns\":{\"_0\":\"midTermTick The mid-term tick value.\"}},\"getTickRange(address,int16,bool)\":{\"details\":\"This function calculates the minimum and maximum tick values among three observed ticks:          long-term tick, mid-term tick, and current tick.\",\"params\":{\"currentTick\":\"The current (most recent) tick based on the current reserves.\",\"includeLongTermTick\":\"Boolean value indicating whether to include the long-term tick in the range.\",\"pair\":\"The address of the pair for which the tick range is being calculated.\"},\"returns\":{\"_0\":\"minTick The minimum tick value among the three observed ticks.\",\"_1\":\"maxTick The maximum tick value among the three observed ticks.\"}},\"init(int16)\":{\"details\":\"initCheck can be removed once the tree structure is fixed\"},\"owner()\":{\"details\":\"Returns the address of the current owner.\"},\"recordObservation(int16,uint32)\":{\"details\":\"This function is used to record new observation data for the contract. It ensures that      the provided tick value is stored appropriately in both mid-term and long-term      observations, updates interval counters, and handles tick cumulative values based      on the current interval configuration. Ensures that this function is called in      chronological order, with increasing timestamps. Returns in case the      provided block timestamp is less than or equal to the last recorded timestamp.\",\"params\":{\"newTick\":\"The new tick value to be recorded, representing the most recent update of      reserveXAssets and reserveYAssets.\",\"timeElapsed\":\"The time elapsed since the last observation.\"},\"returns\":{\"_0\":\"bool indicating whether the observation was recorded or not.\"}},\"renounceOwnership()\":{\"details\":\"Leaves the contract without owner. It will not be possible to call `onlyOwner` functions. Can only be called by the current owner. NOTE: Renouncing ownership will leave the contract without an owner, thereby disabling any functionality that is only available to the owner.\"},\"transferOwnership(address)\":{\"details\":\"Transfers ownership of the contract to a new account (`newOwner`). Can only be called by the current owner.\"},\"update((uint256[6],uint256,uint256,uint256,uint256,uint256,uint256),address)\":{\"details\":\"run accruePenalties before running this function\",\"params\":{\"account\":\"for which is position is being updated\",\"inputParams\":\"contains the position and pair params, like account borrows/deposits, current price and active liquidity\"}}},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{\"accruePenalties(address,uint256,uint256,uint256,uint256,uint256)\":{\"notice\":\"accrue penalties since last accrual based on all over saturated positions\"},\"calcSatChangeRatioBips((uint256[6],uint256,uint256,uint256,uint256,uint256,uint256),uint256,uint256,address,address)\":{\"notice\":\"Calculate the ratio by which the saturation has changed for `account`.\"},\"configLongTermInterval(address,uint24)\":{\"notice\":\"Configures the interval of long-term observations.\"},\"getLendingStateTick(int56,uint32,uint32)\":{\"notice\":\"Gets the tick value representing the TWAP since the last lending update.\"},\"getLendingStateTickAndCheckpoint(uint32,uint32)\":{\"notice\":\"Gets the tick value representing the TWAP since the last         lending update and checkpoints the current lending cumulative sum         as `self.lendingCumulativeSum` and the current block timestamp as `self.lastLendingTimestamp`.\"},\"getTickRange(address,int16,bool)\":{\"notice\":\"Gets the min and max range of tick values from the stored oracle observations.\"},\"init(int16)\":{\"notice\":\"initializes the sat and TWAP struct\"},\"recordObservation(int16,uint32)\":{\"notice\":\"Records a new observation tick value and updates the observation data.\"},\"update((uint256[6],uint256,uint256,uint256,uint256,uint256,uint256),address)\":{\"notice\":\"update the borrow position of an account and potentially check (and revert) if the resulting sat is too high\"}},\"version\":1}},\"settings\":{\"compilationTarget\":{\"contracts/SaturationAndGeometricTWAPState.sol\":\"SaturationAndGeometricTWAPState\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":1inch/=lib/1inch/\",\":@1inch/=lib/1inch/\",\":@mangrovedao/mangrove-core/=lib/mangrove-core/\",\":@mgv/forge-std/=lib/mangrove-core/lib/forge-std/src/\",\":@mgv/lib/=lib/mangrove-core/lib/\",\":@mgv/script/=lib/mangrove-core/script/\",\":@mgv/src/=lib/mangrove-core/src/\",\":@mgv/test/=lib/mangrove-core/test/\",\":@morpho-org/morpho-blue/=lib/morpho-blue/\",\":@openzeppelin/=lib/openzeppelin-contracts/\",\":ExcessivelySafeCall/=lib/ExcessivelySafeCall/src/\",\":core/=lib/mangrove-core/lib/core/\",\":ds-test/=lib/ExcessivelySafeCall/lib/ds-test/src/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":forge-std/=lib/openzeppelin-contracts/lib/forge-std/src/\",\":halmos-cheatcodes/=lib/morpho-blue/lib/halmos-cheatcodes/src/\",\":mangrove-core/=lib/mangrove-core/\",\":morpho-blue/=lib/morpho-blue/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":preprocessed/=lib/mangrove-core/lib/preprocessed/\"]},\"sources\":{\"contracts/SaturationAndGeometricTWAPState.sol\":{\"keccak256\":\"0x5e293a35668bb216a99379ea2176894314cc0f1ac68644fcf4c07017da1a4419\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://00349bb86f1b657010919b4bc3f616ad56ef4883b99ab0eead36815dae93dc76\",\"dweb:/ipfs/QmbEd9GD2JxuDntX35YcfbSCcpRstDU9GDPUkBKGzsxvqE\"]},\"contracts/interfaces/ISaturationAndGeometricTWAPState.sol\":{\"keccak256\":\"0xc9add2ad41f8edd9d360ced8d2cd7bd18dd500304794434fb2e309fa0f5af83c\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://8ecc810c544ac734ef26a2f6bebea3f3bd12d773965d297991e0e0e72892fa20\",\"dweb:/ipfs/QmarXc1Ut4FZzPRRZs2M2udbJjuZUJQHQ8fvmSr3bpHErR\"]},\"contracts/interfaces/tokens/IAmmalgamERC20.sol\":{\"keccak256\":\"0x44a376269170b4270ec221ce3cb31a609b394e216cc4d2e27b818361b4369829\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://c48bc7586631f27ede73d3d0b4c1d7a29b1653e6c501c8b7fc9877c125f8f57e\",\"dweb:/ipfs/QmTSLtqnsxr7h7ct524rqYssHUo4qursmCZ7g5q3J1qQPK\"]},\"contracts/interfaces/tokens/ITokenController.sol\":{\"keccak256\":\"0x7778001aaf582fe10005240eb6023b2b6cee3f100b6c2222bf6b9ade93732624\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://91e5c4519207d6a450be1e0a8649157e86d20f8ef6a91ff6512a31cf5561a570\",\"dweb:/ipfs/QmUqZLW27JJZHFPf2fgLDYSWWj5gM158DdaxTTmDVukRAg\"]},\"contracts/libraries/Convert.sol\":{\"keccak256\":\"0x944776d31291de1a9cdc6a52154c23c22b43a01c3edebe7a4140e267edbba975\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://36c03749859077ba47a3acfd574f8c30f34f97def4ce81d7f4feac9a7b62794c\",\"dweb:/ipfs/QmdycZay5X2WrbS8qS7RycLpZbMQx7yKszWQzGU3rqidpH\"]},\"contracts/libraries/GeometricTWAP.sol\":{\"keccak256\":\"0x3860409daa0fdb5d96f0bfb8b49cbca058b9fe32c8e32457f85d4ee2c5cdcb1e\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://81fe70a80f4005a6529c7e93f1a92547ce0bf74c357c280a91e8778b378b18aa\",\"dweb:/ipfs/QmdRQ1DqsCu11zfbLAbrrzJ9Ups3oKgTGimYo3Zm3ALiCz\"]},\"contracts/libraries/Interest.sol\":{\"keccak256\":\"0xbc8bfa20d7295dd70e3c716fd3dbeb5b45d313e3c609d063d186042cbf000646\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://b015e8d4976d3b6d7eaca07dfcc487aeed3a7d8b4c41c8369a7476dcfb211194\",\"dweb:/ipfs/QmecH84UnZYxDZ2aL6rQtnrEExLEAfo7q4Y47yuBXdymeX\"]},\"contracts/libraries/Liquidation.sol\":{\"keccak256\":\"0x842bc44bc3cff80360ab82c5920070b12680edefe9267bdffc2d6c3c3a692d63\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://85ecd75568a0729aec06741d0575ed07dad8b7daebd7ba3114a93f6019250877\",\"dweb:/ipfs/QmQMvWdsPWsQ4t1yv6eyZy5TM7h1EZpSJdt5b8fDLcumCW\"]},\"contracts/libraries/QuadraticSwapFees.sol\":{\"keccak256\":\"0x00f6b7909be4fa1fc1ba426dd8ae659d1c5cb20c79665148898c973f55cfdccb\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://c64da0826a9b0ffc08319709f6db03339d22d24deda902a6540393251da0aecb\",\"dweb:/ipfs/QmSNwBbn2VAS8HPY4hNZusEc4DoKKZAZHtpPdjL9Gz3gs3\"]},\"contracts/libraries/Saturation.sol\":{\"keccak256\":\"0xf44bc610ece4bc7ebdb0730aa6ad69ea47647e19d4c1944c663d2d2eb4f10860\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://421fdf8d0b27132bc324a42ede9aaf23b476e5134e1073f84e824620a2a44f20\",\"dweb:/ipfs/QmbvSfMuMzDmrfPkCAEp7ydtRDWu5EUiXq4MyrGGjFErzE\"]},\"contracts/libraries/TickMath.sol\":{\"keccak256\":\"0x753813c7ed638d22edb71f48f8eb8b4283b3db2ba5b136b5c8909bd37ffa3f12\",\"license\":\"GPL-2.0-or-later\",\"urls\":[\"bzz-raw://04dd5085b72f6d73e1b17f58148e4d03639f654bdc4fdbc173b7c92ff102fc20\",\"dweb:/ipfs/QmSg4xTQPkngjNxs84428FZdSwH4AUQpwLXaASx7Qev6oG\"]},\"contracts/libraries/Uint16Set.sol\":{\"keccak256\":\"0x26a714430fe1618d78386e953153b4bd2bf024baee54453ec9a7a0cc60e1534f\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://8667dd78541d656a09678e5f9cce4d49adc805955604ccaaec414e9b241f5e06\",\"dweb:/ipfs/QmZVWU2CzyDQfGit32HjJxDphBJMKG3d6JRuxbC682Z1gy\"]},\"contracts/libraries/Validation.sol\":{\"keccak256\":\"0x294848b2af973dbcd8b83732a57b67f14fd15e4af0668de05a2928b8eca5a463\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://fab25c941e87f6924b31e3f20742ca6b5ec1b7e4251543f4a61567a04ef4d778\",\"dweb:/ipfs/Qmf4ChH8afdHc3SfXkFPpNGp3e1hscyvnujPAMza3yuXeA\"]},\"contracts/libraries/constants.sol\":{\"keccak256\":\"0x0dfb294985a8f48287ff13e8476718ddb5334b1d8bf6bfa59a5db1dbcf6ca7c4\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://4bedcfdb2850cfb22b5daa768ab8125b4ccab97c90068d1d0ad4495bf942b362\",\"dweb:/ipfs/Qmf9p88yQN2JYRBR5D7q9BLmwhDJWpFk47ZuayrKqCyHat\"]},\"lib/mangrove-core/lib/core/BitLib.sol\":{\"keccak256\":\"0x80f6885268986b9e976b424993aa875cf7aab8464403ed675a86ade9e9be5ee3\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5a31b4e1e0dc95de9a1dbb114c40c44814de5db3e2d857c93c2524a61454f6c8\",\"dweb:/ipfs/QmRkgE9ue5rGwE6XDnszF2e2meWqAC9nnKM97xKHjHphQr\"]},\"lib/morpho-blue/src/libraries/MathLib.sol\":{\"keccak256\":\"0xa7354cbbcecef7bc0c94b61061c4e5da75515056b8e2db65e826b00d7369744a\",\"license\":\"GPL-2.0-or-later\",\"urls\":[\"bzz-raw://d7419c59bb906fcfa49320b68f265c3200090e5c30b194766256aee70b012e08\",\"dweb:/ipfs/Qmbo4uaW6XYnudya4bb6RU6riWXFk5M3CWJge5XzTTaEfd\"]},\"lib/openzeppelin-contracts/contracts/access/Ownable.sol\":{\"keccak256\":\"0xff6d0bb2e285473e5311d9d3caacb525ae3538a80758c10649a4d61029b017bb\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://8ed324d3920bb545059d66ab97d43e43ee85fd3bd52e03e401f020afb0b120f6\",\"dweb:/ipfs/QmfEckWLmZkDDcoWrkEvMWhms66xwTLff9DDhegYpvHo1a\"]},\"lib/openzeppelin-contracts/contracts/interfaces/IERC20.sol\":{\"keccak256\":\"0xb5d81383d40f4006d1ce4bbad0064e7a930e17302cbe2a745e09cb403f042733\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3fc4a5681c2f00f41f49260a36ae6bbe1121dd93d470ea24d51d556eff2980be\",\"dweb:/ipfs/QmUBW6TwVWtGP96ka9TfuGivd27kH8CtkXD8RQAAecSFiR\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol\":{\"keccak256\":\"0xfe37358f223eddd65d61bb62b0b7bdb69d7101b5ec8d484292b8c1583a153b8a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://28dd43f30af3c12ae0fc08dd031b1250e906ef3c95f63f30fac6fd15aee2a662\",\"dweb:/ipfs/QmUkSyWsSRx36w1ti7U6qnGnQgJq16wpMhjeJrnyn9AXwG\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol\":{\"keccak256\":\"0xbaffa0bcc92adf28a53cc3b68551fc3632cb8f849a0028cb8d5c06e4677715e9\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://32e6f8f6b2e883c85e6a602c0882d9962ce2f92406961244e86cd974df815912\",\"dweb:/ipfs/Qmahvx6fPpecicq1aUE1JihCxV5ep1bfuPukzrxa8Ub5PS\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Permit.sol\":{\"keccak256\":\"0x093f32ab700c2b05373387263915a75f5455cdb0f09a7630cc621e27b7b50d04\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://d163e6ef21df143969df5557305e8c643a135c7660a678d0c65dca91772114a0\",\"dweb:/ipfs/QmTZUgiwEro5oLRhbJ2iSWyCqu1JTDekoFHALVUn4eHqYK\"]},\"lib/openzeppelin-contracts/contracts/utils/Context.sol\":{\"keccak256\":\"0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12\",\"dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF\"]},\"lib/openzeppelin-contracts/contracts/utils/Panic.sol\":{\"keccak256\":\"0xf7fe324703a64fc51702311dc51562d5cb1497734f074e4f483bfb6717572d7a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://c6a5ff4f9fd8649b7ee20800b7fa387d3465bd77cf20c2d1068cd5c98e1ed57a\",\"dweb:/ipfs/QmVSaVJf9FXFhdYEYeCEfjMVHrxDh5qL4CGkxdMWpQCrqG\"]},\"lib/openzeppelin-contracts/contracts/utils/math/Math.sol\":{\"keccak256\":\"0x1225214420c83ebcca88f2ae2b50f053aaa7df7bd684c3e878d334627f2edfc6\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6c5fab4970634f9ab9a620983dc1c8a30153981a0b1a521666e269d0a11399d3\",\"dweb:/ipfs/QmVRnBC575MESGkEHndjujtR7qub2FzU9RWy9eKLp4hPZB\"]},\"lib/openzeppelin-contracts/contracts/utils/math/SafeCast.sol\":{\"keccak256\":\"0x195533c86d0ef72bcc06456a4f66a9b941f38eb403739b00f21fd7c1abd1ae54\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b1d578337048cad08c1c03041cca5978eff5428aa130c781b271ad9e5566e1f8\",\"dweb:/ipfs/QmPFKL2r9CBsMwmUqqdcFPfHZB2qcs9g1HDrPxzWSxomvy\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "uint24", "name": "_midTermIntervalConfig", "type": "uint24"}, {"internalType": "uint24", "name": "_longTermIntervalConfig", "type": "uint24"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "type": "error", "name": "AmmalgamMaxSlippage"}, {"inputs": [], "type": "error", "name": "CannotUpdateZeroAddress"}, {"inputs": [], "type": "error", "name": "InvalidIntervalConfig"}, {"inputs": [], "type": "error", "name": "InvalidUserConfiguration"}, {"inputs": [], "type": "error", "name": "LiquidationPremiumTooHigh"}, {"inputs": [], "type": "error", "name": "MaxTrancheOverSaturated"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "type": "error", "name": "OwnableInvalidOwner"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "type": "error", "name": "OwnableUnauthorizedAccount"}, {"inputs": [], "type": "error", "name": "PairAlreadyExists"}, {"inputs": [], "type": "error", "name": "PairDoesNotExist"}, {"inputs": [], "type": "error", "name": "PriceOutOfBounds"}, {"inputs": [{"internalType": "uint8", "name": "bits", "type": "uint8"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "type": "error", "name": "SafeCastOverflowedUintDowncast"}, {"inputs": [], "type": "error", "name": "TickOutOfBounds"}, {"inputs": [{"internalType": "address", "name": "previousOwner", "type": "address", "indexed": true}, {"internalType": "address", "name": "new<PERSON>wner", "type": "address", "indexed": true}], "type": "event", "name": "OwnershipTransferred", "anonymous": false}, {"inputs": [{"internalType": "int16", "name": "lendingStateTick", "type": "int16", "indexed": false}], "type": "event", "name": "UpdateLendingTick", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "uint256", "name": "externalLiquidity", "type": "uint256"}, {"internalType": "uint256", "name": "duration", "type": "uint256"}, {"internalType": "uint256", "name": "allAssetsDepositL", "type": "uint256"}, {"internalType": "uint256", "name": "allAssetsBorrowL", "type": "uint256"}, {"internalType": "uint256", "name": "allSharesBorrowL", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "accruePenalties", "outputs": [{"internalType": "uint112", "name": "penaltyInBorrowLShares", "type": "uint112"}, {"internalType": "uint112", "name": "accountPenaltyInBorrowLShares", "type": "uint112"}]}, {"inputs": [{"internalType": "int16", "name": "newTick", "type": "int16"}], "stateMutability": "view", "type": "function", "name": "boundTick", "outputs": [{"internalType": "int16", "name": "", "type": "int16"}]}, {"inputs": [{"internalType": "struct Validation.InputParams", "name": "inputParams", "type": "tuple", "components": [{"internalType": "uint256[6]", "name": "userAssets", "type": "uint256[6]"}, {"internalType": "uint256", "name": "sqrtPriceMinInQ72", "type": "uint256"}, {"internalType": "uint256", "name": "sqrtPriceMaxInQ72", "type": "uint256"}, {"internalType": "uint256", "name": "activeLiquidityScalerInQ72", "type": "uint256"}, {"internalType": "uint256", "name": "activeLiquidityAssets", "type": "uint256"}, {"internalType": "uint256", "name": "reservesXAssets", "type": "uint256"}, {"internalType": "uint256", "name": "reservesYAssets", "type": "uint256"}]}, {"internalType": "uint256", "name": "liqSqrtPriceInXInQ72", "type": "uint256"}, {"internalType": "uint256", "name": "liqSqrtPriceInYInQ72", "type": "uint256"}, {"internalType": "address", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address"}, {"internalType": "address", "name": "account", "type": "address"}], "stateMutability": "view", "type": "function", "name": "calcSatChangeRatioBips", "outputs": [{"internalType": "uint256", "name": "ratioNetXBips", "type": "uint256"}, {"internalType": "uint256", "name": "ratioNetYBips", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address"}, {"internalType": "uint24", "name": "_longTermIntervalConfig", "type": "uint24"}], "stateMutability": "nonpayable", "type": "function", "name": "configLongTermInterval"}, {"inputs": [{"internalType": "address", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address"}, {"internalType": "bool", "name": "netDebtX", "type": "bool"}, {"internalType": "address", "name": "accountAddress", "type": "address"}], "stateMutability": "view", "type": "function", "name": "getAccount", "outputs": [{"internalType": "struct Saturation.Account", "name": "", "type": "tuple", "components": [{"internalType": "bool", "name": "exists", "type": "bool"}, {"internalType": "int16", "name": "lastTranche", "type": "int16"}, {"internalType": "uint112", "name": "penaltyInBorrowLShares", "type": "uint112"}, {"internalType": "struct Saturation.SaturationPair[]", "name": "satPairPerTranche", "type": "tuple[]", "components": [{"internalType": "uint128", "name": "satInLAssets", "type": "uint128"}, {"internalType": "uint128", "name": "satRelativeToL", "type": "uint128"}]}, {"internalType": "uint256[]", "name": "treePenaltyAtOnsetInBorrowLSharesPerSatInQ72PerTranche", "type": "uint256[]"}]}]}, {"inputs": [{"internalType": "address", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address"}, {"internalType": "bool", "name": "netDebtX", "type": "bool"}, {"internalType": "uint256", "name": "leafIndex", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "getLeafDetails", "outputs": [{"internalType": "struct Saturation.SaturationPair", "name": "saturation", "type": "tuple", "components": [{"internalType": "uint128", "name": "satInLAssets", "type": "uint128"}, {"internalType": "uint128", "name": "satRelativeToL", "type": "uint128"}]}, {"internalType": "uint256", "name": "currentPenaltyInBorrowLSharesPerSatInQ72", "type": "uint256"}, {"internalType": "uint16[]", "name": "tranches", "type": "uint16[]"}]}, {"inputs": [{"internalType": "int56", "name": "newTick", "type": "int56"}, {"internalType": "uint32", "name": "timeElapsedSinceUpdate", "type": "uint32"}, {"internalType": "uint32", "name": "timeElapsedSinceLendingUpdate", "type": "uint32"}], "stateMutability": "view", "type": "function", "name": "getLendingStateTick", "outputs": [{"internalType": "int16", "name": "lendingStateTick", "type": "int16"}, {"internalType": "uint256", "name": "maxSatInWads", "type": "uint256"}]}, {"inputs": [{"internalType": "uint32", "name": "timeElapsedSinceUpdate", "type": "uint32"}, {"internalType": "uint32", "name": "timeElapsedSinceLendingUpdate", "type": "uint32"}], "stateMutability": "nonpayable", "type": "function", "name": "getLendingStateTickAndCheckpoint", "outputs": [{"internalType": "int16", "name": "lendingStateTick", "type": "int16"}, {"internalType": "uint256", "name": "maxSatInWads", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address"}], "stateMutability": "view", "type": "function", "name": "getObservations", "outputs": [{"internalType": "struct GeometricTWAP.Observations", "name": "", "type": "tuple", "components": [{"internalType": "bool", "name": "isMidTermBufferInitialized", "type": "bool"}, {"internalType": "bool", "name": "isLongTermBufferInitialized", "type": "bool"}, {"internalType": "uint8", "name": "midTermIndex", "type": "uint8"}, {"internalType": "uint8", "name": "longTermIndex", "type": "uint8"}, {"internalType": "int16", "name": "lastTick", "type": "int16"}, {"internalType": "int16", "name": "lastLendingStateTick", "type": "int16"}, {"internalType": "uint24", "name": "midTermIntervalConfig", "type": "uint24"}, {"internalType": "uint24", "name": "longTermIntervalConfig", "type": "uint24"}, {"internalType": "int56", "name": "lendingCumulativeSum", "type": "int56"}, {"internalType": "int56[51]", "name": "midTermCumulativeSum", "type": "int56[51]"}, {"internalType": "int56[9]", "name": "longTermCumulativeSum", "type": "int56[9]"}, {"internalType": "uint32[51]", "name": "midTermTimeInterval", "type": "uint32[51]"}, {"internalType": "uint32[9]", "name": "longTermTimeInterval", "type": "uint32[9]"}]}]}, {"inputs": [{"internalType": "bool", "name": "isLongTermBufferInitialized", "type": "bool"}], "stateMutability": "view", "type": "function", "name": "getObservedMidTermTick", "outputs": [{"internalType": "int16", "name": "", "type": "int16"}]}, {"inputs": [{"internalType": "address", "name": "pair", "type": "address"}, {"internalType": "int16", "name": "currentTick", "type": "int16"}, {"internalType": "bool", "name": "includeLongTermTick", "type": "bool"}], "stateMutability": "view", "type": "function", "name": "getTickRange", "outputs": [{"internalType": "int16", "name": "", "type": "int16"}, {"internalType": "int16", "name": "", "type": "int16"}]}, {"inputs": [{"internalType": "address", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address"}, {"internalType": "bool", "name": "netDebtX", "type": "bool"}, {"internalType": "int16", "name": "tranche", "type": "int16"}], "stateMutability": "view", "type": "function", "name": "getTrancheDetails", "outputs": [{"internalType": "uint16", "name": "leaf", "type": "uint16"}, {"internalType": "struct Saturation.SaturationPair", "name": "saturation", "type": "tuple", "components": [{"internalType": "uint128", "name": "satInLAssets", "type": "uint128"}, {"internalType": "uint128", "name": "satRelativeToL", "type": "uint128"}]}]}, {"inputs": [{"internalType": "address", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address"}, {"internalType": "bool", "name": "netDebtX", "type": "bool"}], "stateMutability": "view", "type": "function", "name": "getTreeDetails", "outputs": [{"internalType": "uint16", "name": "", "type": "uint16"}, {"internalType": "uint128", "name": "", "type": "uint128"}]}, {"inputs": [{"internalType": "int16", "name": "firstTick", "type": "int16"}], "stateMutability": "nonpayable", "type": "function", "name": "init"}, {"inputs": [{"internalType": "struct Validation.InputParams", "name": "inputParams", "type": "tuple", "components": [{"internalType": "uint256[6]", "name": "userAssets", "type": "uint256[6]"}, {"internalType": "uint256", "name": "sqrtPriceMinInQ72", "type": "uint256"}, {"internalType": "uint256", "name": "sqrtPriceMaxInQ72", "type": "uint256"}, {"internalType": "uint256", "name": "activeLiquidityScalerInQ72", "type": "uint256"}, {"internalType": "uint256", "name": "activeLiquidityAssets", "type": "uint256"}, {"internalType": "uint256", "name": "reservesXAssets", "type": "uint256"}, {"internalType": "uint256", "name": "reservesYAssets", "type": "uint256"}]}, {"internalType": "address", "name": "borrower", "type": "address"}, {"internalType": "struct Liquidation.HardLiquidationParams", "name": "hardLiquidationParams", "type": "tuple", "components": [{"internalType": "uint256", "name": "depositLToBeTransferredInLAssets", "type": "uint256"}, {"internalType": "uint256", "name": "depositXToBeTransferredInXAssets", "type": "uint256"}, {"internalType": "uint256", "name": "depositYToBeTransferredInYAssets", "type": "uint256"}, {"internalType": "uint256", "name": "repayLXInXAssets", "type": "uint256"}, {"internalType": "uint256", "name": "repayLYInYAssets", "type": "uint256"}, {"internalType": "uint256", "name": "repayXInXAssets", "type": "uint256"}, {"internalType": "uint256", "name": "repayYInYAssets", "type": "uint256"}]}, {"internalType": "uint256", "name": "actualRepaidLiquidityAssets", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "liquidationCheckHardPremiums", "outputs": [{"internalType": "bool", "name": "badDebt", "type": "bool"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "longTermIntervalConfig", "outputs": [{"internalType": "uint24", "name": "", "type": "uint24"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "midTermIntervalConfig", "outputs": [{"internalType": "uint24", "name": "", "type": "uint24"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "int16", "name": "newTick", "type": "int16"}, {"internalType": "uint32", "name": "timeElapsed", "type": "uint32"}], "stateMutability": "nonpayable", "type": "function", "name": "recordObservation", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "renounceOwnership"}, {"inputs": [{"internalType": "address", "name": "pair", "type": "address"}, {"internalType": "uint256", "name": "maxDesiredSaturationMag2", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "setNewPositionSaturation"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "transferOwnership"}, {"inputs": [{"internalType": "struct Validation.InputParams", "name": "inputParams", "type": "tuple", "components": [{"internalType": "uint256[6]", "name": "userAssets", "type": "uint256[6]"}, {"internalType": "uint256", "name": "sqrtPriceMinInQ72", "type": "uint256"}, {"internalType": "uint256", "name": "sqrtPriceMaxInQ72", "type": "uint256"}, {"internalType": "uint256", "name": "activeLiquidityScalerInQ72", "type": "uint256"}, {"internalType": "uint256", "name": "activeLiquidityAssets", "type": "uint256"}, {"internalType": "uint256", "name": "reservesXAssets", "type": "uint256"}, {"internalType": "uint256", "name": "reservesYAssets", "type": "uint256"}]}, {"internalType": "address", "name": "account", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "update"}], "devdoc": {"kind": "dev", "methods": {"accruePenalties(address,uint256,uint256,uint256,uint256,uint256)": {"params": {"allAssetsBorrowL": "allAsset[BORROW_L]", "allAssetsDepositL": "allAsset[DEPOSIT_L]", "allSharesBorrowL": "allShares[BORROW_L]", "duration": "since last accrual of penalties", "externalLiquidity": "Swap liquidity outside this pool"}}, "boundTick(int16)": {"details": "The function ensures that `newTick` stays within the bounds      determined by `lastTick` and a dynamically calculated factor.", "params": {"newTick": "The proposed new tick value to be adjusted within valid bounds."}, "returns": {"_0": "The adjusted tick value constrained within the allowable range."}}, "calcSatChangeRatioBips((uint256[6],uint256,uint256,uint256,uint256,uint256,uint256),uint256,uint256,address,address)": {"params": {"account": "The account for which we are calculating the saturation change ratio.", "inputParams": "The params containing the position of `account`.", "liqSqrtPriceInXInQ72": "The liquidation price.", "pairAddress": "The address of the pair"}, "returns": {"ratioNetXBips": "The ratio representing the change in netX saturation for account.", "ratioNetYBips": "The ratio representing the change in netY saturation for account."}}, "configLongTermInterval(address,uint24)": {"details": "This function is used to set the long-term interval between observations for the long-term buffer.", "params": {"_longTermIntervalConfig": "The desired duration for each long-term period.      The size is set as a factor of the mid-term interval to ensure a sufficient buffer, requiring      at least 16 * 12 = 192 seconds per period, resulting in a total of ~25 minutes (192 * 8 = 1536 seconds)      for the long-term buffer.", "pairAddress": "The address of the pair for which the long-term interval is being configured."}}, "getLendingStateTick(int56,uint32,uint32)": {"params": {"newTick": "The new tick value to be recorded, representing the most recent update of reserveXAssets and reserveYAssets.", "timeElapsedSinceLendingUpdate": "The time elapsed since the last lending update.", "timeElapsedSinceUpdate": "The time elapsed since the last price update."}, "returns": {"lendingStateTick": "The tick value representing the TWAP since the last lending update.", "maxSatInWads": "The maximum saturation in WADs."}}, "getLendingStateTickAndCheckpoint(uint32,uint32)": {"details": "See `getLendingStateTick` for implementation details which was      separated to allow view access without any state updates.", "params": {"timeElapsedSinceLendingUpdate": "The time elapsed since the last lending update.", "timeElapsedSinceUpdate": "The time elapsed since the last price update."}, "returns": {"lendingStateTick": "The tick value representing the TWAP since the last lending update."}}, "getObservedMidTermTick(bool)": {"details": "Retrieves the mid-term tick value based on the stored observations.", "params": {"isLongTermBufferInitialized": "Boolean value which represents whether long-term buffer is filled or not."}, "returns": {"_0": "midTermTick The mid-term tick value."}}, "getTickRange(address,int16,bool)": {"details": "This function calculates the minimum and maximum tick values among three observed ticks:          long-term tick, mid-term tick, and current tick.", "params": {"currentTick": "The current (most recent) tick based on the current reserves.", "includeLongTermTick": "Boolean value indicating whether to include the long-term tick in the range.", "pair": "The address of the pair for which the tick range is being calculated."}, "returns": {"_0": "minTick The minimum tick value among the three observed ticks.", "_1": "maxTick The maximum tick value among the three observed ticks."}}, "init(int16)": {"details": "initCheck can be removed once the tree structure is fixed"}, "owner()": {"details": "Returns the address of the current owner."}, "recordObservation(int16,uint32)": {"details": "This function is used to record new observation data for the contract. It ensures that      the provided tick value is stored appropriately in both mid-term and long-term      observations, updates interval counters, and handles tick cumulative values based      on the current interval configuration. Ensures that this function is called in      chronological order, with increasing timestamps. Returns in case the      provided block timestamp is less than or equal to the last recorded timestamp.", "params": {"newTick": "The new tick value to be recorded, representing the most recent update of      reserveXAssets and reserveYAssets.", "timeElapsed": "The time elapsed since the last observation."}, "returns": {"_0": "bool indicating whether the observation was recorded or not."}}, "renounceOwnership()": {"details": "Leaves the contract without owner. It will not be possible to call `onlyOwner` functions. Can only be called by the current owner. NOTE: Renouncing ownership will leave the contract without an owner, thereby disabling any functionality that is only available to the owner."}, "transferOwnership(address)": {"details": "Transfers ownership of the contract to a new account (`newOwner`). Can only be called by the current owner."}, "update((uint256[6],uint256,uint256,uint256,uint256,uint256,uint256),address)": {"details": "run accruePenalties before running this function", "params": {"account": "for which is position is being updated", "inputParams": "contains the position and pair params, like account borrows/deposits, current price and active liquidity"}}}, "version": 1}, "userdoc": {"kind": "user", "methods": {"accruePenalties(address,uint256,uint256,uint256,uint256,uint256)": {"notice": "accrue penalties since last accrual based on all over saturated positions"}, "calcSatChangeRatioBips((uint256[6],uint256,uint256,uint256,uint256,uint256,uint256),uint256,uint256,address,address)": {"notice": "Calculate the ratio by which the saturation has changed for `account`."}, "configLongTermInterval(address,uint24)": {"notice": "Configures the interval of long-term observations."}, "getLendingStateTick(int56,uint32,uint32)": {"notice": "Gets the tick value representing the TWAP since the last lending update."}, "getLendingStateTickAndCheckpoint(uint32,uint32)": {"notice": "Gets the tick value representing the TWAP since the last         lending update and checkpoints the current lending cumulative sum         as `self.lendingCumulativeSum` and the current block timestamp as `self.lastLendingTimestamp`."}, "getTickRange(address,int16,bool)": {"notice": "Gets the min and max range of tick values from the stored oracle observations."}, "init(int16)": {"notice": "initializes the sat and TWAP struct"}, "recordObservation(int16,uint32)": {"notice": "Records a new observation tick value and updates the observation data."}, "update((uint256[6],uint256,uint256,uint256,uint256,uint256,uint256),address)": {"notice": "update the borrow position of an account and potentially check (and revert) if the resulting sat is too high"}}, "version": 1}}, "settings": {"remappings": ["1inch/=lib/1inch/", "@1inch/=lib/1inch/", "@mangrovedao/mangrove-core/=lib/mangrove-core/", "@mgv/forge-std/=lib/mangrove-core/lib/forge-std/src/", "@mgv/lib/=lib/mangrove-core/lib/", "@mgv/script/=lib/mangrove-core/script/", "@mgv/src/=lib/mangrove-core/src/", "@mgv/test/=lib/mangrove-core/test/", "@morpho-org/morpho-blue/=lib/morpho-blue/", "@openzeppelin/=lib/openzeppelin-contracts/", "ExcessivelySafeCall/=lib/ExcessivelySafeCall/src/", "core/=lib/mangrove-core/lib/core/", "ds-test/=lib/ExcessivelySafeCall/lib/ds-test/src/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "forge-std/=lib/openzeppelin-contracts/lib/forge-std/src/", "halmos-cheatcodes/=lib/morpho-blue/lib/halmos-cheatcodes/src/", "mangrove-core/=lib/mangrove-core/", "morpho-blue/=lib/morpho-blue/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "preprocessed/=lib/mangrove-core/lib/preprocessed/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"contracts/SaturationAndGeometricTWAPState.sol": "SaturationAndGeometricTWAPState"}, "evmVersion": "cancun", "libraries": {}}, "sources": {"contracts/SaturationAndGeometricTWAPState.sol": {"keccak256": "0x5e293a35668bb216a99379ea2176894314cc0f1ac68644fcf4c07017da1a4419", "urls": ["bzz-raw://00349bb86f1b657010919b4bc3f616ad56ef4883b99ab0eead36815dae93dc76", "dweb:/ipfs/QmbEd9GD2JxuDntX35YcfbSCcpRstDU9GDPUkBKGzsxvqE"], "license": "GPL-3.0-only"}, "contracts/interfaces/ISaturationAndGeometricTWAPState.sol": {"keccak256": "0xc9add2ad41f8edd9d360ced8d2cd7bd18dd500304794434fb2e309fa0f5af83c", "urls": ["bzz-raw://8ecc810c544ac734ef26a2f6bebea3f3bd12d773965d297991e0e0e72892fa20", "dweb:/ipfs/QmarXc1Ut4FZzPRRZs2M2udbJjuZUJQHQ8fvmSr3bpHErR"], "license": "GPL-3.0-only"}, "contracts/interfaces/tokens/IAmmalgamERC20.sol": {"keccak256": "0x44a376269170b4270ec221ce3cb31a609b394e216cc4d2e27b818361b4369829", "urls": ["bzz-raw://c48bc7586631f27ede73d3d0b4c1d7a29b1653e6c501c8b7fc9877c125f8f57e", "dweb:/ipfs/QmTSLtqnsxr7h7ct524rqYssHUo4qursmCZ7g5q3J1qQPK"], "license": "GPL-3.0-only"}, "contracts/interfaces/tokens/ITokenController.sol": {"keccak256": "0x7778001aaf582fe10005240eb6023b2b6cee3f100b6c2222bf6b9ade93732624", "urls": ["bzz-raw://91e5c4519207d6a450be1e0a8649157e86d20f8ef6a91ff6512a31cf5561a570", "dweb:/ipfs/QmUqZLW27JJZHFPf2fgLDYSWWj5gM158DdaxTTmDVukRAg"], "license": "GPL-3.0-only"}, "contracts/libraries/Convert.sol": {"keccak256": "0x944776d31291de1a9cdc6a52154c23c22b43a01c3edebe7a4140e267edbba975", "urls": ["bzz-raw://36c03749859077ba47a3acfd574f8c30f34f97def4ce81d7f4feac9a7b62794c", "dweb:/ipfs/QmdycZay5X2WrbS8qS7RycLpZbMQx7yKszWQzGU3rqidpH"], "license": "GPL-3.0-only"}, "contracts/libraries/GeometricTWAP.sol": {"keccak256": "0x3860409daa0fdb5d96f0bfb8b49cbca058b9fe32c8e32457f85d4ee2c5cdcb1e", "urls": ["bzz-raw://81fe70a80f4005a6529c7e93f1a92547ce0bf74c357c280a91e8778b378b18aa", "dweb:/ipfs/QmdRQ1DqsCu11zfbLAbrrzJ9Ups3oKgTGimYo3Zm3ALiCz"], "license": "GPL-3.0-only"}, "contracts/libraries/Interest.sol": {"keccak256": "0xbc8bfa20d7295dd70e3c716fd3dbeb5b45d313e3c609d063d186042cbf000646", "urls": ["bzz-raw://b015e8d4976d3b6d7eaca07dfcc487aeed3a7d8b4c41c8369a7476dcfb211194", "dweb:/ipfs/QmecH84UnZYxDZ2aL6rQtnrEExLEAfo7q4Y47yuBXdymeX"], "license": "GPL-3.0-only"}, "contracts/libraries/Liquidation.sol": {"keccak256": "0x842bc44bc3cff80360ab82c5920070b12680edefe9267bdffc2d6c3c3a692d63", "urls": ["bzz-raw://85ecd75568a0729aec06741d0575ed07dad8b7daebd7ba3114a93f6019250877", "dweb:/ipfs/QmQMvWdsPWsQ4t1yv6eyZy5TM7h1EZpSJdt5b8fDLcumCW"], "license": "GPL-3.0-only"}, "contracts/libraries/QuadraticSwapFees.sol": {"keccak256": "0x00f6b7909be4fa1fc1ba426dd8ae659d1c5cb20c79665148898c973f55cfdccb", "urls": ["bzz-raw://c64da0826a9b0ffc08319709f6db03339d22d24deda902a6540393251da0aecb", "dweb:/ipfs/QmSNwBbn2VAS8HPY4hNZusEc4DoKKZAZHtpPdjL9Gz3gs3"], "license": "GPL-3.0-only"}, "contracts/libraries/Saturation.sol": {"keccak256": "0xf44bc610ece4bc7ebdb0730aa6ad69ea47647e19d4c1944c663d2d2eb4f10860", "urls": ["bzz-raw://421fdf8d0b27132bc324a42ede9aaf23b476e5134e1073f84e824620a2a44f20", "dweb:/ipfs/QmbvSfMuMzDmrfPkCAEp7ydtRDWu5EUiXq4MyrGGjFErzE"], "license": "GPL-3.0-only"}, "contracts/libraries/TickMath.sol": {"keccak256": "0x753813c7ed638d22edb71f48f8eb8b4283b3db2ba5b136b5c8909bd37ffa3f12", "urls": ["bzz-raw://04dd5085b72f6d73e1b17f58148e4d03639f654bdc4fdbc173b7c92ff102fc20", "dweb:/ipfs/QmSg4xTQPkngjNxs84428FZdSwH4AUQpwLXaASx7Qev6oG"], "license": "GPL-2.0-or-later"}, "contracts/libraries/Uint16Set.sol": {"keccak256": "0x26a714430fe1618d78386e953153b4bd2bf024baee54453ec9a7a0cc60e1534f", "urls": ["bzz-raw://8667dd78541d656a09678e5f9cce4d49adc805955604ccaaec414e9b241f5e06", "dweb:/ipfs/QmZVWU2CzyDQfGit32HjJxDphBJMKG3d6JRuxbC682Z1gy"], "license": "GPL-3.0-only"}, "contracts/libraries/Validation.sol": {"keccak256": "0x294848b2af973dbcd8b83732a57b67f14fd15e4af0668de05a2928b8eca5a463", "urls": ["bzz-raw://fab25c941e87f6924b31e3f20742ca6b5ec1b7e4251543f4a61567a04ef4d778", "dweb:/ipfs/Qmf4ChH8afdHc3SfXkFPpNGp3e1hscyvnujPAMza3yuXeA"], "license": "GPL-3.0-only"}, "contracts/libraries/constants.sol": {"keccak256": "0x0dfb294985a8f48287ff13e8476718ddb5334b1d8bf6bfa59a5db1dbcf6ca7c4", "urls": ["bzz-raw://4bedcfdb2850cfb22b5daa768ab8125b4ccab97c90068d1d0ad4495bf942b362", "dweb:/ipfs/Qmf9p88yQN2JYRBR5D7q9BLmwhDJWpFk47ZuayrKqCyHat"], "license": "GPL-3.0-only"}, "lib/mangrove-core/lib/core/BitLib.sol": {"keccak256": "0x80f6885268986b9e976b424993aa875cf7aab8464403ed675a86ade9e9be5ee3", "urls": ["bzz-raw://5a31b4e1e0dc95de9a1dbb114c40c44814de5db3e2d857c93c2524a61454f6c8", "dweb:/ipfs/QmRkgE9ue5rGwE6XDnszF2e2meWqAC9nnKM97xKHjHphQr"], "license": "MIT"}, "lib/morpho-blue/src/libraries/MathLib.sol": {"keccak256": "0xa7354cbbcecef7bc0c94b61061c4e5da75515056b8e2db65e826b00d7369744a", "urls": ["bzz-raw://d7419c59bb906fcfa49320b68f265c3200090e5c30b194766256aee70b012e08", "dweb:/ipfs/Qmbo4uaW6XYnudya4bb6RU6riWXFk5M3CWJge5XzTTaEfd"], "license": "GPL-2.0-or-later"}, "lib/openzeppelin-contracts/contracts/access/Ownable.sol": {"keccak256": "0xff6d0bb2e285473e5311d9d3caacb525ae3538a80758c10649a4d61029b017bb", "urls": ["bzz-raw://8ed324d3920bb545059d66ab97d43e43ee85fd3bd52e03e401f020afb0b120f6", "dweb:/ipfs/QmfEckWLmZkDDcoWrkEvMWhms66xwTLff9DDhegYpvHo1a"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/IERC20.sol": {"keccak256": "0xb5d81383d40f4006d1ce4bbad0064e7a930e17302cbe2a745e09cb403f042733", "urls": ["bzz-raw://3fc4a5681c2f00f41f49260a36ae6bbe1121dd93d470ea24d51d556eff2980be", "dweb:/ipfs/QmUBW6TwVWtGP96ka9TfuGivd27kH8CtkXD8RQAAecSFiR"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol": {"keccak256": "0xfe37358f223eddd65d61bb62b0b7bdb69d7101b5ec8d484292b8c1583a153b8a", "urls": ["bzz-raw://28dd43f30af3c12ae0fc08dd031b1250e906ef3c95f63f30fac6fd15aee2a662", "dweb:/ipfs/QmUkSyWsSRx36w1ti7U6qnGnQgJq16wpMhjeJrnyn9AXwG"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol": {"keccak256": "0xbaffa0bcc92adf28a53cc3b68551fc3632cb8f849a0028cb8d5c06e4677715e9", "urls": ["bzz-raw://32e6f8f6b2e883c85e6a602c0882d9962ce2f92406961244e86cd974df815912", "dweb:/ipfs/Qmahvx6fPpecicq1aUE1JihCxV5ep1bfuPukzrxa8Ub5PS"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Permit.sol": {"keccak256": "0x093f32ab700c2b05373387263915a75f5455cdb0f09a7630cc621e27b7b50d04", "urls": ["bzz-raw://d163e6ef21df143969df5557305e8c643a135c7660a678d0c65dca91772114a0", "dweb:/ipfs/QmTZUgiwEro5oLRhbJ2iSWyCqu1JTDekoFHALVUn4eHqYK"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Context.sol": {"keccak256": "0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2", "urls": ["bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12", "dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Panic.sol": {"keccak256": "0xf7fe324703a64fc51702311dc51562d5cb1497734f074e4f483bfb6717572d7a", "urls": ["bzz-raw://c6a5ff4f9fd8649b7ee20800b7fa387d3465bd77cf20c2d1068cd5c98e1ed57a", "dweb:/ipfs/QmVSaVJf9FXFhdYEYeCEfjMVHrxDh5qL4CGkxdMWpQCrqG"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/math/Math.sol": {"keccak256": "0x1225214420c83ebcca88f2ae2b50f053aaa7df7bd684c3e878d334627f2edfc6", "urls": ["bzz-raw://6c5fab4970634f9ab9a620983dc1c8a30153981a0b1a521666e269d0a11399d3", "dweb:/ipfs/QmVRnBC575MESGkEHndjujtR7qub2FzU9RWy9eKLp4hPZB"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/math/SafeCast.sol": {"keccak256": "0x195533c86d0ef72bcc06456a4f66a9b941f38eb403739b00f21fd7c1abd1ae54", "urls": ["bzz-raw://b1d578337048cad08c1c03041cca5978eff5428aa130c781b271ad9e5566e1f8", "dweb:/ipfs/QmPFKL2r9CBsMwmUqqdcFPfHZB2qcs9g1HDrPxzWSxomvy"], "license": "MIT"}}, "version": 1}, "id": 1}