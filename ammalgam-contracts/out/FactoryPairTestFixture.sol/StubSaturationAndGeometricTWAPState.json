{"abi": [{"type": "constructor", "inputs": [{"name": "_stubSaturation", "type": "bool", "internalType": "bool"}, {"name": "_stubTWAP", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "IS_TEST", "inputs": [], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "accruePenalties", "inputs": [{"name": "account", "type": "address", "internalType": "address"}, {"name": "externalLiquidity", "type": "uint256", "internalType": "uint256"}, {"name": "duration", "type": "uint256", "internalType": "uint256"}, {"name": "allAssetsDepositL", "type": "uint256", "internalType": "uint256"}, {"name": "allAssetsBorrowL", "type": "uint256", "internalType": "uint256"}, {"name": "allSharesBorrowL", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "penaltyInBorrowLShares", "type": "uint112", "internalType": "uint112"}, {"name": "accountPenaltyInBorrowLShares", "type": "uint112", "internalType": "uint112"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "boundTick", "inputs": [{"name": "newTick", "type": "int16", "internalType": "int16"}], "outputs": [{"name": "", "type": "int16", "internalType": "int16"}], "stateMutability": "view"}, {"type": "function", "name": "calcSatChangeRatioBips", "inputs": [{"name": "inputParams", "type": "tuple", "internalType": "struct Validation.InputParams", "components": [{"name": "userAssets", "type": "uint256[6]", "internalType": "uint256[6]"}, {"name": "sqrtPriceMinInQ72", "type": "uint256", "internalType": "uint256"}, {"name": "sqrtPriceMaxInQ72", "type": "uint256", "internalType": "uint256"}, {"name": "activeLiquidityScalerInQ72", "type": "uint256", "internalType": "uint256"}, {"name": "activeLiquidityAssets", "type": "uint256", "internalType": "uint256"}, {"name": "reservesXAssets", "type": "uint256", "internalType": "uint256"}, {"name": "reservesYAssets", "type": "uint256", "internalType": "uint256"}]}, {"name": "liqSqrtPriceInXInQ72", "type": "uint256", "internalType": "uint256"}, {"name": "liqSqrtPriceInYInQ72", "type": "uint256", "internalType": "uint256"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address", "internalType": "address"}, {"name": "account", "type": "address", "internalType": "address"}], "outputs": [{"name": "ratioNetXBips", "type": "uint256", "internalType": "uint256"}, {"name": "ratioNetYBips", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "configLongTermInterval", "inputs": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address", "internalType": "address"}, {"name": "_longTermIntervalConfig", "type": "uint24", "internalType": "uint24"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "excludeArtifacts", "inputs": [], "outputs": [{"name": "excludedArtifacts_", "type": "string[]", "internalType": "string[]"}], "stateMutability": "view"}, {"type": "function", "name": "excludeContracts", "inputs": [], "outputs": [{"name": "excludedContracts_", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "excludeSelectors", "inputs": [], "outputs": [{"name": "excludedSelectors_", "type": "tuple[]", "internalType": "struct StdInvariant.FuzzSelector[]", "components": [{"name": "addr", "type": "address", "internalType": "address"}, {"name": "selectors", "type": "bytes4[]", "internalType": "bytes4[]"}]}], "stateMutability": "view"}, {"type": "function", "name": "excludeSenders", "inputs": [], "outputs": [{"name": "excludedSenders_", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "failed", "inputs": [], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "getAccount", "inputs": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address", "internalType": "address"}, {"name": "netDebtX", "type": "bool", "internalType": "bool"}, {"name": "accountAddress", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "tuple", "internalType": "struct Saturation.Account", "components": [{"name": "exists", "type": "bool", "internalType": "bool"}, {"name": "lastTranche", "type": "int16", "internalType": "int16"}, {"name": "penaltyInBorrowLShares", "type": "uint112", "internalType": "uint112"}, {"name": "satPairPerTranche", "type": "tuple[]", "internalType": "struct Saturation.SaturationPair[]", "components": [{"name": "satInLAssets", "type": "uint128", "internalType": "uint128"}, {"name": "satRelativeToL", "type": "uint128", "internalType": "uint128"}]}, {"name": "treePenaltyAtOnsetInBorrowLSharesPerSatInQ72PerTranche", "type": "uint256[]", "internalType": "uint256[]"}]}], "stateMutability": "view"}, {"type": "function", "name": "getLeafDetails", "inputs": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address", "internalType": "address"}, {"name": "netDebtX", "type": "bool", "internalType": "bool"}, {"name": "leafIndex", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "saturation", "type": "tuple", "internalType": "struct Saturation.SaturationPair", "components": [{"name": "satInLAssets", "type": "uint128", "internalType": "uint128"}, {"name": "satRelativeToL", "type": "uint128", "internalType": "uint128"}]}, {"name": "currentPenaltyInBorrowLSharesPerSatInQ72", "type": "uint256", "internalType": "uint256"}, {"name": "tranches", "type": "uint16[]", "internalType": "uint16[]"}], "stateMutability": "view"}, {"type": "function", "name": "getLendingStateTick", "inputs": [{"name": "newTick", "type": "int56", "internalType": "int56"}, {"name": "timeElapsedSinceUpdate", "type": "uint32", "internalType": "uint32"}, {"name": "timeElapsedSinceLendingUpdate", "type": "uint32", "internalType": "uint32"}], "outputs": [{"name": "lendingStateTick", "type": "int16", "internalType": "int16"}, {"name": "maxSatInWads", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getLendingStateTickAndCheckpoint", "inputs": [{"name": "timeElapsedSinceUpdate", "type": "uint32", "internalType": "uint32"}, {"name": "timeElapsedSinceLendingUpdate", "type": "uint32", "internalType": "uint32"}], "outputs": [{"name": "lendingStateTick", "type": "int16", "internalType": "int16"}, {"name": "maxSatInWads", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "getObservations", "inputs": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "tuple", "internalType": "struct GeometricTWAP.Observations", "components": [{"name": "isMidTermBufferInitialized", "type": "bool", "internalType": "bool"}, {"name": "isLongTermBufferInitialized", "type": "bool", "internalType": "bool"}, {"name": "midTermIndex", "type": "uint8", "internalType": "uint8"}, {"name": "longTermIndex", "type": "uint8", "internalType": "uint8"}, {"name": "lastTick", "type": "int16", "internalType": "int16"}, {"name": "lastLendingStateTick", "type": "int16", "internalType": "int16"}, {"name": "midTermIntervalConfig", "type": "uint24", "internalType": "uint24"}, {"name": "longTermIntervalConfig", "type": "uint24", "internalType": "uint24"}, {"name": "lendingCumulativeSum", "type": "int56", "internalType": "int56"}, {"name": "midTermCumulativeSum", "type": "int56[51]", "internalType": "int56[51]"}, {"name": "longTermCumulativeSum", "type": "int56[9]", "internalType": "int56[9]"}, {"name": "midTermTimeInterval", "type": "uint32[51]", "internalType": "uint32[51]"}, {"name": "longTermTimeInterval", "type": "uint32[9]", "internalType": "uint32[9]"}]}], "stateMutability": "view"}, {"type": "function", "name": "getObservedMidTermTick", "inputs": [{"name": "isLongTermBufferInitialized", "type": "bool", "internalType": "bool"}], "outputs": [{"name": "", "type": "int16", "internalType": "int16"}], "stateMutability": "view"}, {"type": "function", "name": "getTickRange", "inputs": [{"name": "pair", "type": "address", "internalType": "address"}, {"name": "currentTick", "type": "int16", "internalType": "int16"}, {"name": "includeLongTermTick", "type": "bool", "internalType": "bool"}], "outputs": [{"name": "", "type": "int16", "internalType": "int16"}, {"name": "", "type": "int16", "internalType": "int16"}], "stateMutability": "view"}, {"type": "function", "name": "getTrancheDetails", "inputs": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address", "internalType": "address"}, {"name": "netDebtX", "type": "bool", "internalType": "bool"}, {"name": "tranche", "type": "int16", "internalType": "int16"}], "outputs": [{"name": "leaf", "type": "uint16", "internalType": "uint16"}, {"name": "saturation", "type": "tuple", "internalType": "struct Saturation.SaturationPair", "components": [{"name": "satInLAssets", "type": "uint128", "internalType": "uint128"}, {"name": "satRelativeToL", "type": "uint128", "internalType": "uint128"}]}], "stateMutability": "view"}, {"type": "function", "name": "getTreeDetails", "inputs": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address", "internalType": "address"}, {"name": "netDebtX", "type": "bool", "internalType": "bool"}], "outputs": [{"name": "", "type": "uint16", "internalType": "uint16"}, {"name": "", "type": "uint128", "internalType": "uint128"}], "stateMutability": "view"}, {"type": "function", "name": "init", "inputs": [{"name": "firstTick", "type": "int16", "internalType": "int16"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "liquidationCheckHardPremiums", "inputs": [{"name": "inputParams", "type": "tuple", "internalType": "struct Validation.InputParams", "components": [{"name": "userAssets", "type": "uint256[6]", "internalType": "uint256[6]"}, {"name": "sqrtPriceMinInQ72", "type": "uint256", "internalType": "uint256"}, {"name": "sqrtPriceMaxInQ72", "type": "uint256", "internalType": "uint256"}, {"name": "activeLiquidityScalerInQ72", "type": "uint256", "internalType": "uint256"}, {"name": "activeLiquidityAssets", "type": "uint256", "internalType": "uint256"}, {"name": "reservesXAssets", "type": "uint256", "internalType": "uint256"}, {"name": "reservesYAssets", "type": "uint256", "internalType": "uint256"}]}, {"name": "borrower", "type": "address", "internalType": "address"}, {"name": "hardLiquidationParams", "type": "tuple", "internalType": "struct Liquidation.HardLiquidationParams", "components": [{"name": "depositLToBeTransferredInLAssets", "type": "uint256", "internalType": "uint256"}, {"name": "depositXToBeTransferredInXAssets", "type": "uint256", "internalType": "uint256"}, {"name": "depositYToBeTransferredInYAssets", "type": "uint256", "internalType": "uint256"}, {"name": "repayLXInXAssets", "type": "uint256", "internalType": "uint256"}, {"name": "repayLYInYAssets", "type": "uint256", "internalType": "uint256"}, {"name": "repayXInXAssets", "type": "uint256", "internalType": "uint256"}, {"name": "repayYInYAssets", "type": "uint256", "internalType": "uint256"}]}, {"name": "actualRepaidLiquidityAssets", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "badDebt", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "longTermIntervalConfig", "inputs": [], "outputs": [{"name": "", "type": "uint24", "internalType": "uint24"}], "stateMutability": "view"}, {"type": "function", "name": "midTermIntervalConfig", "inputs": [], "outputs": [{"name": "", "type": "uint24", "internalType": "uint24"}], "stateMutability": "view"}, {"type": "function", "name": "owner", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "recordObservation", "inputs": [{"name": "newTick", "type": "int16", "internalType": "int16"}, {"name": "timeElapsed", "type": "uint32", "internalType": "uint32"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "renounceOwnership", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setNewPositionSaturation", "inputs": [{"name": "pair", "type": "address", "internalType": "address"}, {"name": "maxDesiredSaturationMag2", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setTickRange", "inputs": [{"name": "tickMin", "type": "int16", "internalType": "int16"}, {"name": "tickMax", "type": "int16", "internalType": "int16"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "targetArtifactSelectors", "inputs": [], "outputs": [{"name": "targetedArtifactSelectors_", "type": "tuple[]", "internalType": "struct StdInvariant.FuzzArtifactSelector[]", "components": [{"name": "artifact", "type": "string", "internalType": "string"}, {"name": "selectors", "type": "bytes4[]", "internalType": "bytes4[]"}]}], "stateMutability": "view"}, {"type": "function", "name": "targetArtifacts", "inputs": [], "outputs": [{"name": "targetedArtifacts_", "type": "string[]", "internalType": "string[]"}], "stateMutability": "view"}, {"type": "function", "name": "targetContracts", "inputs": [], "outputs": [{"name": "targetedContracts_", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "targetInterfaces", "inputs": [], "outputs": [{"name": "targetedInterfaces_", "type": "tuple[]", "internalType": "struct StdInvariant.FuzzInterface[]", "components": [{"name": "addr", "type": "address", "internalType": "address"}, {"name": "artifacts", "type": "string[]", "internalType": "string[]"}]}], "stateMutability": "view"}, {"type": "function", "name": "targetSelectors", "inputs": [], "outputs": [{"name": "targetedSelectors_", "type": "tuple[]", "internalType": "struct StdInvariant.FuzzSelector[]", "components": [{"name": "addr", "type": "address", "internalType": "address"}, {"name": "selectors", "type": "bytes4[]", "internalType": "bytes4[]"}]}], "stateMutability": "view"}, {"type": "function", "name": "targetSenders", "inputs": [], "outputs": [{"name": "targetedSenders_", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "transferOwnership", "inputs": [{"name": "new<PERSON>wner", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "update", "inputs": [{"name": "inputParams", "type": "tuple", "internalType": "struct Validation.InputParams", "components": [{"name": "userAssets", "type": "uint256[6]", "internalType": "uint256[6]"}, {"name": "sqrtPriceMinInQ72", "type": "uint256", "internalType": "uint256"}, {"name": "sqrtPriceMaxInQ72", "type": "uint256", "internalType": "uint256"}, {"name": "activeLiquidityScalerInQ72", "type": "uint256", "internalType": "uint256"}, {"name": "activeLiquidityAssets", "type": "uint256", "internalType": "uint256"}, {"name": "reservesXAssets", "type": "uint256", "internalType": "uint256"}, {"name": "reservesYAssets", "type": "uint256", "internalType": "uint256"}]}, {"name": "account", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "event", "name": "OwnershipTransferred", "inputs": [{"name": "previousOwner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "new<PERSON>wner", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "UpdateLendingTick", "inputs": [{"name": "lendingStateTick", "type": "int16", "indexed": false, "internalType": "int16"}], "anonymous": false}, {"type": "event", "name": "log", "inputs": [{"name": "", "type": "string", "indexed": false, "internalType": "string"}], "anonymous": false}, {"type": "event", "name": "log_address", "inputs": [{"name": "", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "log_array", "inputs": [{"name": "val", "type": "uint256[]", "indexed": false, "internalType": "uint256[]"}], "anonymous": false}, {"type": "event", "name": "log_array", "inputs": [{"name": "val", "type": "int256[]", "indexed": false, "internalType": "int256[]"}], "anonymous": false}, {"type": "event", "name": "log_array", "inputs": [{"name": "val", "type": "address[]", "indexed": false, "internalType": "address[]"}], "anonymous": false}, {"type": "event", "name": "log_bytes", "inputs": [{"name": "", "type": "bytes", "indexed": false, "internalType": "bytes"}], "anonymous": false}, {"type": "event", "name": "log_bytes32", "inputs": [{"name": "", "type": "bytes32", "indexed": false, "internalType": "bytes32"}], "anonymous": false}, {"type": "event", "name": "log_int", "inputs": [{"name": "", "type": "int256", "indexed": false, "internalType": "int256"}], "anonymous": false}, {"type": "event", "name": "log_named_address", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "log_named_array", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "uint256[]", "indexed": false, "internalType": "uint256[]"}], "anonymous": false}, {"type": "event", "name": "log_named_array", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "int256[]", "indexed": false, "internalType": "int256[]"}], "anonymous": false}, {"type": "event", "name": "log_named_array", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "address[]", "indexed": false, "internalType": "address[]"}], "anonymous": false}, {"type": "event", "name": "log_named_bytes", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "bytes", "indexed": false, "internalType": "bytes"}], "anonymous": false}, {"type": "event", "name": "log_named_bytes32", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "bytes32", "indexed": false, "internalType": "bytes32"}], "anonymous": false}, {"type": "event", "name": "log_named_decimal_int", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "int256", "indexed": false, "internalType": "int256"}, {"name": "decimals", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "log_named_decimal_uint", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "decimals", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "log_named_int", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "int256", "indexed": false, "internalType": "int256"}], "anonymous": false}, {"type": "event", "name": "log_named_string", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "string", "indexed": false, "internalType": "string"}], "anonymous": false}, {"type": "event", "name": "log_named_uint", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "log_string", "inputs": [{"name": "", "type": "string", "indexed": false, "internalType": "string"}], "anonymous": false}, {"type": "event", "name": "log_uint", "inputs": [{"name": "", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "logs", "inputs": [{"name": "", "type": "bytes", "indexed": false, "internalType": "bytes"}], "anonymous": false}, {"type": "error", "name": "AmmalgamMaxSlippage", "inputs": []}, {"type": "error", "name": "CannotUpdateZeroAddress", "inputs": []}, {"type": "error", "name": "InvalidIntervalConfig", "inputs": []}, {"type": "error", "name": "InvalidUserConfiguration", "inputs": []}, {"type": "error", "name": "LiquidationPremiumTooHigh", "inputs": []}, {"type": "error", "name": "MaxTrancheOverSaturated", "inputs": []}, {"type": "error", "name": "OwnableInvalidOwner", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "OwnableUnauthorizedAccount", "inputs": [{"name": "account", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "PairAlreadyExists", "inputs": []}, {"type": "error", "name": "PairDoesNotExist", "inputs": []}, {"type": "error", "name": "PriceOutOfBounds", "inputs": []}, {"type": "error", "name": "SafeCastOverflowedUintDowncast", "inputs": [{"name": "bits", "type": "uint8", "internalType": "uint8"}, {"name": "value", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "TickOutOfBounds", "inputs": []}], "bytecode": {"object": "0x60c060405260118054600160ff19918216811790925560248054909116909117905534801561002c575f5ffd5b50604051616cb3380380616cb383398101604081905261004b91610120565b60086070338061007457604051631e4fbdf760e01b81525f600482015260240160405180910390fd5b61007d816100bd565b5062ffffff9182166080521660a0526024805462ffff0019166101009315159390930262ff00001916929092176201000091151591909102179055610151565b5f80546001600160a01b038381166001600160a01b0319831681178455604051919092169283917f8be0079c531659141344cd1fd0a4f28419497f9722a3daafe3b4186f6b6457e09190a35050565b8051801515811461011b575f5ffd5b919050565b5f5f60408385031215610131575f5ffd5b61013a8361010c565b91506101486020840161010c565b90509250929050565b60805160a051616b336101805f395f81816103f101526107bf01525f818161046f015261079e0152616b335ff3fe608060405234801561000f575f5ffd5b5060043610610212575f3560e01c80636ef9c9681161011f578063b0464fdc116100a9578063df31e2cc11610079578063df31e2cc14610540578063e20c9f7114610553578063ecabd7aa1461055b578063f2fde38b14610588578063fa7626d41461059b575f5ffd5b8063b0464fdc14610515578063b5508aa91461051d578063ba414fa614610525578063beca5a671461052d575f5ffd5b806388205b40116100ef57806388205b401461046a5780638da5cb5b146104915780638ecc5eda146104ab578063916a17c6146104cb578063b0016809146104e0575f5ffd5b80636ef9c96814610427578063715018a61461043a578063807cca851461044257806385226c8114610455575f5ffd5b80632ade3880116101a05780634d003c0e116101705780634d003c0e146103615780635cb5fb0a14610383578063668243a7146103b657806366d9a9a0146103d75780636c8c3191146103ec575f5ffd5b80632ade3880146103165780633d011d651461032b5780633e5e3c23146103515780633f7286f414610359575f5ffd5b806313ed39d9116101e657806313ed39d914610290578063159e7cd7146102b857806315f0cc2f146102db5780631b060ac7146102ee5780631ed7831c14610301575f5ffd5b8062ba55ec1461021657806301cbfeb214610248578063********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", "sourceMap": "11299:2314:177:-:0;;;3126:44:97;;;3166:4;-1:-1:-1;;3126:44:97;;;;;;;;1016:26:107;;;;;;;;;;;11499:248:177;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;2941:1:30;3387:3;1184:10:1;;1269:95:53;;1322:31;;-1:-1:-1;;;1322:31:53;;1350:1;1322:31;;;615:51:190;588:18;;1322:31:53;;;;;;;1269:95;1373:32;1392:12;1373:18;:32::i;:::-;-1:-1:-1;1206:46:1::1;::::0;;::::1;;::::0;1262:48:::1;;::::0;11678:14:177::1;:32:::0;;-1:-1:-1;;11720:20:177;11678:32:::1;::::0;::::1;;::::0;;;::::1;-1:-1:-1::0;;11720:20:177;;;;;;;::::1;;::::0;;;::::1;;::::0;;11299:2314;;2912:187:53;2985:16;3004:6;;-1:-1:-1;;;;;3020:17:53;;;-1:-1:-1;;;;;;3020:17:53;;;;;;3052:40;;3004:6;;;;;;;3052:40;;2985:16;3052:40;2975:124;2912:187;:::o;14:164:190:-;90:13;;139;;132:21;122:32;;112:60;;168:1;165;158:12;112:60;14:164;;;:::o;183:281::-;256:6;264;317:2;305:9;296:7;292:23;288:32;285:52;;;333:1;330;323:12;285:52;356:37;383:9;356:37;:::i;:::-;346:47;;412:46;454:2;443:9;439:18;412:46;:::i;:::-;402:56;;183:281;;;;;:::o;469:203::-;11299:2314:177;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "0x608060405234801561000f575f5ffd5b5060043610610212575f3560e01c80636ef9c9681161011f578063b0464fdc116100a9578063df31e2cc11610079578063df31e2cc14610540578063e20c9f7114610553578063ecabd7aa1461055b578063f2fde38b14610588578063fa7626d41461059b575f5ffd5b8063b0464fdc14610515578063b5508aa91461051d578063ba414fa614610525578063beca5a671461052d575f5ffd5b806388205b40116100ef57806388205b401461046a5780638da5cb5b146104915780638ecc5eda146104ab578063916a17c6146104cb578063b0016809146104e0575f5ffd5b80636ef9c96814610427578063715018a61461043a578063807cca851461044257806385226c8114610455575f5ffd5b80632ade3880116101a05780634d003c0e116101705780634d003c0e146103615780635cb5fb0a14610383578063668243a7146103b657806366d9a9a0146103d75780636c8c3191146103ec575f5ffd5b80632ade3880146103165780633d011d651461032b5780633e5e3c23146103515780633f7286f414610359575f5ffd5b806313ed39d9116101e657806313ed39d914610290578063159e7cd7146102b857806315f0cc2f146102db5780631b060ac7146102ee5780631ed7831c14610301575f5ffd5b8062ba55ec1461021657806301cbfeb214610248578063********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", "sourceMap": "11299:2314:177:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;14408:562:1;;;;;;:::i;:::-;;:::i;:::-;;;;899:1:190;888:21;;;;870:40;;941:2;926:18;;919:34;;;;843:18;14408:562:1;;;;;;;;4615:242;;;;;;:::i;:::-;;:::i;:::-;;;;;;;:::i;1664:623::-;;;;;;:::i;:::-;;:::i;:::-;;8889:214;;;;;;:::i;:::-;;:::i;12829:782:177:-;;;;;;:::i;:::-;;:::i;:::-;;;;7085:25:190;;;7141:2;7126:18;;7119:34;;;;7058:18;12829:782:177;6911:248:190;14996:1496:1;;;;;;:::i;:::-;;:::i;:::-;;;8784:14:190;;8777:22;8759:41;;8747:2;8732:18;14996:1496:1;8619:187:190;12345:489:1;;;;;;:::i;:::-;;:::i;2312:419::-;;;;;;:::i;:::-;;:::i;2907:134:100:-;;;:::i;:::-;;;;;;;:::i;3823:151::-;;;:::i;:::-;;;;;;;:::i;13104:230:1:-;;;;;;:::i;:::-;;:::i;:::-;;;12293:1:190;12282:21;;;;12264:40;;12252:2;12237:18;13104:230:1;12122:188:190;3684:133:100;;;:::i;3385:141::-;;;:::i;3396:594:1:-;;;;;;:::i;:::-;;:::i;:::-;;;;;;;;;:::i;6074:656::-;;;;;;:::i;:::-;;:::i;:::-;;;;-1:-1:-1;;;;;14536:43:190;;;14518:62;;14616:43;;;;14611:2;14596:18;;14589:71;14491:18;6074:656:1;14344:322:190;4242:367:1;;;;;;:::i;:::-;;:::i;:::-;;;;;;;;:::i;3193:186:100:-;;;:::i;:::-;;;;;;;:::i;667:46:1:-;;;;;;;;17226:8:190;17214:21;;;17196:40;;17184:2;17169:18;667:46:1;17052:190:190;10048:208:1;;;;;;:::i;:::-;;:::i;2293:101:53:-;;;:::i;12026:275:177:-;;;;;;:::i;:::-;;:::i;3047:140:100:-;;;:::i;:::-;;;;;;;:::i;616:45:1:-;;;;;1638:85:53;1684:7;1710:6;1638:85;;-1:-1:-1;;;;;1710:6:53;;;18194:51:190;;18182:2;18167:18;1638:85:53;18048:203:190;8068:172:1;;;;;;:::i;:::-;;:::i;:::-;;;;;;;:::i;3532:146:100:-;;;:::i;:::-;;;;;;;:::i;3996:240:1:-;;;;;;:::i;:::-;;:::i;:::-;;;;23179:6:190;23167:19;;;23149:38;;-1:-1:-1;;;;;23223:47:190;;;23218:2;23203:18;;23196:75;23122:18;3996:240:1;22977:300:190;2754:147:100;;;:::i;2459:141::-;;;:::i;1243:204:96:-;;;:::i;13667:165:1:-;;;;;;:::i;:::-;;:::i;11771:237:177:-;;;;;;:::i;:::-;;:::i;2606:142:100:-;;;:::i;12307:516:177:-;;;;;;:::i;:::-;;:::i;:::-;;;;24123:1:190;24112:21;;;24094:40;;24170:21;;;;24165:2;24150:18;;24143:49;24067:18;12307:516:177;23928:270:190;2543:215:53;;;;;;:::i;:::-;;:::i;1016:26:107:-;;;;;;;;;14408:562:1;14784:10;14574:22;14766:29;;;:17;:29;;;;;14574:22;;14719:156;;14797:7;14806:22;14830:29;14861:4;14719:33;:156::i;:::-;-1:-1:-1;14951:10:1;14934:28;;;;:16;:28;;;;;14697:178;;-1:-1:-1;14900:63:1;;:33;:63::i;:::-;14885:78;;14408:562;;;;;;:::o;4615:242::-;-1:-1:-1;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4792:30:1;4800:11;4813:8;4792:7;:30::i;:::-;-1:-1:-1;;;;;4792:58:1;;;;;;:42;;;;;:58;;;;;;;;4785:65;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;4785:65:1;;;;;;;;;;;;;;;;;;;;;;;;;;4792:58;;4785:65;;;;;;;;4792:58;4785:65;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;4785:65:1;;;;;-1:-1:-1;;;4785:65:1;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4615:242;;;;;;:::o;1664:623::-;1750:10;1732:29;;;;:17;:29;;;;;;;;1728:61;;;1770:19;;-1:-1:-1;;;1770:19:1;;;;;;;;;;;1728:61;1864:10;1799:45;1847:28;;;:16;:28;;;;;;;;1935:17;:29;;;;;;1975:48;1847:28;1975:37;:48::i;:::-;2033:102;2075:12;2089:21;2112:22;2033:41;:102::i;:::-;2163:10;2145:29;;;;:17;:29;;;;;;;;:36;;-1:-1:-1;;2145:36:1;2177:4;2145:36;;;2239:17;:29;;;;;2192:88;;2270:9;2192:46;:88::i;:::-;1718:569;;1664:623;:::o;8889:214::-;1531:13:53;:11;:13::i;:::-;-1:-1:-1;;;;;9040:30:1;::::1;;::::0;;;:17:::1;:30;::::0;;;;9003:93:::1;::::0;9072:23;9003:36:::1;:93::i;:::-;8889:214:::0;;:::o;12829:782:177:-;13149:14;;13088:21;;;;13149:14;;;;;13144:404;;-1:-1:-1;;;;;13227:29:177;;13179:45;13227:29;;;:16;:29;;;;;;13304:46;13244:11;13342:7;13304:24;:46::i;:::-;13270:80;;13372:165;13423:9;13434:11;13447:20;13469;13491:7;13500:23;13372:33;:165::i;:::-;13365:172;;;;;;13144:404;12829:782;;;;;;;;:::o;14996:1496:1:-;15407:29;;;;;15478;;;;;15446:61;;;15517:60;;;;15257:12;;;;;15674:123;15407:11;15746:21;15769:27;15674:58;:123::i;:::-;15932:10;15809:24;15915:28;;;:16;:28;;;;;15588:209;;-1:-1:-1;15588:209:1;;-1:-1:-1;15588:209:1;-1:-1:-1;15809:24:1;;;15859:239;;15957:11;15982:8;15588:209;;;15859:42;:239::i;:::-;15808:290;;;;16109:23;16147:99;16177:22;16201:26;16229:16;16147:29;:99::i;:::-;16109:137;;16267:18;:37;;;;;16289:15;16267:37;16386:29;;;;;16354;;;;:61;;;;16425:60;;;;-1:-1:-1;16257:47:1;;14996:1496;-1:-1:-1;;;;;;;;;14996:1496:1:o;12345:489::-;12510:22;12534:20;1358:16;:14;:16::i;:::-;12663:10:::1;12645:29;::::0;;;:17:::1;:29;::::0;;;;12585:154:::1;::::0;12676:22;12700:29;12585:46:::1;:154::i;:::-;12815:10;12798:28;::::0;;;:16:::1;:28;::::0;;;;12566:173;;-1:-1:-1;12764:63:1::1;::::0;:33:::1;:63::i;:::-;12749:78;;12345:489:::0;;;;;:::o;2312:419::-;-1:-1:-1;;;;;2418:23:1;;;;;;:17;:23;;;;;;;;2413:55;;2450:18;;-1:-1:-1;;;2450:18:1;;;;;;;;;;;2413:55;6863:2:25;2482:24:1;:65;:98;;;-1:-1:-1;2551:29:1;;2482:98;2478:162;;;2603:26;;-1:-1:-1;;;2603:26:1;;;;;;;;;;;2478:162;-1:-1:-1;;;;;2649:36:1;;;;;;;:30;:36;;;;;;;;2686:10;2649:48;;;;;;;:75;2312:419::o;2907:134:100:-;2954:33;3018:16;2999:35;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;2999:35:100;;;;;;;;;;;;;;;;;;;;;;;2907:134;:::o;3823:151::-;3872:42;3948:19;3926:41;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;3926:41:100;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3823:151;:::o;13104:230:1:-;13286:10;13207:5;13268:29;;;:17;:29;;;;;13231:96;;13299:27;13231:36;:96::i;:::-;13224:103;13104:230;-1:-1:-1;;13104:230:1:o;3684:133:100:-;3730:33;3794:16;3775:35;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;3775:35:100;;;;;;;;;;;;;;;;;;;;;;3684:133;:::o;3385:141::-;3433:35;3501:18;3480:39;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;3480:39:100;;;;;;;;;;;;;;;;;;;;;;3385:141;:::o;3396:594:1:-;-1:-1:-1;;;;;;;;;;;;;;;;;3622:48:1;3684:24;3733:28;3764:30;3772:11;3785:8;3764:7;:30::i;:::-;:36;;3801:9;3764:47;;;;;;;:::i;:::-;3821:29;;;;;;;;3764:47;;;;;;;;;3834:16;;;3821:29;-1:-1:-1;;;;;3821:29:1;;;;;-1:-1:-1;;;3821:29:1;;;;;;;;;;;;3903:38;;;;3821:29;3962:21;;3951:32;;;;;;;;;;;;;;;;;3821:29;;-1:-1:-1;3903:38:1;;-1:-1:-1;3764:47:1;;-1:-1:-1;3962:21:1;;3951:32;;;3962:21;3951:32;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3723:267;3396:594;;;;;;;:::o;6074:656::-;6326:30;6358:37;1358:16;:14;:16::i;:::-;6537:10:::1;6520:28;::::0;;;:16:::1;:28;::::0;;;;6480:243:::1;::::0;6562:7;6583:17;6614:8;6636:17;6667:16;6697;6480:26:::1;:243::i;:::-;6473:250;;;;1384:1;6074:656:::0;;;;;;;;;:::o;4242:367::-;4373:11;4386:43;-1:-1:-1;;;;;;;;;;;;;;;;;;;4386:43:1;4441:28;4472:30;4480:11;4493:8;4472:7;:30::i;:::-;4519:27;;;;;;;;;:18;;;:27;;;;;;;;;4569:24;;;;:33;;;;;;4556:46;;;;;;;;;-1:-1:-1;;;;;4556:46:1;;;;;-1:-1:-1;;;4556:46:1;;;;;;;;4519:27;;;;;4556:46;;-1:-1:-1;4242:367:1;;-1:-1:-1;;;;4242:367:1:o;3193:186:100:-;3249:56;3346:26;3317:55;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;3317:55:100;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;10048:208:1;10142:4;1358:16;:14;:16::i;:::-;10215:10:::1;10197:29;::::0;;;:17:::1;:29;::::0;;;;10165:84:::1;::::0;10228:7;10237:11;10165:31:::1;:84::i;2293:101:53:-:0;1531:13;:11;:13::i;:::-;2357:30:::1;2384:1;2357:18;:30::i;:::-;2293:101::o:0;12026:275:177:-;12105:8;;;;;;;12097:111;;;;-1:-1:-1;;;12097:111:177;;24922:2:190;12097:111:177;;;24904:21:190;24961:2;24941:18;;;24934:30;25000:34;24980:18;;;24973:62;25071:34;25051:18;;;25044:62;25143:28;25122:19;;;25115:57;25189:19;;12097:111:177;;;;;;;;;12219:12;:19;;12248:18;12276;;;;;-1:-1:-1;;12248:18:177;;;;;;-1:-1:-1;;12248:18:177;;;;-1:-1:-1;;;12248:18:177;12276;;;;;;;;;;12026:275::o;3047:140:100:-;3095:34;3162:18;3141:39;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8068:172:1;8151:33;;:::i;:::-;-1:-1:-1;;;;;8203:30:1;;;;;;:17;:30;;;;;;;;8196:37;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;8196:37:1;;;;;;;;;;-1:-1:-1;;;8196:37:1;;;;;;;;;-1:-1:-1;;;8196:37:1;;;;;;;;-1:-1:-1;;;8196:37:1;;;;;;;-1:-1:-1;;;8196:37:1;;;;;;;;;;;;;;;;;;;;;;;8203:30;;8196:37;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;8196:37:1;;;-1:-1:-1;;8196:37:1;;;;;;;;;;;;;;;;-1:-1:-1;8196:37:1;;;;;;;-1:-1:-1;8196:37:1;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;8196:37:1;;;-1:-1:-1;;8196:37:1;;;;;;;;;;;;;;;;-1:-1:-1;8196:37:1;;;;;;;-1:-1:-1;8196:37:1;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;8196:37:1;;;-1:-1:-1;;8196:37:1;;;;;;;;;;;;;;;;-1:-1:-1;8196:37:1;;;;;;;-1:-1:-1;8196:37:1;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8068:172;;;:::o;3532:146:100:-;3580:40;3653:18;3632:39;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;3632:39:100;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;3632:39:100;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3996:240:1;4079:6;4087:7;4106:28;4137:30;4145:11;4158:8;4137:7;:30::i;:::-;4185:19;;;;;;;4206:22;;;;-1:-1:-1;;;;;4206:22:1;;-1:-1:-1;3996:240:1;-1:-1:-1;;;;3996:240:1:o;2754:147:100:-;2803:40;2876:18;2855:39;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;2855:39:100;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;2855:39:100;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2459:141;2508:34;2575:18;2554:39;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1243:204:96;1302:7;;1282:4;;1302:7;;1298:143;;;-1:-1:-1;1332:7:96;;;;;1243:204::o;1298:143::-;1377:39;;-1:-1:-1;;;1377:39:96;;:7;:39;;;25393:51:190;;;-1:-1:-1;;;25460:18:190;;;25453:34;1428:1:96;;1377:7;;25366:18:190;;1377:39:96;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;:53;;1370:60;;1243:204;:::o;13667:165:1:-;13804:10;13738:5;13786:29;;;:17;:29;;;;;13762:63;;13817:7;13762:23;:63::i;11771:237:177:-;11878:14;;;;;;;11873:82;;11915:29;11923:11;11936:7;11915;:29::i;2606:142:100:-;2655:35;2723:18;2702:39;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;2702:39:100;;;;;;;;;;;;;;;;;;;;;;2606:142;:::o;12307:516:177:-;12476:8;;12448:5;;;;12476:8;;;;;12472:214;;;12504:12;;-1:-1:-1;;;12504:12:177;;;;12500:176;;;-1:-1:-1;;12544:8:177;;;;;;;;;;12554;;;;;12536:27;;12500:176;12588:11;:16;;12603:1;12588:16;12584:92;;12632:11;12645:15;12632:11;12659:1;12645:15;:::i;:::-;12624:37;;;;;;12584:92;12696:13;12711;12728:53;12742:4;12748:11;12761:19;12728:13;:53::i;:::-;12695:86;;-1:-1:-1;12695:86:177;-1:-1:-1;;;12307:516:177;;;;;;;:::o;2543:215:53:-;1531:13;:11;:13::i;:::-;-1:-1:-1;;;;;2627:22:53;::::1;2623:91;;2672:31;::::0;-1:-1:-1;;;2672:31:53;;2700:1:::1;2672:31;::::0;::::1;18194:51:190::0;18167:18;;2672:31:53::1;18048:203:190::0;2623:91:53::1;2723:28;2742:8;2723:18;:28::i;:::-;2543:215:::0;:::o;18563:909:21:-;18873:17;;18792:5;;;;;;18845:46;;18873:17;;;;;;;18845:21;:46::i;:::-;18816:75;;18906:13;:69;;;;-1:-1:-1;18949:26:21;;-1:-1:-1;;;18949:26:21;;;;18923:52;;;;;18906:69;18902:284;;;19130:30;;;19107:54;;19083:78;18902:284;19218:34;;;;19217:204;;19364:25;;19316:105;;19342:20;;-1:-1:-1;;;19364:25:21;;;;19391:29;19316:25;:105::i;:::-;19217:204;;;19272:25;;-1:-1:-1;;;19272:25:21;;;;19217:204;19196:269;19435:20;;-1:-1:-1;18563:909:21;-1:-1:-1;;;;;;18563:909:21:o;82009:993:25:-;82191:33;;82226:18;;;:33;82114:28;;;;82182:78;;82191:33;;;;;;;;;82226;;;;;82182:8;:78::i;:::-;82275:17;;;;82154:107;;-1:-1:-1;82275:17:25;;:22;:42;;;;-1:-1:-1;82301:16:25;;;;82275:42;82271:81;;;-1:-1:-1;82340:1:25;;82009:993;-1:-1:-1;;82009:993:25:o;82271:81::-;82376:17;;;;82361:12;;82376:31;;82396:11;;82376:17;;:31;:::i;:::-;82361:46;;82429:1;82421:5;:9;;;82417:48;;;-1:-1:-1;82453:1:25;;82009:993;-1:-1:-1;;;82009:993:25:o;82417:48::-;82505:5;82528:1;82523:61;;;;82602:1;82597:61;;;;82676:1;82671:61;;;;82750:1;82745:61;;;;82824:1;82819:61;;;;82927:32;82903:56;;82498:463;;82523:61;82556:26;82532:50;;82523:61;;82597;82630:26;82606:50;;82597:61;;82671;82704:26;82680:50;;82671:61;;82745;82778:26;82754:50;;82745:61;;82819;82852:26;82828:50;;82498:463;;82484:512;;82009:993;;;:::o;3129:261:1:-;-1:-1:-1;;;;;3287:29:1;;3204:23;3287:29;;;:16;:29;;;;;3333:8;:50;;3365:9;:18;;3333:50;;;3344:9;3333:50;3326:57;3129:261;-1:-1:-1;;;;3129:261:1:o;14206:354:25:-;14344:28;14353:9;14344:8;:28::i;:::-;14415;14424:9;:18;;14415:8;:28::i;:::-;14523:30;;-1:-1:-1;;14523:30:25;14549:4;14523:30;;;14206:354::o;4643:571:21:-;4815:54;;-1:-1:-1;;4815:54:21;-1:-1:-1;;;4815:54:21;;;4897:25;;;;;:125;;-1:-1:-1;4966:56:21;;;;1418:2;4966:56;:::i;:::-;4942:21;:80;;;4897:125;4880:208;;;5054:23;;-1:-1:-1;;;5054:23:21;;;;;;;;;;;4880:208;5097:49;;-1:-1:-1;;5156:51:21;-1:-1:-1;;;5097:49:21;;;;;-1:-1:-1;;;;5156:51:21;;-1:-1:-1;;;5156:51:21;;;;;;;;4643:571::o;5220:277::-;5398:47;5417:4;5423:9;5434:1;5437;5440:4;5398:18;:47::i;:::-;5455:35;5471:4;5477:9;5488:1;5455:15;:35::i;1796:162:53:-;1684:7;1710:6;-1:-1:-1;;;;;1710:6:53;735:10:77;1855:23:53;1851:101;;1901:40;;-1:-1:-1;;;1901:40:53;;735:10:77;1901:40:53;;;18194:51:190;18167:18;;1901:40:53;18048:203:190;5864:493:21;6004:26;;-1:-1:-1;;;6004:26:21;;;;6081:57;6004:26;1418:2;6081:57;:::i;:::-;6057:21;:81;;;:127;;;-1:-1:-1;6158:26:21;;6057:127;6040:250;;;6256:23;;-1:-1:-1;;;6256:23:21;;;;;;;;;;;6040:250;-1:-1:-1;6299:51:21;;;;;;-1:-1:-1;;;6299:51:21;-1:-1:-1;;;;6299:51:21;;;;;;5864:493::o;2737:386:1:-;-1:-1:-1;;;;;2922:36:1;;;2847:34;2922:36;;;:30;:36;;;;;;;;:45;;;;;;;;;;;;;2981:31;;;2977:140;;-1:-1:-1;6799:2:25;2737:386:1;;;;:::o;77143:3530:25:-;77439:21;;77579:24;;77575:1500;;77622:35;77677:92;77706:11;77719:20;77741:21;77764:4;77677:28;:92::i;:::-;-1:-1:-1;;;;;77856:39:25;;77783:33;77856:39;;;:30;;;:39;;;;;:57;;:64;77619:150;;-1:-1:-1;77783:33:25;-1:-1:-1;77783:33:25;77934:848;77981:1;77950:13;:28;;;-1:-1:-1;;;;;77950:32:25;;:54;;;;;77990:14;77986:1;:18;77950:54;77934:848;;;-1:-1:-1;;;;;78207:39:25;;78112:29;78207:39;;;:30;;;:39;;;;;:57;;:60;;78173:185;;78207:57;78265:1;;78207:60;;;;;;:::i;:::-;;;;;;;;;;;:75;78308:28;;;;-1:-1:-1;;;;;;;;78207:75:25;;;;;;78173:185;:8;:185::i;:::-;78112:264;;78427:21;78395:13;:28;;:53;;;;;;;:::i;:::-;-1:-1:-1;;;;;78395:53:25;;;;;;78466:50;;-1:-1:-1;78466:50:25;;;;:::i;:::-;;;78692:74;78707:13;:28;;;-1:-1:-1;;;;;78692:74:25;-1:-1:-1;;;1513:21:30;78760:5:25;78692:14;:74::i;:::-;-1:-1:-1;;;;;78633:134:25;:28;;;:134;-1:-1:-1;78006:3:25;;;;:::i;:::-;;;;77934:848;;;-1:-1:-1;78799:28:25;;;;-1:-1:-1;;;;;78799:32:25;;78795:270;;78867:183;9875:6;78933:25;78902:13;:28;;;-1:-1:-1;;;;;78902:56:25;;;;;:::i;:::-;78901:84;;;;:::i;:::-;79007:25;78867:12;:183::i;:::-;78851:199;;78795:270;77605:1470;;;77575:1500;79169:24;;79165:1502;;79212:35;79267:93;79296:11;79309:20;79331:21;79354:5;79267:28;:93::i;:::-;-1:-1:-1;;;;;79447:39:25;;79375:33;79447:39;;;:30;;;:39;;;;;:57;;:64;79209:151;;-1:-1:-1;79375:33:25;-1:-1:-1;79375:33:25;79526:848;79573:1;79542:13;:28;;;-1:-1:-1;;;;;79542:32:25;;:54;;;;;79582:14;79578:1;:18;79542:54;79526:848;;;-1:-1:-1;;;;;79799:39:25;;79704:29;79799:39;;;:30;;;:39;;;;;:57;;:60;;79765:185;;79799:57;79857:1;;79799:60;;;;;;:::i;79765:185::-;79704:264;;80019:21;79987:13;:28;;:53;;;;;;;:::i;:::-;-1:-1:-1;;;;;79987:53:25;;;;;;80058:50;;-1:-1:-1;80058:50:25;;;;:::i;:::-;;;80284:74;80299:13;:28;;;-1:-1:-1;;;;;80284:74:25;-1:-1:-1;;;1513:21:30;80352:5:25;80284:14;:74::i;:::-;-1:-1:-1;;;;;80225:134:25;:28;;;:134;-1:-1:-1;79598:3:25;;;;:::i;:::-;;;;79526:848;;;-1:-1:-1;80391:28:25;;;;-1:-1:-1;;;;;80391:32:25;;80387:270;;80459:183;9875:6;80525:25;80494:13;:28;;;-1:-1:-1;;;;;80494:56:25;;;;;:::i;80459:183::-;80443:199;;80387:270;79195:1472;;;77143:3530;;;;;;;;;:::o;2812:1161:23:-;3040:24;3066:30;3098:13;3187:31;3221:11;:22;;;3187:56;;3253:392;;;;;;;;3292:21;:54;;;3253:392;;;;3360:21;:54;;;3253:392;;;;3428:21;:54;;;3253:392;;;;3496:27;3253:392;;;;3547:21;:37;;;3253:392;;;;3598:21;:37;;;3253:392;;;:11;:22;;:392;;;;3656:47;3706:41;3735:11;3706:28;:41::i;:::-;3656:91;;3812:48;3845:14;3812:32;:48::i;:::-;3928:38;;;;3757:103;;;-1:-1:-1;2812:1161:23;;-1:-1:-1;;;;;2812:1161:23:o;58200:970:25:-;58598:22;;58506:24;;;;;58672:106;58711:9;58598:11;58735:8;58745:22;58769:8;58672:38;:106::i;:::-;58630:148;;58808:49;58845:11;58808:36;:49::i;:::-;58789:68;-1:-1:-1;59085:29:25;:23;59111:3;59085:29;:::i;:::-;59057:24;:19;59079:2;59057:24;:::i;:::-;59125:38;;;;-1:-1:-1;58200:970:25;;59057:57;;;;;;-1:-1:-1;;;;;;58200:970:25:o;2272:534:23:-;2435:23;2470:21;2494:62;2516:13;2531:24;2494:21;:62::i;:::-;2470:86;;2590:13;2571:16;:32;2567:72;;;2612:27;;-1:-1:-1;;;2612:27:23;;;;;;;;;;;2567:72;1345:6;2737:13;:35;2733:66;;;2795:4;2774:25;;2733:66;2460:346;2272:534;;;;;:::o;1398:118:1:-;1471:10;1453:29;;;;:17;:29;;;;;;;;1448:61;;1491:18;;-1:-1:-1;;;1491:18:1;;;;;;;;;;;17342:648:21;17528:5;17721:22;17745:26;17787:90;17807:4;17813:1;17816:22;17840:29;17871:5;17787:19;:90::i;:::-;17720:157;;;;17888:61;17904:4;17910:16;17928:20;17888:15;:61::i;:::-;-1:-1:-1;17967:16:21;17342:648;-1:-1:-1;;;;17342:648:21:o;11300:1403::-;11497:17;;11438;;11497;;;;;11438;11569:60;11497:17;815:25;839:1;637:2;815:25;:::i;:::-;11569:12;:60::i;:::-;11539:90;;11639:31;11673:27;:62;;;-1:-1:-1;11704:31:21;;;;11673:62;11639:96;;11751:26;11750:27;:55;;;;-1:-1:-1;11781:24:21;;11750:55;11746:951;;;11835:13;;-1:-1:-1;;;11835:13:21;;;;;-1:-1:-1;11746:951:21;;;12129:543;12171:4;:25;;12197:19;12171:46;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;12240:26;:86;;12324:1;12240:86;;;12269:4;:25;;12295:19;12269:46;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;12240:86;12460:26;:168;;12601:24;;;:27;;;12460:168;;;12521:4;:24;;12546:19;12521:45;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;12460:168;12349:305;;12357:4;:24;;12382:19;12357:45;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;12349:54;;:305;12129:20;:543::i;:::-;12115:557;;11746:951;11457:1246;;;11300:1403;;;;:::o;49671:1554:25:-;49953:30;;50038:12;;50034:995;;50084:34;50136:45;50199:34;50251:45;50314:21;50352:141;50386:9;50397:17;50416:8;50426:17;50445:16;50463;50352;:141::i;:::-;50066:427;;;;;;;;;;50532:75;50580:26;50551;:55;;;;:::i;:::-;50532:18;:75::i;:::-;50507:100;-1:-1:-1;50672:41:25;;50668:169;;50733:89;50749:9;50769:13;50784:37;50733:15;:89::i;:::-;50854:41;;50850:169;;50915:89;50931:9;:18;;50951:13;50966:37;50915:15;:89::i;:::-;50052:977;;;;;50034:995;-1:-1:-1;;;;;51088:23:25;;;51084:135;;51159:49;51189:9;51200:7;51159:29;:49::i;:::-;51127:81;;51084:135;49671:1554;;;;;;;;;;:::o;7377:845:21:-;7662:26;;7514:12;;-1:-1:-1;;;7662:26:21;;;;7647:41;;;;7643:573;;7734:17;;;;;;;7776:24;7734:17;7792:7;7776:9;:24::i;:::-;7766:34;;7876:27;7906:48;7928:4;7934:19;7906:21;:48::i;:::-;8021:14;;;;8044:19;;;8021:43;7996:68;;-1:-1:-1;8093:84:21;8112:4;8027:7;7996:68;8150:19;8171:5;8093:18;:84::i;:::-;-1:-1:-1;8201:4:21;;7377:845;-1:-1:-1;;;;;7377:845:21:o;2912:187:53:-;2985:16;3004:6;;-1:-1:-1;;;;;3020:17:53;;;-1:-1:-1;;;;;;3020:17:53;;;;;;3052:40;;3004:6;;;;;;;3052:40;;2985:16;3052:40;2975:124;2912:187;:::o;25619:909:21:-;25814:32;;25703:5;;3685:1:30;;25814:32:21;;;;;;25857:186;;25973:58;25997:4;26003:27;25973:23;:58::i;:::-;901:26;926:1;696;901:26;:::i;:::-;25944:87;;;;:::i;:::-;25905:127;;25857:186;26069:13;;-1:-1:-1;;;26069:13:21;;;;;;;3542:2:30;26138:30:21;;;26204:23;;;;26263;;;;26305:22;;;;-1:-1:-1;26301:186:21;;;26363:12;26347:29;;26301:186;;;26411:12;26401:7;:22;;;26397:90;;;26459:12;26443:29;;26397:90;-1:-1:-1;26514:7:21;;25619:909;-1:-1:-1;;;;;;;25619:909:21:o;5412:255:1:-;1358:16;:14;:16::i;:::-;5570:10:::1;5553:28;::::0;;;:16:::1;:28;::::0;;;;5522:138:::1;::::0;5583:11;;5596:7;;5605:45:::1;::::0;5596:7;5605:24:::1;:45::i;:::-;5522:17;:138::i;11221:419::-:0;11356:5;11363;11454:19;:179;;-1:-1:-1;;;;;11609:23:1;;;;;;:17;:23;;;;;11567:66;;:41;:66::i;:::-;11454:179;;;-1:-1:-1;;;;;11515:23:1;;;;;;:17;:23;;;;;11488:64;;11540:11;11488:26;:64::i;:::-;11447:186;;;;11221:419;;;;;;:::o;16009:199:21:-;16104:5;16128:4;:25;;16154:46;16167:5;839:1;637:2;815:25;;;;:::i;16154:46::-;16128:73;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;16121:80;;16009:199;;;;:::o;20023:379::-;20198:5;20219:29;:34;;20252:1;20219:34;20215:73;;-1:-1:-1;;;20255:33:21;;20215:73;20306:89;20327:13;20342:21;20365:29;20306:89;;:20;:89::i;5435:111:89:-;5493:7;5312:5;;;5527;;;5311:36;5306:42;;5519:20;5071:294;14684:231:25;14770:16;;;14784:1;14770:16;;;;;;;;;;;;;;;;;;;-1:-1:-1;;14754:32:25;;;;:10;;;;-1:-1:-1;14754:32:25;;;;:::i;:::-;-1:-1:-1;14812:32:25;;;8548:2;14812:32;;;;;;;;;;;;;;;;;;-1:-1:-1;;14796:48:25;;;;:13;;;;-1:-1:-1;14796:48:25;;;;:::i;:::-;-1:-1:-1;14870:38:25;;;8626:3;14870:38;;;;;;;;;;;;;;;;;;-1:-1:-1;;14854:54:25;;;;:13;;;;-1:-1:-1;14854:54:25;;;;:::i;21208:1913:21:-;26839:21;:15;:21;21571;21522:25;;;21548:19;21522:46;;;;;;;:::i;:::-;;;;;;;;;;;;:70;;;;;;;;;;;;;;;;;;;;21650:16;21602:4;:24;;21627:19;21602:45;;;;;;;:::i;:::-;;;;;;;;;;;;:64;;;;;;;;;;;;;;;;;;21724:17;21764:56;21777:19;637:2;21764:12;:56::i;:::-;21744:76;;;;;;;;;-1:-1:-1;;21744:76:21;;;;;;;;-1:-1:-1;21836:31:21;;;;;21835:32;:50;;;;-1:-1:-1;21871:14:21;;21835:50;21831:119;;;21901:38;;-1:-1:-1;;21901:38:21;21935:4;21901:38;;;21831:119;22067:27;;-1:-1:-1;;;22067:27:21;;;;;22036:28;;22216:25;;;;22242:60;;22255:18;;;;;901:26;22242:12;:60::i;:::-;22216:87;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;22177:16;:126;22166:137;;22347:21;22335:33;;:8;:33;;;;:48;;;;22372:11;22335:48;22331:689;;;22434:18;;;;;;;22578:21;22527:26;;;22434:18;22527:48;;;;;;;:::i;:::-;;;;;;;;;;;;:72;;;;;;;;;;;;;;;;;;;;22667:16;22617:4;:25;;22643:20;22617:47;;;;;;;:::i;:::-;;;;;;;;;;;;:66;;;;;;;;;;;;;;;;;;22792:58;22805:20;696:1;22792:12;:58::i;:::-;22771:79;;-1:-1:-1;;22771:79:21;;;;;;;;;;;;;;;;;;-1:-1:-1;22771:79:21;22874:32;;;22873:33;:51;;;;-1:-1:-1;22910:14:21;;22873:51;22869:137;;;22948:39;;-1:-1:-1;;22948:39:21;;;;;22869:137;22385:635;22331:689;-1:-1:-1;;23091:23:21;;;;;;-1:-1:-1;;;23091:23:21;-1:-1:-1;;23091:23:21;;;;;;;;;;-1:-1:-1;;;;;21208:1913:21:o;20408:282::-;20530:44;;-1:-1:-1;;20584:48:21;-1:-1:-1;;;20530:44:21;;;;-1:-1:-1;;;;20584:48:21;;-1:-1:-1;;;20584:48:21;;;;;;;20648:35;;-1:-1:-1;12282:21:190;;;12264:40;;20648:35:21;;12252:2:190;12237:18;20648:35:21;;;;;;;20408:282;;;:::o;86122:1376:25:-;86339:30;86371:32;-1:-1:-1;;;;;;;;;;;;;;;;;;;86371:32:25;86416:25;86443:24;86469:26;86511:68;86535:11;86548:20;86570:8;86511:23;:68::i;:::-;86415:164;;;;;;86615:36;86634:16;86615:18;:36::i;:::-;-1:-1:-1;;;;;86589:62:25;;;:23;9611:2;86697:38;;:291;;86932:56;2194:7:30;86956:25:25;86980:1;86956:20;:25;:::i;:::-;:31;;;;:::i;:::-;86932:23;:56::i;:::-;86697:291;;;;;86750:167;86799:17;86818:11;:33;;;86853:18;86873:20;86895:8;86750:31;:167::i;:::-;86661:327;;86999:34;87085:18;87037:8;:37;;8073:1;87037:37;;;-1:-1:-1;;87037:37:25;87036:68;;;;:::i;:::-;86999:105;-1:-1:-1;87141:56:25;86999:105;87141:26;:56;:::i;:::-;87115:82;;87208:22;87233:62;87267:26;87233:27;:62::i;:::-;87208:87;;87387:104;87406:84;87426:17;87445:14;87461:18;87481:8;87406:19;:84::i;:::-;87387:18;:104::i;:::-;-1:-1:-1;;;;;87347:144:25;:25;;;:144;-1:-1:-1;86122:1376:25;;87347:10;;-1:-1:-1;86122:1376:25;;-1:-1:-1;;;;;;;;86122:1376:25:o;5617:111:89:-;5675:7;5312:5;;;5709;;;5311:36;5306:42;;5701:20;5071:294;1908:204:20;1997:14;2032:5;2036:1;2032;:5;:::i;:::-;2023:14;;2056:10;:49;;2095:10;2104:1;2095:6;:10;:::i;:::-;2056:49;;;2069:23;2082:6;2090:1;2069:12;:23::i;:::-;2047:58;1908:204;-1:-1:-1;;;;;1908:204:20:o;6215:704:89:-;6277:7;6300:1;6305;6300:6;6296:150;;6400:35;1035:4:79;6400:11:89;:35::i;:::-;6896:1;6891;6887;:5;6886:11;;;;;:::i;:::-;;6900:1;6886:15;6876:5;;;6860:22;:42;;6215:704;-1:-1:-1;;;6215:704:89:o;3450:452:29:-;3546:36;-1:-1:-1;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3546:36:29;3628:22;;:33;3594:31;;;:67;3750:27;3628:22;3750:14;:27::i;:::-;3710:36;;;3671:106;3672:36;;;3671:106;3868:27;3883:11;3868:14;:27::i;:::-;3827:37;;;3787:108;;;3788:14;3450:452;-1:-1:-1;3450:452:29:o;13211:1441::-;13478:36;;;;13438:37;;13596:36;;;;13556:37;;;;13317:27;;;;;;13438:76;-1:-1:-1;13438:76:29;13556;13647:25;;13438:76;13647:54;;;13677:24;13676:25;13647:54;13643:1003;;;13906:14;:37;;;13867:14;:36;;;:76;13806:14;:37;;;13767:14;:36;;;:76;:177;13745:199;;13643:1003;;;13980:24;13975:671;;14109:14;:37;;;14070:14;:36;;;:76;14048:98;;14232:14;:36;;;14192:14;:37;;;:76;14164:104;;14307:4;14296:15;;13975:671;;;14333:24;14328:318;;14462:14;:37;;;14423:14;:36;;;:76;14401:98;;14585:14;:36;;;14545:14;:37;;;:76;14517:104;;14328:318;13396:1256;;13211:1441;;;;;:::o;59574:1832:25:-;59846:27;59885:24;59933:47;59983:41;60012:11;59983:28;:41::i;:::-;59933:91;;60082:48;60115:14;60082:32;:48::i;:::-;-1:-1:-1;60039:91:25;-1:-1:-1;60039:91:25;-1:-1:-1;60152:21:25;;-1:-1:-1;60152:21:25;;;60256:99;60301:9;60312:8;60322:22;60346:8;60256:44;:99::i;:::-;60151:204;;;;;;;;60383:1;60369:11;:15;60365:476;;;60466:22;;60451:83;;342:1:19;60466:32:25;;;;;60500:13;60515:11;60528:5;60451:14;:83::i;:::-;60400:22;;:32;;:134;60614:22;;60599:83;;373:1:19;60614:32:25;;60599:83;60548:22;;:32;;:134;60762:22;;60747:83;;404:1:19;60762:32:25;;60747:83;60696:22;;:32;;:134;60365:476;60854:16;;60850:550;;60996:22;;60981:92;;60996:22;:33;;;;;61031:14;61047:12;475:4:19;60981:14:25;:92::i;:::-;60929:22;;:144;61154:22;;61139:92;;279:1:19;61154:33:25;;61139:92;61087:22;;:33;;:144;61312:22;;61297:92;;311:1:19;61312:33:25;;61297:92;61245:22;;:33;;:144;60850:550;59875:1531;;;;;59574:1832;;;;;;;:::o;9935:764:23:-;10049:24;10132;10158:30;10192:42;10222:11;10192:29;:42::i;:::-;10131:103;;;;10529:153;10570:22;10566:1;:26;:102;;10667:1;10529:19;:153::i;10566:102::-;10595:69;10610:16;2789:6:30;10634:22:23;10658:5;10595:14;:69::i;:::-;10529:19;:153::i;11159:500::-;11292:21;11418:13;11435:1;11418:18;11414:73;;-1:-1:-1;;;11452:24:23;;11414:73;11574:68;11589:24;2789:6:30;11621:13:23;11636:5;11574:14;:68::i;16214:179:21:-;16292:7;16342:10;;:34;;16375:1;16367:5;:9;16342:34;;;-1:-1:-1;16355:9:21;16214:179;-1:-1:-1;16214:179:21:o;23736:342::-;23897:10;24047:12;24015:21;23992:20;:44;23991:69;;;;;;;:::i;:::-;;;23736:342;-1:-1:-1;;;;23736:342:21:o;52040:1840:25:-;52339:34;;;;;52727:139;4401:3:30;6799:2:25;52791:16;52751:37;52771:17;52751;:37;:::i;:::-;:56;;;;:::i;:::-;52750:99;;;;:::i;:::-;:106;;;;:::i;:::-;52727:9;:139::i;:::-;52711:155;;52877:37;52917:66;52947:16;52965:17;52917:29;:66::i;:::-;52877:106;;53062:34;53099:33;53122:9;53099:22;:33::i;:::-;53062:70;-1:-1:-1;53213:290:25;53252:9;53284:13;53311:8;53333:29;53062:70;53416:17;53447:16;53477;53213:25;:290::i;:::-;53143:360;;-1:-1:-1;53143:360:25;-1:-1:-1;53583:290:25;53622:18;;;53654:13;53681:8;53703:29;53746:26;53786:17;53817:16;53847;53583:25;:290::i;:::-;52040:1840;;;;-1:-1:-1;53513:360:25;-1:-1:-1;53513:360:25;;-1:-1:-1;52040:1840:25;;-1:-1:-1;52040:1840:25;;-1:-1:-1;;;;;;52040:1840:25:o;10282:218:90:-;10339:7;-1:-1:-1;;;;;10362:25:90;;10358:105;;;10410:42;;-1:-1:-1;;;10410:42:90;;10441:3;10410:42;;;29330:36:190;29382:18;;;29375:34;;;29303:18;;10410:42:90;29148:267:190;10358:105:90;-1:-1:-1;10487:5:90;10282:218::o;42741:507:25:-;42932:19;;42903:26;;42932:23;;:19;;;;;;:23;:::i;:::-;42903:52;;;;42985:18;42969:13;:34;42965:277;;;43044:13;43019:213;43071:18;43059:9;:30;43019:213;;;43181:36;43122:4;:10;;43133:9;43122:21;;;;;;;:::i;:::-;;;;:55;;;:95;;;;;;;:::i;:::-;;;;-1:-1:-1;;43091:11:25;;43019:213;;;;42965:277;42893:355;42741:507;;;:::o;57031:512::-;57163:30;57314:69;57333:49;57354:9;:18;;57374:7;57333:20;:49::i;57314:69::-;57230;57249:49;57270:9;57290:7;57249:20;:49::i;57230:69::-;:153;;;;:::i;:::-;-1:-1:-1;;;;;57394:39:25;;;57459:1;57394:39;;;:30;;;:39;;;;;;;;:66;;-1:-1:-1;;57394:66:25;;;;;;57470:30;;;;:39;;;;;:66;;;;;;;;57205:178;57031:512;-1:-1:-1;57031:512:25:o;24491:496:21:-;24629:14;901:26;926:1;696;901:26;:::i;:::-;24846:35;;24896:27;24891:90;;24948:18;;:22;;24969:1;;24948:18;;;;;:22;:::i;:::-;24939:31;;;24491:496;-1:-1:-1;;;24491:496:21:o;15394:3441:25:-;-1:-1:-1;;;;;15600:23:25;;15596:61;;15632:25;;-1:-1:-1;;;15632:25:25;;;;;;;;;;;15596:61;15764:11;:33;;;15801:1;15764:38;15760:3069;;-1:-1:-1;;;;;15903:39:25;;:18;:39;;;:30;;;:39;;;;;:46;;;15899:143;;;15969:58;15990:9;16010:7;8073:1;15969:20;:58::i;:::-;;15899:143;-1:-1:-1;;;;;16059:39:25;;;;;;:30;;;:39;;;;;:46;;;16055:152;;;16125:67;16146:9;:18;;16166:7;-1:-1:-1;;16125:20:25;:67::i;:::-;;16055:152;15760:3069;;;16321:32;16355;16407:43;16427:11;:22;;;16407:19;:43::i;:::-;16493:29;;;;16564;;;;16320:130;;-1:-1:-1;16320:130:25;;-1:-1:-1;16493:29:25;16666:28;;16662:719;;16714:29;;;:56;;;16788:29;;;:56;;;-1:-1:-1;;16951:98:25;16714:11;16746:24;17019:23;17044:4;16951:28;:98::i;:::-;16862:187;;;;17067:299;17124:9;:18;;17164:10;17196:7;17225:23;17270:11;:33;;;17325:23;17067:35;:299::i;:::-;16696:685;;16662:719;17453:28;;17449:720;;17501:29;;;:56;;;17575:29;;;:56;;;-1:-1:-1;;17738:99:25;17501:11;17533:24;17806:23;-1:-1:-1;17738:28:25;:99::i;:::-;17649:188;;;;17855:299;17912:9;:18;;17952:10;17984:7;18013:23;18058:11;:33;;;18113:23;17855:35;:299::i;:::-;17483:686;;17449:720;18247:29;;;:49;;;18310:29;;;:49;;;18402:33;;;;-1:-1:-1;;18392:44:25;;:9;:44::i;:::-;18563:33;;18598:18;;;:33;18374:62;;-1:-1:-1;9559:1:25;;18554:78;;18563:33;;;;;;;;;18598;;;;;18554:8;:78::i;:::-;:135;;;;:::i;:::-;18524:7;:165;18503:266;;;18729:25;;-1:-1:-1;;;18729:25:25;;;;;;;;;;;18503:266;18783:17;;;:35;;;;;;-1:-1:-1;;18783:35:25;;;;;;;;;-1:-1:-1;;;;15394:3441:25;;;;:::o;9767:452:21:-;9868:13;9883;9908:17;9928:35;9951:4;9957:5;9928:22;:35::i;:::-;9991:13;;9908:55;;-1:-1:-1;;;;9991:13:21;;;;10136:54;9908:55;9991:13;;10136:19;:54::i;:::-;10115:75;;-1:-1:-1;10115:75:21;-1:-1:-1;10200:12:21;10211:1;10115:75;10200:12;:::i;:::-;;;9898:321;;9767:452;;;:::o;8850:490::-;9000:32;;8941:5;;;;9000:32;;;;;8941:5;;;9101:51;9000:32;;9101:16;:51::i;:::-;9042:110;;;;;;9162:14;9179:58;9203:4;9209:27;9179:23;:58::i;:::-;9162:75;;9254:79;9275:12;9289:11;9302:9;9313:11;9326:6;9254:20;:79::i;:::-;9247:86;;;;;;;;;8850:490;;;;;:::o;16399:191::-;16486:5;16561:11;16541:12;16556:1;16541:16;16540:32;;;;;:::i;:::-;;;16399:191;-1:-1:-1;;;16399:191:21:o;87891:1193:25:-;88065:25;88092:22;88116:26;88154:47;88204:41;88233:11;88204:28;:41::i;:::-;88154:91;;88255:28;88335:48;88368:14;88335:32;:48::i;:::-;88293:90;;;;;;;;;88415:142;88454:14;88470:20;88492:11;:33;;;88527:20;88415:25;:142::i;:::-;88394:163;;88747:8;:330;;88931:146;88971:14;88987:11;:29;;;89018:11;:38;;;89058:5;88931:22;:146::i;:::-;88747:330;;;88770:146;88810:14;88826:11;:29;;;88857:11;:38;;;88897:5;88770:22;:146::i;:::-;88727:350;;88144:940;;87891:1193;;;;;;;:::o;9264:218:90:-;9321:7;-1:-1:-1;;;;;9344:25:90;;9340:105;;;9392:42;;-1:-1:-1;;;9392:42:90;;9423:3;9392:42;;;29330:36:190;29382:18;;;29375:34;;;29303:18;;9392:42:90;29148:267:190;1966:3501:26;2048:5;821:7;2069:11;:31;:66;;;;2124:11;-1:-1:-1;;;2104:31:26;2069:66;2065:97;;;2144:18;;-1:-1:-1;;;2144:18:26;;;;;;;;;;;2065:97;-1:-1:-1;;;;;2271:41:26;;2268:1;2264:49;2361:9;;;2434:18;2428:25;;2425:1;2421:33;2502:9;;;2575:10;2569:17;;2566:1;2562:25;2635:9;;;2708:6;2702:13;;2699:1;2695:21;2764:9;;;2837:4;2831:11;;2828:1;2824:19;;;2891:9;;;2964:3;2958:10;;2955:1;2951:18;3017:9;;;3084:10;;;3081:1;3077:18;;;3143:9;;;;3203:10;;;2474;;2607;;;2736;;;2863;2989;;;3115;3233;2173:9;3323:3;3316:10;;3312:95;;3354:3;3348;:9;3332:11;:26;;3328:30;;3312:95;;;3403:3;3397;:9;3381:11;:26;;3377:30;;3312:95;-1:-1:-1;3515:9:26;;;3510:3;3506:19;;;3547:11;;;;3625:9;;;;3690;;3681:19;;;3722:11;;;3800:9;3865;;3856:19;;;3897:11;;;3975:9;4040;;4031:19;;;4072:11;;;4150:9;4215;;4206:19;;;4247:11;;;4325:9;4390;;4381:19;;;4422:11;;;4500:9;4565;;4556:19;;;4597:11;;;4675:9;4740;;4731:19;;;4772:11;;;4850:9;4915;;4906:19;;;;4947:11;;;;5025:9;;;;;3515;-1:-1:-1;;3433:17:26;;3455:2;3432:25;3596:10;;;;;-1:-1:-1;;;3596:10:26;3583:24;3771:10;;;;;;;3758:24;;;;3946:10;;;;;;;3933:24;;;;4121:10;;;;;;;4108:24;;;;4296:10;;;;;;;4283:24;4471:10;;;;;;;4458:24;4646:10;;;;;;;4633:24;4821:10;;;;;-1:-1:-1;;;4821:10:26;4808:24;4996:10;;;;;;;4983:24;550:20;5092:39;;-1:-1:-1;;5168:40:26;;3447:3;5167:49;;;;734:34;5253:39;;5252:48;;5319:17;;;;;;;;;5315:37;;-1:-1:-1;5345:7:26;1966:3501;-1:-1:-1;;;;;;1966:3501:26:o;5315:37::-;5396:11;5370:22;5385:6;5370:14;:22::i;:::-;:37;5366:56;;5416:6;1966:3501;-1:-1:-1;;;;;;;1966:3501:26:o;5366:56::-;-1:-1:-1;5443:7:26;1966:3501;-1:-1:-1;;;;;;1966:3501:26:o;94522:1554:25:-;94763:36;;;-1:-1:-1;;;94763:36:25;94938;9662:2;94938:18;:36;:::i;:::-;94919:55;-1:-1:-1;94993:9:25;94988:232;95012:8;95008:1;:12;94988:232;;;95062:72;95077:18;1843:21:30;-1:-1:-1;;;95128:5:25;95062:14;:72::i;:::-;95041:93;-1:-1:-1;95184:3:25;;94988:232;;;;-1:-1:-1;;;95476:272:25;6371:3;95512:17;:50;-1:-1:-1;;;95584:18:25;:24;95683:20;95659:21;-1:-1:-1;;;95631:18:25;:24;95630:50;:73;95725:5;95476:14;:272::i;:::-;:278;95262:492;;95801:8;:37;;-1:-1:-1;;95801:37:25;;;8073:1;95801:37;95783:56;-1:-1:-1;95860:11:25;;-1:-1:-1;95874:38:25;95898:13;95910:1;95898:8;:13;:::i;95874:38::-;95860:52;;;;95922:14;-1:-1:-1;;95940:4:25;:9;95939:38;;;;95962:1;95955:4;:8;:21;;;;;95968:8;95967:9;95955:21;95939:61;;8166:1;95939:61;;;95980:9;95939:61;;;95922:78;-1:-1:-1;95922:78:25;96043:16;;;;;:4;:16;:::i;:::-;:26;;;;:::i;:::-;96011:58;94522:1554;-1:-1:-1;;;;;;;;;;;94522:1554:25:o;1452:464:26:-;1529:22;-1:-1:-1;;1567:15:26;;;;;;:34;;-1:-1:-1;1586:15:26;;;;1234:6;1586:15;1567:34;1563:64;;;1610:17;;-1:-1:-1;;;1610:17:26;;;;;;;;;;;1563:64;1655:12;;;;1638:14;1703:11;;;:32;;1728:7;1703:32;;;1717:8;1718:7;1717:8;:::i;:::-;1677:59;;1797:2;1764:29;1785:7;1764:20;:29::i;:::-;:35;;1747:52;;1835:4;1831:8;;:1;:8;1827:65;;;1858:34;1878:14;1858:17;:34;:::i;1827:65::-;1553:363;;1452:464;;;:::o;90619:1387:25:-;90806:18;-1:-1:-1;;;90806:18:25;90900:49;90909:36;9662:2;90909:18;:36;:::i;:::-;90947:1;90900:8;:49::i;:::-;90881:68;-1:-1:-1;90964:9:25;90959:218;90983:8;90979:1;:12;90959:218;;;91032:75;91047:21;1843::30;-1:-1:-1;;;91101:5:25;91032:14;:75::i;:::-;91008:99;-1:-1:-1;91149:3:25;;90959:218;;;;91547:8;:188;;91664:71;91687:17;91706;-1:-1:-1;;;91730:4:25;91664:22;:71::i;:::-;91547:188;;;91574:71;91597:17;91616;-1:-1:-1;;;91640:4:25;91574:22;:71::i;:::-;91520:225;-1:-1:-1;91808:191:25;91836:72;91849:34;91862:21;91520:225;91849:34;:::i;:::-;91885:22;-1:-1:-1;;;1513:21:30;91885:22:25;:::i;:::-;91836:12;:72::i;:::-;6371:3;4401::30;91984:5:25;91808:14;:191::i;:::-;91795:204;90619:1387;-1:-1:-1;;;;;;;90619:1387:25:o;1776:194:79:-;1881:10;1875:4;1868:24;1918:4;1912;1905:18;1949:4;1943;1936:18;9114:996:29;9302:22;;:32;;;;9415;;;;;9302;;9415:36;9411:342;;9532:22;;9492:250;;373:1:19;9532:32:29;;;;9582:11;:29;;;9668:11;:38;;;9724:4;9492:22;:250::i;:::-;9467:275;;;;:::i;:::-;;;9411:342;9766:22;;:32;;;:36;9762:342;;9883:22;;9843:250;;404:1:19;9883:32:29;;;;9933:11;:29;;;10019:11;:38;;;10075:4;9843:22;:250::i;:::-;9818:275;;;;:::i;:::-;;;9762:342;9114:996;;;:::o;8096:1012::-;8287:22;;:33;;;8407;;;;8287;;8403:37;8399:347;;8522:22;;8482:253;;279:1:19;8522:33:29;;;;8573:11;:29;;;8660:11;:38;;;8716:5;8482:22;:253::i;:::-;8456:279;;;;:::i;:::-;;;8399:347;8763:22;;:33;;;8759:37;8755:347;;8878:22;;8838:253;;311:1:19;8878:33:29;;;;8929:11;:29;;;9016:11;:38;;;9072:5;8838:22;:253::i;61626:1779:25:-;61866:21;61889:19;61910:22;61934:20;61970:23;62008:8;:94;;-1:-1:-1;;;;;62062:40:25;;;;;;:30;;;:40;;;;;62008:94;;;-1:-1:-1;;;;;62019:40:25;;:18;:40;;;:30;;;:40;;;;;62008:94;62135:25;;;;:32;61970:132;;-1:-1:-1;62235:17:25;;;62231:67;;62276:1;62279;62282;62285;62268:19;;;;;;;;;;;;62231:67;-1:-1:-1;;;62332:36:25;62452:12;62435:954;62466:5;;62435:954;;62496:37;62536:7;:25;;62566:1;62562;:5;62536:32;;;;;;;;:::i;:::-;;;;;;;;;62496:72;;;;;;;;;62536:32;;62496:72;-1:-1:-1;;;;;62496:72:25;;;;;;-1:-1:-1;;;62496:72:25;;;;;;;;;;62666:51;;;;;62496:72;;-1:-1:-1;62496:72:25;62795:78;;62496:72;;-1:-1:-1;;;2443:21:30;62837:28:25;;62795:14;:78::i;:::-;62780:93;;;;62907:78;62922:20;62944:28;-1:-1:-1;;;62979:5:25;62907:14;:78::i;:::-;62891:94;;;;63030:22;63008:19;:44;63004:228;;;63151:11;63135:27;;63201:12;63184:29;;63004:228;63300:74;63315:28;1513:21:30;-1:-1:-1;;;63368:5:25;63300:14;:74::i;:::-;63249:125;-1:-1:-1;;;;;62473:3:25;62435:954;;;;62308:1091;;61960:1445;;61626:1779;;;;;;;;;;:::o;13338:846:23:-;13457:24;13483:30;13571:47;13621:41;13650:11;13621:28;:41::i;:::-;13571:91;;13719:48;13752:14;13719:32;:48::i;:::-;13673:94;;;;;;;;;14038:139;14082:16;14136:14;:31;;;14100:11;:33;;;:67;;;;:::i;:::-;14038:30;:139::i;:::-;14019:158;;13515:669;13338:846;;;:::o;12402:930::-;12485:24;774:4;12525:7;:41;12521:805;;;844:4;12586:7;:32;12582:734;;;1005:6;12833:68;923:6;12880:7;2789:6:30;12895:5:23;12833:14;:68::i;:::-;:125;;;;:::i;12582:734::-;1205:4;13176:68;1121:4;13223:7;2789:6:30;13238:5:23;13176:14;:68::i;:::-;:125;;;;:::i;12582:734::-;12402:930;;;:::o;83157:920:25:-;83233:12;7887:3;83290:10;:38;83286:785;;7720:34;83348:10;:40;83344:62;;83397:9;83405:1;8477:4;83397:9;:::i;83344:62::-;83522:17;83542;-1:-1:-1;;;83542:10:25;:17;:::i;:::-;83522:37;;83721:10;7655:4;83734:34;83758:9;83734:23;:34::i;:::-;:48;;;;:::i;:::-;83721:61;-1:-1:-1;7393:35:25;7492;-1:-1:-1;;;83968:25:25;83980:12;;;;83968:1;:25;:::i;:::-;:32;;;;:::i;:::-;:65;;;;:::i;:::-;83967:93;;;;:::i;11621:455:22:-;11755:19;11790:24;;11786:284;;11984:61;93:4:50;11997:19:22;:25;12024:20;11984:12;:61::i;54603:2180:25:-;54944:30;54976:41;55105:32;55140:51;55171:4;55177:13;55140:30;:51::i;:::-;55105:86;;55263:24;-1:-1:-1;;;;;55263:29:25;55291:1;55263:29;55259:48;;55302:1;55305;55294:13;;;;;;;55259:48;55360:34;55397:174;55455:29;55486:26;55514:24;55540:17;55397:40;:174::i;:::-;55360:211;;55586:38;55627:152;55684:8;55694:24;-1:-1:-1;;;;;55627:152:25;55720:17;55739:26;55627:39;:152::i;:::-;55586:193;;55885:30;55934:84;55949:24;-1:-1:-1;;;;;55934:84:25;55975:30;93:4:50;56012:5:25;55934:14;:84::i;:::-;55885:133;;56078:41;56138:68;-1:-1:-1;;;56151:22:25;:28;56181:24;-1:-1:-1;;;;;56138:68:25;:12;:68::i;:::-;56078:128;;56306:100;56323:33;56358:16;56376;475:4:19;56306:16:25;:100::i;:::-;56254:152;;56533:87;56548:33;56583:24;-1:-1:-1;;;;;56533:87:25;-1:-1:-1;;;56614:5:25;56533:14;:87::i;:::-;56492:128;-1:-1:-1;56676:90:25;56492:128;56717:16;56735;56753:12;56676:16;:90::i;:::-;56635:131;;55029:1748;;;;;54603:2180;;;;;;;;;;;;:::o;44154:1836::-;-1:-1:-1;;;;;44376:25:25;;44260:30;44376:25;;;:16;;;:25;;;;;:37;44503:9;;44376:37;;;;;;;44260:30;;44503:9;;:24;;-1:-1:-1;;44503:24:25;;;8073:1;44503:24;-1:-1:-1;;;;;44567:25:25;;44542:22;44567:25;;;:16;;;:25;;;;;:43;;:50;44477;;-1:-1:-1;44670:1199:25;44716:14;44701:12;:29;44670:1199;;;-1:-1:-1;;;;;44898:25:25;;44827:48;44898:25;;;:16;;;:25;;;;;:43;;:57;;44942:12;;44898:57;;;;;;:::i;:::-;;;;;;;;;;44827:128;;;;;;;;;44898:57;;44827:128;-1:-1:-1;;;;;44827:128:25;;;;;;-1:-1:-1;;;44827:128:25;;;;;;;;;;;;-1:-1:-1;44977:43:25;44973:805;;45115:34;;;;45100:12;45115:34;;;:18;;;:34;;;;;;45598:39;;45115:34;;;;;45100:12;45539:143;;45115:4;;:34;;-1:-1:-1;;;;;45539:143:25;45639:7;45648:12;45539:21;:143::i;:::-;-1:-1:-1;;;;;45420:25:25;;;;;;:16;;;:25;;;;;:80;;:94;;45501:12;;45420:94;;;;;;:::i;:::-;;;;;;;;;;45339:343;45704:55;;;;;-1:-1:-1;;44973:805:25;-1:-1:-1;45827:27:25;;;;44732:14;;44670:1199;;;;44302:1577;;;45941:42;45960:22;45941:18;:42::i;:::-;-1:-1:-1;;;;;45889:25:25;;;;;;:16;;;:25;;;;;:94;;:48;;:94;;;;;;;-1:-1:-1;;;;;45889:94:25;;:::i;:::-;;;;;;;;-1:-1:-1;;;;;45889:94:25;;;;;-1:-1:-1;;;;;45889:94:25;;;;;;44154:1836;;;;:::o;21285:1938::-;-1:-1:-1;;;;;21508:25:25;;21424:26;21508:25;;;:16;;;:25;;;;;:37;;;21580:43;;;:50;21508:37;;;;;;;;21424:26;21772:1314;21818:14;21803:12;:29;21772:1314;;;21959:7;9225:3;21945:21;:46;;;;-1:-1:-1;;21970:7:25;:21;21945:46;21993:5;21941:57;-1:-1:-1;;;;;22083:25:25;;22013:51;22083:25;;;:16;;;:25;;;;;:43;;:57;;22127:12;;22083:57;;;;;;:::i;:::-;;;;;;;;;;22013:127;;;;;;;;;22083:57;;22013:127;-1:-1:-1;;;;;22013:127:25;;;;;-1:-1:-1;;;22013:127:25;;;;;;;;;;;-1:-1:-1;22237:48:25;22233:723;;22380:34;;;;22362:15;22380:34;;;:18;;;:34;;;;;;;;22504:148;22380:4;22564:29;22405:7;22380:34;22613:7;22622:12;22504:32;:148::i;:::-;22696:19;;;;;;;22671:22;22752:52;22768:10;;;22696:19;22768:26;;;;;;;:::i;:::-;;;;2022:12:28;;:19;;1920:129;22752:52:25;:57;;;;-1:-1:-1;22831:25:25;;;:40;;;;;22860:11;22831:40;22827:115;;;22919:4;22895:28;;22827:115;22287:669;;;22233:723;-1:-1:-1;23034:27:25;;;;21834:14;;21772:1314;;;-1:-1:-1;;;;;;23191:25:25;;;;;;:16;;;:25;;;;;23184:32;;-1:-1:-1;;23184:32:25;;;23191:25;23184:32;;;;23191:25;23184:32;:::i;:::-;;;;;;;:::i;:::-;;;21452:1771;;21285:1938;;;;;:::o;68070:5273::-;68405:20;;;;68373:21;;68492:20;;;;68405;68460:21;;;68579:20;;;;68547:21;;;;68166:34;;;;68366:60;;68453;;;;68540;4401:3:30;68997:26:25;68166:34;;;69112:15;;;;:42;;69144:10;69143:11;;69112:42;;;69130:10;69112:42;69088:67;-1:-1:-1;69170:17:25;69190:15;-1:-1:-1;69190:15:25;;69243:39;;69272:10;69271:11;;69243:39;;;69258:10;69243:39;7233:3;69313:53;;-1:-1:-1;69313:53:25;;-1:-1:-1;69433:12:25;69428:43;;69461:10;69460:11;;69447:24;;69428:43;69486:17;69506:15;-1:-1:-1;69506:15:25;;69559:39;;69588:10;69587:11;;69559:39;;;69574:10;69559:39;6988:2;69629:44;;-1:-1:-1;69629:44:25;;-1:-1:-1;69740:12:25;69735:43;;69768:10;69767:11;;69754:24;;69735:43;68748:1041;;69827:13;69844:1;69827:18;69823:2707;;70050:26;70079:56;70094:13;-1:-1:-1;;;70114:13:25;70129:5;70079:14;:56::i;:::-;70199:20;;;;70050:85;;-1:-1:-1;70223:1:25;70199:25;70195:270;;70277:18;70248:47;;70195:270;;;70428:18;70399:47;;70195:270;69847:632;69823:2707;;;70526:13;70543:1;70526:18;70522:2008;;70701:26;70730:56;70745:13;-1:-1:-1;;;70765:13:25;70780:5;70730:14;:56::i;:::-;70850:20;;;;70701:85;;-1:-1:-1;70874:1:25;70850:25;70846:270;;70928:18;70899:47;;70846:270;;;71079:18;-1:-1:-1;70522:2008:25;;;71177:13;71194:1;71177:18;71173:1357;;71534:21;;;;71530:25;71526:116;;71587:21;;;;71583:25;71579:44;;-1:-1:-1;71618:1:25;;;;-1:-1:-1;68070:5273:25;-1:-1:-1;;;;;;;68070:5273:25:o;71579:44::-;71675:26;71704:68;71714:57;71729:13;-1:-1:-1;;;71750:13:25;71765:5;71714:14;:57::i;:::-;71704:9;:68::i;:::-;71840:21;;;;71675:97;;-1:-1:-1;71836:25:25;71832:270;;71914:18;71885:47;;71832:270;;71173:1357;72255:260;72315:182;;;;;;;;72382:10;72315:182;;;;72394:10;72315:182;;;;72406:10;72315:182;;;;72418:13;72315:182;;;;72433:10;373:1:19;72433:20:25;;;;;;;:::i;:::-;;;;;72315:182;;;;72455:10;404:1:19;72455:20:25;;;;;;;:::i;:::-;;;;;72315:182;;72255:38;:260::i;:::-;72196:319;;-1:-1:-1;72196:319:25;-1:-1:-1;71173:1357:25;1323:6:26;72590:26:25;:59;:142;;;;72706:26;1385:34:26;72673:59:25;72590:142;72569:241;;;72794:1;72765:30;;72569:241;1323:6:26;72844:26:25;:59;:142;;;;72960:26;1385:34:26;72927:59:25;72844:142;72823:241;;;73048:1;73019:30;;72823:241;73128:30;;73124:203;;73227:85;73242:26;4401:3:30;6988:2:25;73306:5;73227:14;:85::i;:::-;73178:134;;73124:203;68238:5105;;;;;;68070:5273;;;:::o;19376:1474::-;19750:9;;19724:23;;19750:9;;:38;;-1:-1:-1;;19750:38:25;;;8073:1;19750:38;-1:-1:-1;;;;;19987:25:25;;19854:26;19987:25;;;:16;;;:25;;;;;:32;19724:64;;-1:-1:-1;19854:26:25;;;19987:32;;19983:140;;;20059:53;20080:4;20086:7;20095:16;20059:20;:53::i;:::-;20035:77;;19983:140;20196:28;;;;-1:-1:-1;;;;;20192:32:25;;20188:353;;20262:268;20295:4;20317:7;20342:16;20376:26;20420:13;20451:24;20493:23;20262:15;:268::i;:::-;20240:290;;20188:353;20588:21;:45;;;;;20614:19;20613:20;20588:45;20584:260;;;20778:19;;20726:92;;20778:19;;8410:1;;8548:2;;20778:19;;;;;:39;;20726:25;:92::i;:::-;20677:142;;;;;;;;;-1:-1:-1;;20677:142:25;;;;;;20584:260;19659:1191;;;19376:1474;;;;;;:::o;15745:258:21:-;15823:9;15834;15872:1;15868:5;;:1;:5;;;:23;;15886:1;15889;15868:23;;;15877:1;15880;15868:23;15855:36;;-1:-1:-1;15855:36:21;-1:-1:-1;15905:7:21;;;;;;;;;15901:96;;;15934:1;15928:7;;15901:96;;;15962:1;15956:7;;:3;:7;;;15952:45;;;-1:-1:-1;15745:258:21;;;;:::o;10630:357::-;10762:5;10769;10776;10814:58;10838:4;10844:27;10814:23;:58::i;:::-;10886:57;10909:4;10915:27;10886:22;:57::i;:::-;10957:13;;10793:187;;-1:-1:-1;;;10957:13:21;;;;;;-1:-1:-1;10630:357:21;-1:-1:-1;;;10630:357:21:o;14247:1265::-;14437:13;14452;14498:69;14517:12;14531:11;14544:9;14555:11;14498:18;:69::i;:::-;14477:90;;-1:-1:-1;14477:90:21;-1:-1:-1;14771:25:21;;;;:15;;;;:25;14747:13;14880:154;752:3;901:26;14942:35;;;;14747:13;14880:14;:154::i;:::-;14852:5;:182;14812:236;;-1:-1:-1;;15256:25:21;;15247:6;15234:9;15227:17;;:26;:54;:102;;-1:-1:-1;;15227:102:21;;;15302:6;15290:9;:18;;;15227:102;15201:128;-1:-1:-1;1234:6:26;15386:25:21;15353:17;;;:26;;:30;:58;:142;;1234:6:26;15353:142:21;;;15448:6;15436:9;:18;;;15457:1;15436:22;15353:142;15343:152;;14676:830;;14247:1265;;;;;;;;:::o;92515:1531:25:-;92706:26;92744:12;9973:19;92931:617;92959:339;92991:88;93006:4;7153:7;4520:9:30;93073:5:25;92991:14;:88::i;:::-;93097:10;93236:25;93260:1;93236:20;:25;:::i;:::-;93212:49;;:21;:49;:::i;:::-;93279:5;92959:14;:339::i;:::-;10227:22;93498:21;93533:5;92931:14;:617::i;:::-;:627;;;;:::i;:::-;92744:814;-1:-1:-1;93625:16:25;93688:1;93652:32;-1:-1:-1;;;93662:9:25;93688:1;92744:814;93662:9;:::i;:::-;:21;;;;:::i;93652:32::-;93645:39;;:4;:39;:::i;:::-;93644:45;;;;:::i;:::-;93625:64;-1:-1:-1;93869:10:25;93882:83;93906:58;93921:13;93933:1;93625:64;93921:13;:::i;:::-;10344:21;-1:-1:-1;;;93958:5:25;93906:14;:58::i;93882:83::-;93869:96;-1:-1:-1;94005:16:25;;;;;:33;;94034:4;94005:33;;;;;8073:1;94005:33;93976:63;92515:1531;-1:-1:-1;;;;;;;;92515:1531:25:o;12871:334:29:-;13041:21;13078:6;13088:1;13078:11;13074:25;;-1:-1:-1;13098:1:29;13091:8;;13074:25;13125:73;13140:26;13168:6;13176:12;13190:7;13125:14;:73::i;11407:381::-;11577:21;11614:6;11624:1;11614:11;11610:25;;-1:-1:-1;11634:1:29;11627:8;;11610:25;11673:108;11688:64;11703:26;11731:6;-1:-1:-1;;;11744:7:29;11688:14;:64::i;:::-;11754:12;-1:-1:-1;;;11773:7:29;11673:14;:108::i;5473:602:26:-;5546:19;-1:-1:-1;;5581:15:26;;;;;;:34;;-1:-1:-1;5600:15:26;;;;1234:6;5600:15;5581:34;5577:64;;;5624:17;;-1:-1:-1;;;5624:17:26;;;;;;;;;;;5577:64;5651:21;;;;:14;5708:11;;;:32;;5733:7;5708:32;;;5722:8;5723:7;5722:8;:::i;:::-;5682:59;-1:-1:-1;5848:12:26;5859:1;5682:59;5848:12;:::i;:::-;;;5884:29;5905:7;5884:20;:29::i;:::-;5870:43;-1:-1:-1;5937:6:26;5927:16;;:21;5923:75;;5995:3;5965:25;:11;5979;5965:25;:::i;:::-;5964:34;;5950:48;;5923:75;6017:4;6013:8;;:1;:8;6009:59;;;6037:31;6057:11;-1:-1:-1;;6037:31:26;:::i;6081:2078::-;6164:19;6233:7;6243:3;6233:13;6250:1;6233:18;:93;;-1:-1:-1;;;6233:93:26;;;-1:-1:-1;;;6233:93:26;6219:107;;;-1:-1:-1;6354:3:26;6344:13;;:18;6340:95;;-1:-1:-1;;;6379:48:26;6432:3;6378:57;6340:95;6463:3;6453:13;;:18;6449:95;;-1:-1:-1;;;6488:48:26;6541:3;6487:57;6449:95;6572:3;6562:13;;:18;6558:95;;6611:34;6597:48;6650:3;6596:57;6558:95;6681:4;6671:14;;:19;6667:129;;6739:34;6725:48;6778:3;6724:57;6667:129;6823:4;6813:14;;:19;6809:129;;6881:34;6867:48;6920:3;6866:57;6809:129;6965:4;6955:14;;:19;6951:129;;7023:34;7009:48;7062:3;7008:57;6951:129;7107:4;7097:14;;:19;7093:129;;7165:34;7151:48;7204:3;7150:57;7093:129;7249:5;7239:15;;:20;7235:130;;7308:34;7294:48;7347:3;7293:57;7235:130;7392:5;7382:15;;:20;7378:130;;7451:34;7437:48;7490:3;7436:57;7378:130;7535:5;7525:15;;:20;7521:130;;7594:34;7580:48;7633:3;7579:57;7521:130;7678:5;7668:15;;:20;7664:129;;7737:33;7723:47;7775:3;7722:56;7664:129;7820:6;7810:16;;:21;7806:129;;7880:32;7866:46;7917:3;7865:55;7806:129;7962:6;7952:16;;:21;7948:93;;8004:29;7990:43;8038:3;7989:52;7948:93;8069:6;8059:16;;:21;8055:87;;8111:23;8097:37;8139:3;8096:46;6081:2078;;;:::o;12472:393:29:-;12674:23;12713:15;12732:1;12713:20;12709:34;;-1:-1:-1;12742:1:29;12735:8;;12709:34;12771:87;12786:15;12803:17;12822:26;12850:7;12771:14;:87::i;10973:428::-;11157:21;11194:15;11213:1;11194:20;11190:34;;-1:-1:-1;11223:1:29;11216:8;;11190:34;11250:144;11278:64;11293:15;-1:-1:-1;;;11315:17:29;11334:7;11278:14;:64::i;:::-;-1:-1:-1;;;11349:26:29;11377:7;11250:14;:144::i;18393:383::-;18527:7;18573:21;18550:19;:44;18546:103;;18617:21;;-1:-1:-1;;;18617:21:29;;;;;;;;;;;18546:103;18665:104;18678:43;18702:19;18678:21;:43;:::i;:::-;18724;18748:19;18724:21;:43;:::i;81159:592:25:-;81345:19;;81286:29;;81345:19;;;;;81378:23;;;81374:92;;;81424:1;81417:8;;;;;81374:92;81501:13;81476:269;81529:7;81516:9;:20;81476:269;;81624:15;81642:4;:10;;81653:9;81642:21;;;;;;;:::i;:::-;;;;:33;;:46;-1:-1:-1;;;;;81642:46:25;;-1:-1:-1;81702:32:25;81642:46;81702:32;;:::i;:::-;;;81551:194;81538:11;;;;;:::i;:::-;;;;81476:269;;;;81317:434;81159:592;;;;:::o;98076:1952::-;98320:34;;98493:35;98499:29;93:4:50;98493:35:25;:::i;:::-;98452:76;-1:-1:-1;98539:28:25;98570:58;98602:26;5211:7:30;98570:58:25;:::i;:::-;98539:89;;98639:38;98692:106;98707:30;98739:20;5211:7:30;98792:5:25;98692:14;:106::i;:::-;98639:159;;98809:31;5342:6:30;98854:30:25;:63;98850:421;;;-1:-1:-1;99088:1:25;98850:421;;;99197:63;99230:30;5342:6:30;99197:63:25;:::i;:::-;99171:89;;98850:421;99411:45;5046:1:30;99459:92:25;99518:23;99459:45;:92::i;:::-;:132;;;;:::i;:::-;99411:180;;99731:35;99781:97;99796:30;99828:37;93:4:50;99872:5:25;99781:14;:97::i;:::-;99731:147;;99929:92;99944:27;99973:17;99992:21;-1:-1:-1;;;;;99929:92:25;100015:5;99929:14;:92::i;14048:638:22:-;14236:7;14459:210;14485:91;14500:47;14526:10;14538:8;14500:25;:47::i;:::-;14549:14;93:4:50;14570:5:22;14485:14;:91::i;:::-;14614:41;14623:15;14640:14;14614:8;:41::i;:::-;-1:-1:-1;;;;;14594:61:22;14459:8;:210::i;1541:361:20:-;1695:15;1726:11;1741:1;1726:16;1722:105;;-1:-1:-1;1765:6:20;1758:13;;1722:105;1843:52;1850:6;1858:11;1871;1884:10;1843:6;:52::i;46655:833:25:-;46864:30;46896:52;47081:42;47112:4;47118;47081:30;:42::i;:::-;-1:-1:-1;;;;;47300:25:25;;;;;;:16;;;:25;;;;;:80;;:94;;47034:89;;-1:-1:-1;47209:272:25;;47381:12;;47300:94;;;;;;:::i;:::-;;;;;;;;;47237:44;:157;;;;:::i;:::-;47408:28;-1:-1:-1;;;47467:4:25;47209:14;:272::i;:::-;47184:297;;46655:833;;;;;;;;:::o;23718:3050::-;24081:40;;;;24036:42;24081:40;;;:24;;;:40;;;;;;;;;24036:85;;;;;;;;;-1:-1:-1;;;;;24036:85:25;;;;;-1:-1:-1;;;24036:85:25;;;;;;;;24241:42;-1:-1:-1;;;;;;;;;;;;;;;;;;;24241:42:25;24409:44;;;;;24371:35;;;;:82;-1:-1:-1;;;;;24317:136:25;;;:35;;;:136;;;24555:42;;24519:33;;:78;24467:130;;;-1:-1:-1;;24697:46:25;;:9;:46::i;:::-;24810:40;;;;;;;;:24;;;:40;;;;;;;:63;;;;;;-1:-1:-1;;;;;24810:63:25;;;-1:-1:-1;;;24810:63:25;;;;;;;;;24679:64;;-1:-1:-1;24888:40:25;24884:1829;;25054:65;25074:4;25080:20;25102:7;25111;25054:19;:65::i;:::-;25331:102;25362:4;25368:29;25399:7;25408;25417:12;25431:1;25331:30;:102::i;:::-;24884:1829;;;25464:7;25454;:17;25450:1263;;;25555:144;25610:4;25616:7;25625:20;25647;25669:7;25678;25555:37;:144::i;:::-;25843:256;25891:4;25913:29;25960:7;25985;26010:12;26040:45;26071:4;26077:7;26040:30;:45::i;:::-;25843:30;:256::i;25450:1263::-;26323:33;;26275:10;;;26286:7;26275:19;;;;;;;:::i;:::-;;;;:31;;:81;;:44;;:81;;;;-1:-1:-1;;;;;26275:81:25;;:::i;:::-;;;;;;;;-1:-1:-1;;;;;26275:81:25;;;;;-1:-1:-1;;;;;26275:81:25;;;;;;26420:20;:35;;;26370:4;:10;;26381:7;26370:19;;;;;;;:::i;:::-;;;;:31;;:46;;;:85;;;;;;;;;;-1:-1:-1;;;;;26370:85:25;;;;;:::i;:::-;;;;;;;;-1:-1:-1;;;;;26370:85:25;;;;;-1:-1:-1;;;;;26370:85:25;;;;;;26565:74;26581:4;26603:20;:33;;;-1:-1:-1;;;;;26595:42:25;26587:51;;26565:15;:74::i;20567:5181:89:-;20615:7;20733:1;20728;:6;20724:53;;-1:-1:-1;20761:1:89;20567:5181::o;20724:53::-;21717:1;21745;-1:-1:-1;;;21765:16:89;;21761:92;;21808:3;21801:10;;;;;21836:2;21829:9;21761:92;-1:-1:-1;;;21870:2:89;:15;21866:90;;21912:2;21905:9;;;;;21939:2;21932:9;21866:90;-1:-1:-1;;;21973:2:89;:15;21969:90;;22015:2;22008:9;;;;;22042:2;22035:9;21969:90;22083:7;22076:2;:15;22072:89;;22118:2;22111:9;;;;;22145:1;22138:8;22072:89;22185:6;22178:2;:14;22174:87;;22219:1;22212:8;;;;;22245:1;22238:8;22174:87;22285:6;22278:2;:14;22274:87;;22319:1;22312:8;;;;;22345:1;22338:8;22274:87;22385:6;22378:2;:14;22374:61;;22419:1;22412:8;22374:61;22861:1;:6;22872:1;22860:13;;;;;24771:1;22860:13;24771:6;;;;:::i;:::-;;24766:2;:11;24765:18;;24760:23;;24891:1;24884:2;24880:1;:6;;;;;:::i;:::-;;24875:2;:11;24874:18;;24869:23;;25002:1;24995:2;24991:1;:6;;;;;:::i;:::-;;24986:2;:11;24985:18;;24980:23;;25111:1;25104:2;25100:1;:6;;;;;:::i;:::-;;25095:2;:11;25094:18;;25089:23;;25221:1;25214:2;25210:1;:6;;;;;:::i;:::-;;25205:2;:11;25204:18;;25199:23;;25331:1;25324:2;25320:1;:6;;;;;:::i;:::-;;25315:2;:11;25314:18;;25309:23;;25703:28;25728:2;25724:1;:6;;;;;:::i;:::-;;25719:11;;;34795:145:90;25703:28:89;25698:33;;;20567:5181;-1:-1:-1;;;20567:5181:89:o;73662:2777:25:-;74039:16;;74194;;;;74175;;;;73803:34;;;;;;;;74039:16;74171:39;:1;:39;74145:23;;;:65;74228:17;;;74224:36;;;-1:-1:-1;74255:1:25;;;;-1:-1:-1;73662:2777:25;-1:-1:-1;;;;;73662:2777:25:o;74224:36::-;74489:13;74506:1;74489:18;74485:37;;-1:-1:-1;74517:1:25;;;;-1:-1:-1;73662:2777:25;-1:-1:-1;;;;;73662:2777:25:o;74485:37::-;74565:24;74599:33;74617:13;74599:9;:33::i;:::-;74844:19;;;;74669:30;;;;-1:-1:-1;74736:30:25;;;-1:-1:-1;74820:21:25;;-1:-1:-1;74820:21:25;;-1:-1:-1;74820:21:25;75071:24;;;:71;;75122:20;75071:71;;;75099:20;75098:21;;75071:71;75013:130;;75182:202;75244:1;75222:19;:23;:68;;75271:19;75222:68;;;75249:19;75248:20;;75222:68;-1:-1:-1;;;75334:13:25;75330:1;:17;75365:5;75182:14;:202::i;:::-;75157:227;;75424:70;75439:23;-1:-1:-1;;;75473:13:25;75469:1;:17;75488:5;75424:14;:70::i;:::-;75398:96;;74989:516;75519:5;:21;;;75544:1;75519:26;75515:918;;75565:5;:21;;;75590:1;75565:26;75561:390;;75690:22;75661:51;;75759:23;75730:52;;75515:918;;75561:390;75914:22;75885:51;;75515:918;;;76064:5;:21;;;76089:1;76064:26;76060:363;;76176:23;76147:52;;76060:363;;;76316:22;76287:51;;76385:23;76356:52;;76060:363;73875:2564;;;;;73662:2777;;;:::o;28199:4207::-;28504:24;28540:16;28566:17;28607:24;8838:3;28664:26;28660:1;:30;:89;;28722:27;28723:26;28722:27;:::i;:::-;28660:89;;;28693:26;28660:89;28659:131;;;;:::i;:::-;28607:197;;28926:1;28897:26;:30;:55;;;;-1:-1:-1;28931:21:25;;;28897:55;:85;;8166:1;28897:85;;;-1:-1:-1;;28897:85:25;28831:46;8838:3;28831:26;:46;:::i;:::-;:152;;;;:::i;:::-;28818:165;-1:-1:-1;29008:21:25;;:79;;29053:34;9662:2;29053:16;:34;:::i;:::-;29008:79;;;9716:1;29008:79;28997:90;;28593:505;29112:8;29124:1;29112:13;29108:399;;29296:74;29311:13;:28;;;-1:-1:-1;;;;;29296:74:25;-1:-1:-1;;;1513:21:30;29364:5:25;29296:14;:74::i;:::-;-1:-1:-1;;;;;29241:130:25;:28;;;:130;29422:30;29436:16;29422:30;;:::i;:::-;;;9716:1;29467:29;;29108:399;-1:-1:-1;;;;;29517:25:25;;;;;;:16;;;:25;;;;;:57;;-1:-1:-1;;29517:57:25;;;;;;;;;29653:2655;29664:28;;;;-1:-1:-1;;;;;29660:32:25;;29653:2655;;29803:10;9225:3;29789:24;:52;;;;-1:-1:-1;;29817:10:25;:24;29789:52;29843:5;29785:63;29923:74;30012:169;30066:4;30072:10;30084:13;30099:24;30125:7;30134:23;30159:8;30012:36;:169::i;:::-;30391:51;;;;:66;;;9716:1;;-1:-1:-1;29923:258:25;;-1:-1:-1;;;;;;30387:70:25;;30383:1167;;-1:-1:-1;;;;;30523:25:25;;;;;;:16;;;:25;;;;;;;;30593:250;;;;;;;;30648:51;;;;;:64;-1:-1:-1;;;;;30593:250:25;;;;;30754:51;;:66;;;30593:250;;;;;;;;30523:43;;;;:338;;;;;;;;;;;;;;;;;;-1:-1:-1;;;30523:338:25;;;;;;;;;;;;30951:25;;;;;;:80;;31058:68;30523:4;30648:33;31058:27;:68::i;:::-;30951:193;;;;;;;-1:-1:-1;30951:193:25;;;;;;;;;;31333:41;;;31311:19;;;;;;;:63;31307:229;;;31427:41;;;;31398:71;;;;;;;;-1:-1:-1;;31398:71:25;;;;;;:19;;-1:-1:-1;31307:229:25;31685:51;;;;;:66;;;;;31653:28;;;:98;;-1:-1:-1;;;;;31653:98:25;;;;;;;;;31799:51;;:64;31769:94;;;;;;32119:74;;-1:-1:-1;;;1513:21:30;-1:-1:-1;32119:14:25;:74::i;:::-;-1:-1:-1;;;;;32060:134:25;:28;;;:134;-1:-1:-1;32253:30:25;;29653:2655;;;-1:-1:-1;;;;;;;32360:25:25;;;;;;;:16;;;;:25;;-1:-1:-1;;32360:25:25;;;;:39;;-1:-1:-1;;32360:39:25;32395:4;32360:39;;;-1:-1:-1;28199:4207:25;;;-1:-1:-1;;28199:4207:25:o;65919:499::-;66059:22;66121:47;66139:4;:10;;66150:5;66139:17;;;;;;;:::i;:::-;;66157:9;66139:28;;;;;;;;:::i;:::-;;;;;;;;;9387:6;97310:22;;97199:140;66121:47;66172:1;66121:52;66117:213;;66197:5;66206:1;66197:10;66193:24;;-1:-1:-1;66216:1:25;66209:8;;66193:24;66242:73;66268:4;-1:-1:-1;;66274:9:25;;8548:2;66285:9;:29;;66242:73;66235:80;;;;66117:213;66350:51;66378:4;66384:5;66391:9;66350:27;:51::i;13019:1164:21:-;13218:18;;13157;;13218;;;;;13157;13277:62;13218:18;901:26;926:1;696;901:26;:::i;13277:62::-;13246:93;;13355:27;13354:28;:57;;;;-1:-1:-1;13386:25:21;;13354:57;13350:827;;;13442:13;;-1:-1:-1;;;13442:13:21;;;;;-1:-1:-1;13350:827:21;;;13600:552;13642:4;:26;;13669:20;13642:48;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;13712:27;:89;;13799:1;13712:89;;;13742:4;:26;;13769:20;13742:48;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;13712:89;13936:27;:172;;14080:25;;;:28;;;13936:172;;;13998:4;:25;;14024:20;13998:47;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;13936:172;13823:311;;13831:4;:25;;13857:20;13831:47;;;;;;;:::i;15518:221::-;15604:9;15615;15649:28;15669:1;15672;15675;15649:19;:28::i;:::-;15636:41;;-1:-1:-1;15636:41:21;-1:-1:-1;15700:32:21;15720:1;15636:41;;15700:19;:32::i;:::-;15687:45;;;;-1:-1:-1;15518:221:21;-1:-1:-1;;;;;15518:221:21:o;15292:614:22:-;15402:20;1395:6;15438:40;;15434:425;;15509:34;:17;1520:6;15509:26;:34::i;:::-;15494:49;;15434:425;;;1462:8;15564:39;;15560:299;;1676:7;15634:58;1567:4;15635:39;1395:6;15635:17;:39;:::i;:::-;15634:50;;:58::i;:::-;:85;;;;:::i;15560:299::-;1747:7;15765:57;1612:5;15766:38;1462:8;15766:17;:38;:::i;15765:57::-;:83;;;;:::i;:::-;15750:98;;15560:299;15868:31;5393:8:30;15868:31:22;;:::i;1311:319:50:-;1383:7;;1422:5;1426:1;1422;:5;:::i;:::-;1402:25;-1:-1:-1;1437:18:50;1458:41;1402:25;;1491:7;93:4;1491:1;:7;:::i;:::-;1458:10;:41::i;:::-;1437:62;-1:-1:-1;1509:17:50;1529:42;1437:62;1552:9;1563:7;93:4;1563:1;:7;:::i;1529:42::-;1509:62;-1:-1:-1;1509:62:50;1589:22;1601:10;1589:9;:22;:::i;:::-;:34;;;;:::i;43577:242:25:-;43694:41;43754:4;:10;;43772:4;43754:24;;;;;;;;;:::i;:::-;;;;:58;;;43747:65;;43577:242;;;;:::o;39159:1150::-;39378:34;;;;39415:1;39378:34;;;:18;;;:34;;;;;:38;;-1:-1:-1;;39378:38:25;;;39571:30;;39518:10;;;39378:38;39518:24;;;;;;;;;:::i;:::-;;;;:36;;:83;;-1:-1:-1;;;;;;39518:83:25;;-1:-1:-1;;;;;39518:83:25;;;;;;;;;;;;;;39670:32;;;;39615:10;;;:24;;;;;;;;;;:::i;:::-;;;;:36;;:87;;-1:-1:-1;;;;;39615:87:25;;;-1:-1:-1;;;39615:87:25;;;;;;;;;;;;;;;;;;-1:-1:-1;39615:51:25;39788:4;:24;39768:44;;39875:95;39892:4;:10;;39910:4;39892:24;;;;;;;;;:::i;:::-;;;;39942:25;;;39875:16;:95::i;:::-;39870:229;;39990:94;40019:4;8410:1;40045:9;8548:2;40056:4;:24;40082:1;39990:28;:94::i;:::-;40221:71;40237:4;40259:17;:30;;;-1:-1:-1;;;;;40251:39:25;40243:48;;40221:15;:71::i;48082:927::-;48571:30;48618:103;48640:4;48646:7;48655:29;:42;;;-1:-1:-1;;;;;48618:103:25;48699:7;48708:12;48618:21;:103::i;:::-;48570:151;;;48784:42;48803:22;48784:18;:42::i;:::-;-1:-1:-1;;;;;48732:25:25;;;;;;:16;;;:25;;;;;:94;;:48;;:94;;;;;;;-1:-1:-1;;;;;48732:94:25;;:::i;:::-;;;;;;;;-1:-1:-1;;;;;48732:94:25;;;;;-1:-1:-1;;;;;48732:94:25;;;;;;48945:57;48836:4;:16;;:25;48853:7;-1:-1:-1;;;;;48836:25:25;-1:-1:-1;;;;;48836:25:25;;;;;;;;;;;;:80;;48917:12;48836:94;;;;;;;;:::i;:::-;;;;;;;;;;:166;-1:-1:-1;;;;;;;48082:927:25:o;38302:536::-;38639:65;38659:4;38665:20;38687:7;38696;38639:19;:65::i;:::-;38769:62;38786:4;38792:20;38814:7;38823;38769:16;:62::i;:::-;38302:536;;;;;;:::o;42040:375::-;42191:22;;42149:83;;42176:54;;42218:12;;42191:22;;;-1:-1:-1;;;;;42191:22:25;42176:54;:::i;42149:83::-;42124:108;;-1:-1:-1;;;;;42124:108:25;;;;;;-1:-1:-1;;42124:108:25;;;;;;42272:136;42124:22;42351:16;-1:-1:-1;42351:45:25;;-1:-1:-1;;42351:45:25;;;8073:1;42351:45;42319:4;:27;;;42312:85;;;;:::i;42272:136::-;-1:-1:-1;;;;;42242:166:25;:4;:27;;:166;;;;42040:375;;:::o;33010:2544::-;33315:74;;:::i;:::-;33446:40;;;;33401:42;33446:40;;;:24;;;:40;;;;;;;;33401:85;;;;;;;;;-1:-1:-1;;;;;33401:85:25;;;;;-1:-1:-1;;;33401:85:25;;;;;;;;;;33674:28;;;;33401:85;;:42;33592:228;;33636:24;;33765:23;33802:8;33592:30;:228::i;:::-;33553:267;;33916:34;33996:13;:28;;;-1:-1:-1;;;;;33964:60:25;:28;-1:-1:-1;;;;;33964:60:25;;33960:700;;-1:-1:-1;34130:26:25;;33960:700;;;34452:197;34488:147;34524:28;-1:-1:-1;;;;;34488:147:25;34554:13;:26;;;-1:-1:-1;;;;;34488:147:25;34582:13;:28;;;-1:-1:-1;;;;;34488:147:25;34612:5;34488:14;:147::i;34452:197::-;34423:226;;33960:700;-1:-1:-1;;;;;;;;;;;;;;;;;34758:33:25;;:62;;34794:26;;34758:62;:::i;:::-;-1:-1:-1;;;;;34722:98:25;;;34868:35;;;;:66;;34906:28;;34868:66;:::i;:::-;-1:-1:-1;;;;;34830:104:25;:35;;;:104;;;35006:15;;35024:46;;:9;:46::i;:::-;35117:430;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;35356:150:25;;;;;;;;-1:-1:-1;;;;;35356:150:25;;;;;;;;;;;35117:430;;;;-1:-1:-1;;;;;;;;35117:430:25;;;;;-1:-1:-1;35117:430:25;;33010:2544;-1:-1:-1;;33010:2544:25:o;35830:1968::-;36044:14;;36113:27;;;;36187:24;;;;:37;36252:14;;;;;36330:34;;;;35975:7;36330:34;;;:18;;;:34;;;;;;;36408:24;;;:40;;;;;:63;;;;;;-1:-1:-1;;;;;36408:63:25;;;-1:-1:-1;;;36408:63:25;;;;;;;;;;35975:7;;36044:14;36113:27;36150:74;;;;;36330:34;;36527:12;;:127;;;;;36564:90;36581:4;:10;;36592:7;36581:19;;;;;;;:::i;:::-;;;;36626:25;;;36564:16;:90::i;:::-;36563:91;36527:127;36506:1213;;;36752:62;36769:4;36775:20;36797:7;36806;36752:16;:62::i;:::-;36506:1213;;;36849:7;36839;:17;36835:884;;;36947:159;37006:4;37012:7;37021:6;:27;;;37050:20;37072:7;37081;36947:37;:159::i;36835:884::-;37231:34;;;;;;;;:18;;;:34;;;;;:52;;-1:-1:-1;;37231:52:25;;;;;;;37416:24;;;;:37;37368:10;;;37231:52;37368:19;;;;;;;:::i;:::-;;;;:31;;:85;;-1:-1:-1;;;;;;37368:85:25;;-1:-1:-1;;;;;37368:85:25;;;;;;;;;;;;;;37521:24;;;;:39;;;37471:10;;;37482:7;37471:19;;;;;;;:::i;:::-;;;;:31;;:89;;-1:-1:-1;;;;;37471:89:25;;;-1:-1:-1;;;37471:89:25;;;;;;;;;;;;;;;;;;37647:57;37663:4;37676:26;37647:15;:57::i;:::-;37746:45;37777:4;37783:7;37746:30;:45::i;66796:581::-;66939:12;67082:1;67037:42;67050:4;:10;;67061:5;67050:17;;;;;;;:::i;:::-;;67068:9;67050:28;;;;;;;;:::i;:::-;;;;;;;;;669:66:49;362:18;356:25;;;479:6;;487:1;475:14;468:22;;;;539:10;536:17;-1:-1:-1;533:1:49;529:25;420:9;;418:1;414:16;524:31;;;;621:9;;;-1:-1:-1;;;617:36:49;612:3;608:46;603:133;597:140;;219:536;67037:42:25;67020:9;67032:1;67020:13;8548:2;66999:35;:80;:84;66987:96;;8410:1;67196:5;:27;67192:49;;-1:-1:-1;67232:9:25;67225:16;;67192:49;67305:55;67333:4;67339:5;67347:1;67339:9;67350;67305:27;:55::i;314:117:50:-;377:7;403:21;414:1;417;93:4;840:120;916:7;952:1;943:5;947:1;943;:5;:::i;1322:500:28:-;1386:19;1422:17;1429:4;1435:3;1422:6;:17::i;:::-;1417:35;;-1:-1:-1;1448:4:28;1441:11;;1417:35;1462:11;1490:1;1476:11;1482:4;2022:12;;:19;;1920:129;1476:11;:15;;;;:::i;:::-;1523:21;;;;1501:19;1523:21;;;;;;;;;;;1462:29;;-1:-1:-1;1523:21:28;;;;1558:20;;;;1554:196;;1594:16;1613:4;:12;;1626:4;1613:18;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1645:27;;;;;;;;;;;:42;;-1:-1:-1;;1645:42:28;;;;;;;;;-1:-1:-1;1701:12:28;;:26;;1613:18;;-1:-1:-1;1613:18:28;;1701:12;;:26;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;:38;;;;;;;;;;;;;;;;;;1580:170;1554:196;1766:21;;;:16;:21;;;;;;;;;;1759:28;;-1:-1:-1;;1759:28:28;;;;1797:12;;:18;;;;;;;:::i;:::-;;;;;;;;;-1:-1:-1;;1797:18:28;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;1322:500:28;;;;:::o;63870:1618:25:-;64190:21;:36;;;64159:28;64263:10;;;64274:5;64263:17;;;;;;;:::i;:::-;;64281:9;64263:28;;;;;;;;:::i;:::-;;;;;;;;;;;;-1:-1:-1;96565:1:25;:11;;96646:15;;96639:23;96632:31;64666:17;;;64662:30;;64685:7;;;;;64662:30;97008:1;:11;;97039:13;;64775:75;;64958:11;64927:4;:10;;64938:5;64927:17;;;;;;;:::i;:::-;;64945:9;64927:28;;;;;;;;:::i;:::-;;;;;;;;:42;;;;65038:5;65047:1;65038:10;65034:23;;65050:7;;;;;65034:23;65075:3;65082:1;65075:8;65071:185;;9387:6;97310:22;;65198:35;65194:48;;65235:7;;;;;65194:48;65329:142;65375:4;-1:-1:-1;;65381:9:25;;8548:2;65392:9;:29;8548:2;65423:9;:29;65454:3;65329:28;:142::i;:::-;64061:1421;;;63870:1618;;;;;:::o;40628:1163::-;40872:34;;;;;;;;:18;;;:34;;;;;:49;;-1:-1:-1;;40872:49:25;;;;;;;;;;41056:30;;;41003:10;;;;:24;;;;;;;:::i;:::-;;;;:36;;:83;;-1:-1:-1;;;;;;41003:83:25;;-1:-1:-1;;;;;41003:83:25;;;;;;;;;;;;;;41155:32;;;;41100:10;;;:24;;;;;;;;;;:::i;:::-;;;;:36;;:87;;-1:-1:-1;;;;;41100:87:25;;;-1:-1:-1;;;41100:87:25;;;;;;;;;;;;;;;;;;-1:-1:-1;41100:51:25;41273:4;:24;41253:44;;41358:95;41375:4;:10;;41393:4;41375:24;;;;;;;;;:::i;:::-;;;;41425:25;;;41358:16;:95::i;:::-;41353:229;;41473:94;41502:4;8410:1;41528:9;8548:2;41539:4;:24;41565:1;41473:28;:94::i;:::-;41704:70;41720:4;41741:17;:30;;;-1:-1:-1;;;;;41733:39:25;41704:15;:70::i;84508:1148::-;84771:34;84844:78;84859:24;84885:23;4401:3:30;84916:5:25;84844:14;:78::i;:::-;84817:105;;84985:32;85060:24;85031:26;-1:-1:-1;;;;;85031:53:25;;85027:164;;;85127:53;-1:-1:-1;;;;;85127:53:25;;:24;:53;:::i;:::-;85100:80;;85027:164;85543:95;85552:23;-1:-1:-1;;;;;85543:95:25;85577:60;85592:24;85618:8;85628:1;85631:5;85577:14;:60::i;:::-;85543:8;:95::i;2258:193:28:-;2347:12;;;:19;2327:4;;2347:24;;2343:42;;-1:-1:-1;2380:5:28;2373:12;;2343:42;2402;;;;2415:16;:21;;;;;;;;;;;2402:12;;;:35;;:42;;:12;;2415:21;;;2402:35;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;:42;;2258:193;-1:-1:-1;;;2258:193:28:o;835:235::-;899:21;936:17;943:4;949:3;936:6;:17::i;:::-;932:34;;;-1:-1:-1;962:4:28;955:11;;932:34;976:12;;;;:22;;;;;;;-1:-1:-1;976:22:28;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1039:19;1032:31;;976:12;1032:31;:::i;:::-;1008:21;;;;:16;:21;;;;;;;;;;;;:55;;-1:-1:-1;;1008:55:28;;;;;;;;;;;835:235;:::o;-1:-1:-1:-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;:::i;:::-;;;;:::o;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::o;:::-;;;;;;;;;;;;;;;;;;;;;;;;:::o;:::-;;;;;;;;;;;;;;;;;;;;;;;;:::o;:::-;;;;;;;;;;;;;;;14:163:190;81:20;;141:10;130:22;;120:33;;110:61;;167:1;164;157:12;182:417;255:6;263;271;324:2;312:9;303:7;299:23;295:32;292:52;;;340:1;337;330:12;292:52;379:9;366:23;432:5;429:1;418:20;411:5;408:31;398:59;;453:1;450;443:12;398:59;476:5;-1:-1:-1;500:37:190;533:2;518:18;;500:37;:::i;:::-;490:47;;556:37;589:2;578:9;574:18;556:37;:::i;:::-;546:47;;182:417;;;;;:::o;964:173::-;1032:20;;-1:-1:-1;;;;;1081:31:190;;1071:42;;1061:70;;1127:1;1124;1117:12;1142:160;1207:20;;1263:13;;1256:21;1246:32;;1236:60;;1292:1;1289;1282:12;1307:328;1381:6;1389;1397;1450:2;1438:9;1429:7;1425:23;1421:32;1418:52;;;1466:1;1463;1456:12;1418:52;1489:29;1508:9;1489:29;:::i;:::-;1479:39;;1537:35;1568:2;1557:9;1553:18;1537:35;:::i;:::-;1527:45;;1591:38;1625:2;1614:9;1610:18;1591:38;:::i;1978:420::-;2031:3;2069:5;2063:12;2096:6;2091:3;2084:19;2128:4;2123:3;2119:14;2112:21;;2167:4;2160:5;2156:16;2190:1;2200:173;2214:6;2211:1;2208:13;2200:173;;;2275:13;;2263:26;;2318:4;2309:14;;;;2346:17;;;;2236:1;2229:9;2200:173;;;-1:-1:-1;2389:3:190;;1978:420;-1:-1:-1;;;;1978:420:190:o;2403:1152::-;2582:2;2571:9;2564:21;2545:4;2623:3;2612:9;2608:19;2683:6;2677:13;2670:21;2663:29;2658:2;2647:9;2643:18;2636:57;2761:2;2753:6;2749:15;2743:22;2740:1;2729:37;2724:2;2713:9;2709:18;2702:65;-1:-1:-1;;;;;2825:2:190;2817:6;2813:15;2807:22;2803:59;2798:2;2787:9;2783:18;2776:87;2910:2;2902:6;2898:15;2892:22;2951:4;2945:3;2934:9;2930:19;2923:33;2976:6;3011:12;3005:19;3048:6;3040;3033:22;3086:3;3075:9;3071:19;3064:26;;3131:2;3117:12;3113:21;3099:35;;3152:1;3143:10;;3162:195;3176:6;3173:1;3170:13;3162:195;;;3225:52;3273:3;3264:6;3258:13;1820:12;;-1:-1:-1;;;;;1816:53:190;;;1804:66;;1923:4;1912:16;;;1906:23;1902:64;1886:14;;1879:88;1736:237;3225:52;3306:2;3301:3;3297:12;3290:19;;3344:2;3336:6;3332:15;3322:25;;3198:1;3195;3191:9;3186:14;;3162:195;;;-1:-1:-1;3406:3:190;3394:16;;3388:23;3453:19;;;-1:-1:-1;;3449:33:190;3442:4;3427:20;;3420:63;3388:23;-1:-1:-1;3500:49:190;3457:3;3388:23;3500:49;:::i;3560:160::-;3626:20;;3686:1;3675:20;;;3665:31;;3655:59;;3710:1;3707;3700:12;3725:182;3782:6;3835:2;3823:9;3814:7;3810:23;3806:32;3803:52;;;3851:1;3848;3841:12;3803:52;3874:27;3891:9;3874:27;:::i;3912:348::-;3979:6;3987;4040:2;4028:9;4019:7;4015:23;4011:32;4008:52;;;4056:1;4053;4046:12;4008:52;4079:29;4098:9;4079:29;:::i;:::-;4069:39;;4158:2;4147:9;4143:18;4130:32;4202:8;4195:5;4191:20;4184:5;4181:31;4171:59;;4226:1;4223;4216:12;4171:59;4249:5;4239:15;;;3912:348;;;;;:::o;4265:127::-;4326:10;4321:3;4317:20;4314:1;4307:31;4357:4;4354:1;4347:15;4381:4;4378:1;4371:15;4397:248;4464:2;4458:9;4506:4;4494:17;;4541:18;4526:34;;4562:22;;;4523:62;4520:88;;;4588:18;;:::i;:::-;4624:2;4617:22;4397:248;:::o;4650:252::-;4722:2;4716:9;4764:3;4752:16;;4798:18;4783:34;;4819:22;;;4780:62;4777:88;;;4845:18;;:::i;4907:1371::-;4965:5;5013:6;5001:9;4996:3;4992:19;4988:32;4985:52;;;5033:1;5030;5023:12;4985:52;5055:17;;:::i;:::-;5046:26;;5117:3;5110:4;5099:9;5095:20;5091:30;5081:58;;5135:1;5132;5125:12;5081:58;5159:22;;:::i;:::-;5203:3;5244;5233:9;5229:19;5271:3;5263:6;5260:15;5257:35;;;5288:1;5285;5278:12;5257:35;5312:9;5330:206;5346:6;5341:3;5338:15;5330:206;;;5442:17;;5472:20;;5521:4;5512:14;;;;5363;5330:206;;;-1:-1:-1;5545:20:190;;;5610;5657:4;5646:16;;5639:33;-1:-1:-1;5745:4:190;5730:20;;5717:34;5778:4;5767:16;;5760:33;5866:3;5851:19;;5838:33;5898:4;5887:16;;5880:33;5986:3;5971:19;;5958:33;6018:4;6007:16;;6000:33;6106:3;6091:19;;6078:33;6138:4;6127:16;;6120:33;6226:3;6211:19;;;6198:33;6258:3;6247:15;;6240:32;5552:5;4907:1371;-1:-1:-1;4907:1371:190:o;6283:623::-;6408:6;6416;6424;6432;6440;6493:3;6481:9;6472:7;6468:23;6464:33;6461:53;;;6510:1;6507;6500:12;6461:53;6533:49;6574:7;6563:9;6533:49;:::i;:::-;6523:59;-1:-1:-1;6651:3:190;6636:19;;6623:33;;-1:-1:-1;6753:3:190;6738:19;;6725:33;;-1:-1:-1;6803:39:190;6837:3;6822:19;;6803:39;:::i;:::-;6793:49;;6861:39;6895:3;6884:9;6880:19;6861:39;:::i;:::-;6851:49;;6283:623;;;;;;;;:::o;7164:1450::-;7319:6;7327;7335;7343;7387:9;7378:7;7374:23;7417:3;7413:2;7409:12;7406:32;;;7434:1;7431;7424:12;7406:32;7457:49;7498:7;7487:9;7457:49;:::i;:::-;7447:59;;7525:39;7559:3;7548:9;7544:19;7525:39;:::i;:::-;7515:49;-1:-1:-1;7599:4:190;-1:-1:-1;;7580:17:190;;7576:28;7573:48;;;7617:1;7614;7607:12;7573:48;;7643:17;;:::i;:::-;7733:3;7718:19;;7705:33;7747:22;;7842:3;7827:19;;7814:33;7874:2;7863:14;;7856:31;7960:3;7945:19;;7932:33;7992:2;7981:14;;7974:31;8078:3;8063:19;;8050:33;8110:2;8099:14;;8092:31;8196:3;8181:19;;8168:33;8228:3;8217:15;;8210:32;8315:3;8300:19;;8287:33;8347:3;8336:15;;8329:32;8434:3;8419:19;;8406:33;8466:3;8455:15;;8448:32;7164:1450;;;;-1:-1:-1;7754:5:190;;8577:3;8562:19;8549:33;;-1:-1:-1;;7164:1450:190:o;8811:256::-;8877:6;8885;8938:2;8926:9;8917:7;8913:23;8909:32;8906:52;;;8954:1;8951;8944:12;8906:52;8977:28;8995:9;8977:28;:::i;:::-;8967:38;;9024:37;9057:2;9046:9;9042:18;9024:37;:::i;9072:300::-;9140:6;9148;9201:2;9189:9;9180:7;9176:23;9172:32;9169:52;;;9217:1;9214;9207:12;9169:52;9240:29;9259:9;9240:29;:::i;:::-;9230:39;9338:2;9323:18;;;;9310:32;;-1:-1:-1;;;9072:300:190:o;9377:637::-;9567:2;9579:21;;;9649:13;;9552:18;;;9671:22;;;9519:4;;9750:15;;;9724:2;9709:18;;;9519:4;9793:195;9807:6;9804:1;9801:13;9793:195;;;9872:13;;-1:-1:-1;;;;;9868:39:190;9856:52;;9937:2;9963:15;;;;9928:12;;;;9904:1;9822:9;9793:195;;;-1:-1:-1;10005:3:190;;9377:637;-1:-1:-1;;;;;9377:637:190:o;10019:289::-;10061:3;10099:5;10093:12;10126:6;10121:3;10114:19;10182:6;10175:4;10168:5;10164:16;10157:4;10152:3;10148:14;10142:47;10234:1;10227:4;10218:6;10213:3;10209:16;10205:27;10198:38;10297:4;10290:2;10286:7;10281:2;10273:6;10269:15;10265:29;10260:3;10256:39;10252:50;10245:57;;;10019:289;;;;:::o;10313:579::-;10365:3;10396;10428:5;10422:12;10455:6;10450:3;10443:19;10487:4;10482:3;10478:14;10471:21;;10545:4;10535:6;10532:1;10528:14;10521:5;10517:26;10513:37;10584:4;10577:5;10573:16;10607:1;10617:249;10631:6;10628:1;10625:13;10617:249;;;10718:2;10714:7;10706:5;10700:4;10696:16;10692:30;10687:3;10680:43;10744:38;10777:4;10768:6;10762:13;10744:38;:::i;:::-;10817:4;10842:14;;;;10736:46;;-1:-1:-1;10805:17:190;;;;;10653:1;10646:9;10617:249;;;-1:-1:-1;10882:4:190;;10313:579;-1:-1:-1;;;;;;10313:579:190:o;10897:1035::-;11103:4;11151:2;11140:9;11136:18;11181:2;11170:9;11163:21;11204:6;11239;11233:13;11270:6;11262;11255:22;11308:2;11297:9;11293:18;11286:25;;11370:2;11360:6;11357:1;11353:14;11342:9;11338:30;11334:39;11320:53;;11408:2;11400:6;11396:15;11429:1;11439:464;11453:6;11450:1;11447:13;11439:464;;;11518:22;;;-1:-1:-1;;11514:36:190;11502:49;;11574:13;;11619:9;;-1:-1:-1;;;;;11615:35:190;11600:51;;11698:2;11690:11;;;11684:18;11739:2;11722:15;;;11715:27;;;11684:18;11765:58;;11807:15;;11684:18;11765:58;:::i;:::-;11755:68;-1:-1:-1;;11858:2:190;11881:12;;;;11846:15;;;;;11475:1;11468:9;11439:464;;;-1:-1:-1;11920:6:190;;10897:1035;-1:-1:-1;;;;;;10897:1035:190:o;11937:180::-;11993:6;12046:2;12034:9;12025:7;12021:23;12017:32;12014:52;;;12062:1;12059;12052:12;12014:52;12085:26;12101:9;12085:26;:::i;12315:368::-;12389:6;12397;12405;12458:2;12446:9;12437:7;12433:23;12429:32;12426:52;;;12474:1;12471;12464:12;12426:52;12497:29;12516:9;12497:29;:::i;:::-;12487:39;;12545:35;12576:2;12565:9;12561:18;12545:35;:::i;:::-;12315:368;;12535:45;;-1:-1:-1;;;12649:2:190;12634:18;;;;12621:32;;12315:368::o;12688:863::-;12948:4;12996:3;12985:9;12981:19;13009:51;13050:9;13042:6;1820:12;;-1:-1:-1;;;;;1816:53:190;;;1804:66;;1923:4;1912:16;;;1906:23;1902:64;1886:14;;1879:88;1736:237;13009:51;13091:2;13076:18;;13069:34;;;13139:3;13134:2;13119:18;;13112:31;13192:13;;13214:22;;;;13306:4;13294:17;;;13267:3;13252:19;;;-1:-1:-1;13339:186:190;13353:6;13350:1;13347:13;13339:186;;;13418:13;;13433:6;13414:26;13402:39;;13470:4;13498:17;;;;13461:14;;;;13375:1;13368:9;13339:186;;;-1:-1:-1;13542:3:190;;12688:863;-1:-1:-1;;;;;;;12688:863:190:o;13556:783::-;13660:6;13668;13676;13684;13692;13700;13753:3;13741:9;13732:7;13728:23;13724:33;13721:53;;;13770:1;13767;13760:12;13721:53;13793:29;13812:9;13793:29;:::i;:::-;13783:39;13891:2;13876:18;;13863:32;;-1:-1:-1;13992:2:190;13977:18;;13964:32;;14095:2;14080:18;;14067:32;;-1:-1:-1;14198:3:190;14183:19;;14170:33;;-1:-1:-1;14302:3:190;14287:19;14274:33;;-1:-1:-1;13556:783:190;-1:-1:-1;;;13556:783:190:o;14671:324::-;14743:6;14751;14759;14812:2;14800:9;14791:7;14787:23;14783:32;14780:52;;;14828:1;14825;14818:12;14780:52;14851:29;14870:9;14851:29;:::i;:::-;14841:39;;14899:35;14930:2;14919:9;14915:18;14899:35;:::i;:::-;14889:45;;14953:36;14985:2;14974:9;14970:18;14953:36;:::i;15000:349::-;15266:6;15254:19;;15236:38;;15224:2;15209:18;;15283:60;15339:2;15324:18;;15316:6;1820:12;;-1:-1:-1;;;;;1816:53:190;;;1804:66;;1923:4;1912:16;;;1906:23;1902:64;1886:14;;1879:88;1736:237;15354:446;15406:3;15444:5;15438:12;15471:6;15466:3;15459:19;15503:4;15498:3;15494:14;15487:21;;15542:4;15535:5;15531:16;15565:1;15575:200;15589:6;15586:1;15583:13;15575:200;;;15654:13;;-1:-1:-1;;;;;;15650:40:190;15638:53;;15720:4;15711:14;;;;15748:17;;;;15611:1;15604:9;15575:200;;15805:1145;16025:4;16073:2;16062:9;16058:18;16103:2;16092:9;16085:21;16126:6;16161;16155:13;16192:6;16184;16177:22;16230:2;16219:9;16215:18;16208:25;;16292:2;16282:6;16279:1;16275:14;16264:9;16260:30;16256:39;16242:53;;16330:2;16322:6;16318:15;16351:1;16361:560;16375:6;16372:1;16369:13;16361:560;;;16468:2;16464:7;16452:9;16444:6;16440:22;16436:36;16431:3;16424:49;16502:6;16496:13;16548:2;16542:9;16579:2;16571:6;16564:18;16609:48;16653:2;16645:6;16641:15;16627:12;16609:48;:::i;:::-;16595:62;;16706:2;16702;16698:11;16692:18;16670:40;;16759:6;16751;16747:19;16742:2;16734:6;16730:15;16723:44;16790:51;16834:6;16818:14;16790:51;:::i;:::-;16780:61;-1:-1:-1;;;16876:2:190;16899:12;;;;16864:15;;;;;16397:1;16390:9;16361:560;;17247:254;17312:6;17320;17373:2;17361:9;17352:7;17348:23;17344:32;17341:52;;;17389:1;17386;17379:12;17341:52;17412:27;17429:9;17412:27;:::i;17506:252::-;17570:6;17578;17631:2;17619:9;17610:7;17606:23;17602:32;17599:52;;;17647:1;17644;17637:12;17599:52;17670:27;17687:9;17670:27;:::i;:::-;17660:37;;17716:36;17748:2;17737:9;17733:18;17716:36;:::i;17763:280::-;17962:2;17951:9;17944:21;17925:4;17982:55;18033:2;18022:9;18018:18;18010:6;17982:55;:::i;18256:186::-;18315:6;18368:2;18356:9;18347:7;18343:23;18339:32;18336:52;;;18384:1;18381;18374:12;18336:52;18407:29;18426:9;18407:29;:::i;18623:327::-;18725:5;18748:1;18758:186;18772:4;18769:1;18766:11;18758:186;;;18845:13;;18842:1;18831:28;18819:41;;18889:4;18880:14;;;;18917:17;;;;18792:1;18785:9;18758:186;;18955:316;19046:5;19069:1;19079:186;19093:4;19090:1;19087:11;19079:186;;;19166:13;;19163:1;19152:28;19140:41;;19210:4;19201:14;;;;19238:17;;;;19113:1;19106:9;19079:186;;19276:330;19379:5;19402:1;19412:188;19426:4;19423:1;19420:11;19412:188;;;19489:13;;19504:10;19485:30;19473:43;;19545:4;19536:14;;;;19573:17;;;;19446:1;19439:9;19412:188;;19611:319;19703:5;19726:1;19736:188;19750:4;19747:1;19744:11;19736:188;;;19813:13;;19828:10;19809:30;19797:43;;19869:4;19860:14;;;;19897:17;;;;19770:1;19763:9;19736:188;;19935:1740;20159:13;;1710;1703:21;1691:34;;20129:4;20114:20;;20231:4;20223:6;20219:17;20213:24;20246:51;20291:4;20280:9;20276:20;20262:12;1710:13;1703:21;1691:34;;1640:91;20246:51;;20346:4;20338:6;20334:17;20328:24;20361:54;20409:4;20398:9;20394:20;20378:14;18514:4;18503:16;18491:29;;18447:75;20361:54;;20464:4;20456:6;20452:17;20446:24;20479:54;20527:4;20516:9;20512:20;20496:14;18514:4;18503:16;18491:29;;18447:75;20479:54;;20582:4;20574:6;20570:17;20564:24;20597:54;20645:4;20634:9;20630:20;20614:14;679:1;668:20;656:33;;604:91;20597:54;;20700:4;20692:6;20688:17;20682:24;20715:54;20763:4;20752:9;20748:20;20732:14;679:1;668:20;656:33;;604:91;20715:54;;20818:4;20810:6;20806:17;20800:24;20833:55;20882:4;20871:9;20867:20;20851:14;17031:8;17020:20;17008:33;;16955:92;20833:55;;20937:4;20929:6;20925:17;20919:24;20952:55;21001:4;20990:9;20986:20;20970:14;17031:8;17020:20;17008:33;;16955:92;20952:55;;21056:6;21048;21044:19;21038:26;21073:56;21121:6;21110:9;21106:22;21090:14;18602:1;18591:20;18579:33;;18527:91;21073:56;;21178:6;21170;21166:19;21160:26;21195:73;21260:6;21249:9;21245:22;21229:14;21195:73;:::i;:::-;;21317:6;21309;21305:19;21299:26;21334:62;21388:6;21377:9;21373:22;21357:14;21334:62;:::i;:::-;;21446:6;21438;21434:19;21428:26;21463:75;21530:6;21519:9;21515:22;21498:15;21463:75;:::i;:::-;;21588:6;21580;21576:19;21570:26;21605:64;21661:6;21650:9;21646:22;21629:15;21605:64;:::i;:::-;;19935:1740;;;;:::o;21680:1033::-;21884:4;21932:2;21921:9;21917:18;21962:2;21951:9;21944:21;21985:6;22020;22014:13;22051:6;22043;22036:22;22089:2;22078:9;22074:18;22067:25;;22151:2;22141:6;22138:1;22134:14;22123:9;22119:30;22115:39;22101:53;;22189:2;22181:6;22177:15;22210:1;22220:464;22234:6;22231:1;22228:13;22220:464;;;22299:22;;;-1:-1:-1;;22295:36:190;22283:49;;22355:13;;22400:9;;-1:-1:-1;;;;;22396:35:190;22381:51;;22479:2;22471:11;;;22465:18;22520:2;22503:15;;;22496:27;;;22465:18;22546:58;;22588:15;;22465:18;22546:58;:::i;:::-;22536:68;-1:-1:-1;;22639:2:190;22662:12;;;;22627:15;;;;;22256:1;22249:9;22220:464;;22718:254;22783:6;22791;22844:2;22832:9;22823:7;22819:23;22815:32;22812:52;;;22860:1;22857;22850:12;22812:52;22883:29;22902:9;22883:29;:::i;:::-;22873:39;;22931:35;22962:2;22951:9;22947:18;22931:35;:::i;23282:312::-;23380:6;23388;23441:3;23429:9;23420:7;23416:23;23412:33;23409:53;;;23458:1;23455;23448:12;23409:53;23481:49;23522:7;23511:9;23481:49;:::i;:::-;23471:59;;23549:39;23583:3;23572:9;23568:19;23549:39;:::i;23599:324::-;23671:6;23679;23687;23740:2;23728:9;23719:7;23715:23;23711:32;23708:52;;;23756:1;23753;23746:12;23708:52;23779:29;23798:9;23779:29;:::i;:::-;23769:39;;23827:36;23859:2;23848:9;23844:18;23827:36;:::i;:::-;23817:46;;23882:35;23913:2;23902:9;23898:18;23882:35;:::i;24203:380::-;24282:1;24278:12;;;;24325;;;24346:61;;24400:4;24392:6;24388:17;24378:27;;24346:61;24453:2;24445:6;24442:14;24422:18;24419:38;24416:161;;24499:10;24494:3;24490:20;24487:1;24480:31;24534:4;24531:1;24524:15;24562:4;24559:1;24552:15;24588:127;24649:10;24644:3;24640:20;24637:1;24630:31;24680:4;24677:1;24670:15;24704:4;24701:1;24694:15;25498:184;25568:6;25621:2;25609:9;25600:7;25596:23;25592:32;25589:52;;;25637:1;25634;25627:12;25589:52;-1:-1:-1;25660:16:190;;25498:184;-1:-1:-1;25498:184:190:o;25687:127::-;25748:10;25743:3;25739:20;25736:1;25729:31;25779:4;25776:1;25769:15;25803:4;25800:1;25793:15;25819:185;25915:1;25886:16;;;25904;;;;25882:39;25967:5;25936:16;;-1:-1:-1;;25954:20:190;;25933:42;25930:68;;;25978:18;;:::i;26009:158::-;26102:6;26095:14;;;26079;;;26075:35;;26122:16;;26119:42;;;26141:18;;:::i;26172:168::-;26245:9;;;26276;;26293:15;;;26287:22;;26273:37;26263:71;;26314:18;;:::i;26345:243::-;-1:-1:-1;;;;;26460:42:190;;;26416;;;26412:91;;26515:44;;26512:70;;;26562:18;;:::i;26593:125::-;26658:9;;;26679:10;;;26676:36;;;26692:18;;:::i;26723:135::-;26762:3;26783:17;;;26780:43;;26803:18;;:::i;:::-;-1:-1:-1;26850:1:190;26839:13;;26723:135::o;26863:128::-;26930:9;;;26951:11;;;26948:37;;;26965:18;;:::i;26996:375::-;27084:1;27102:5;27116:249;27137:1;27127:8;27124:15;27116:249;;;27187:4;27182:3;27178:14;27172:4;27169:24;27166:50;;;27196:18;;:::i;:::-;27246:1;27236:8;27232:16;27229:49;;;27260:16;;;;27229:49;27343:1;27339:16;;;;;27299:15;;27116:249;;27376:902;27425:5;27455:8;27445:80;;-1:-1:-1;27496:1:190;27510:5;;27445:80;27544:4;27534:76;;-1:-1:-1;27581:1:190;27595:5;;27534:76;27626:4;27644:1;27639:59;;;;27712:1;27707:174;;;;27619:262;;27639:59;27669:1;27660:10;;27683:5;;;27707:174;27744:3;27734:8;27731:17;27728:43;;;27751:18;;:::i;:::-;-1:-1:-1;;27807:1:190;27793:16;;27866:5;;27619:262;;27965:2;27955:8;27952:16;27946:3;27940:4;27937:13;27933:36;27927:2;27917:8;27914:16;27909:2;27903:4;27900:12;27896:35;27893:77;27890:203;;;-1:-1:-1;28002:19:190;;;28078:5;;27890:203;28125:42;-1:-1:-1;;28150:8:190;28144:4;28125:42;:::i;:::-;28203:6;28199:1;28195:6;28191:19;28182:7;28179:32;28176:58;;;28214:18;;:::i;28283:140::-;28341:5;28370:47;28411:4;28401:8;28397:19;28391:4;28370:47;:::i;28428:127::-;28489:10;28484:3;28480:20;28477:1;28470:31;28520:4;28517:1;28510:15;28544:4;28541:1;28534:15;28560:120;28600:1;28626;28616:35;;28631:18;;:::i;:::-;-1:-1:-1;28665:9:190;;28560:120::o;28685:237::-;28757:9;;;28724:7;28782:9;;-1:-1:-1;;;28793:18:190;;28778:34;28775:60;;;28815:18;;:::i;:::-;28888:1;28879:7;28874:16;28871:1;28868:23;28864:1;28857:9;28854:38;28844:72;;28896:18;;:::i;28927:216::-;28991:9;;;29019:11;;;28966:3;29049:9;;29077:10;;29073:19;;29102:10;;29094:19;;29070:44;29067:70;;;29117:18;;:::i;29420:155::-;29511:6;29488:14;;;29504;;;29484:35;;29531:15;;29528:41;;;29549:18;;:::i;29580:228::-;-1:-1:-1;;;;;29649:38:190;;;29689;;;29645:83;;29740:39;;29737:65;;;29782:18;;:::i;29813:151::-;29903:4;29896:12;;;29882;;;29878:31;;29921:14;;29918:40;;;29938:18;;:::i;30241:136::-;30276:3;-1:-1:-1;;;30297:22:190;;30294:48;;30322:18;;:::i;:::-;-1:-1:-1;30362:1:190;30358:13;;30241:136::o;30382:240::-;-1:-1:-1;;;;;30451:42:190;;;30495;;;30447:91;;30550:43;;30547:69;;;30596:18;;:::i;30627:112::-;30658:1;30684;30674:35;;30689:18;;:::i;:::-;-1:-1:-1;30723:10:190;;30627:112::o;30744:193::-;30783:1;30809;30799:35;;30814:18;;:::i;:::-;-1:-1:-1;;;30850:18:190;;-1:-1:-1;;30870:13:190;;30846:38;30843:64;;;30887:18;;:::i;:::-;-1:-1:-1;30921:10:190;;30744:193::o;30942:127::-;31003:10;30998:3;30994:20;30991:1;30984:31;31034:4;31031:1;31024:15;31058:4;31055:1;31048:15", "linkReferences": {}, "immutableReferences": {"3142": [{"start": 1135, "length": 32}, {"start": 1950, "length": 32}], "3144": [{"start": 1009, "length": 32}, {"start": 1983, "length": 32}]}}, "methodIdentifiers": {"IS_TEST()": "fa7626d4", "accruePenalties(address,uint256,uint256,uint256,uint256,uint256)": "5cb5fb0a", "boundTick(int16)": "beca5a67", "calcSatChangeRatioBips((uint256[6],uint256,uint256,uint256,uint256,uint256,uint256),uint256,uint256,address,address)": "13ed39d9", "configLongTermInterval(address,uint24)": "08c91120", "excludeArtifacts()": "b5508aa9", "excludeContracts()": "e20c9f71", "excludeSelectors()": "b0464fdc", "excludeSenders()": "1ed7831c", "failed()": "ba414fa6", "getAccount(address,bool,address)": "01cbfeb2", "getLeafDetails(address,bool,uint256)": "4d003c0e", "getLendingStateTick(int56,uint32,uint32)": "00ba55ec", "getLendingStateTickAndCheckpoint(uint32,uint32)": "15f0cc2f", "getObservations(address)": "8ecc5eda", "getObservedMidTermTick(bool)": "3d011d65", "getTickRange(address,int16,bool)": "ecabd7aa", "getTrancheDetails(address,bool,int16)": "668243a7", "getTreeDetails(address,bool)": "b0016809", "init(int16)": "********", "liquidationCheckHardPremiums((uint256[6],uint256,uint256,uint256,uint256,uint256,uint256),address,(uint256,uint256,uint256,uint256,uint256,uint256,uint256),uint256)": "159e7cd7", "longTermIntervalConfig()": "6c8c3191", "midTermIntervalConfig()": "88205b40", "owner()": "8da5cb5b", "recordObservation(int16,uint32)": "6ef9c968", "renounceOwnership()": "715018a6", "setNewPositionSaturation(address,uint256)": "1b060ac7", "setTickRange(int16,int16)": "807cca85", "targetArtifactSelectors()": "66d9a9a0", "targetArtifacts()": "85226c81", "targetContracts()": "3f7286f4", "targetInterfaces()": "2ade3880", "targetSelectors()": "916a17c6", "targetSenders()": "3e5e3c23", "transferOwnership(address)": "f2fde38b", "update((uint256[6],uint256,uint256,uint256,uint256,uint256,uint256),address)": "df31e2cc"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"bool\",\"name\":\"_stubSaturation\",\"type\":\"bool\"},{\"internalType\":\"bool\",\"name\":\"_stubTWAP\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"inputs\":[],\"name\":\"AmmalgamMaxSlippage\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"CannotUpdateZeroAddress\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidIntervalConfig\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidUserConfiguration\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"LiquidationPremiumTooHigh\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"MaxTrancheOverSaturated\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"}],\"name\":\"OwnableInvalidOwner\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"OwnableUnauthorizedAccount\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"PairAlreadyExists\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"PairDoesNotExist\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"PriceOutOfBounds\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"uint8\",\"name\":\"bits\",\"type\":\"uint8\"},{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"SafeCastOverflowedUintDowncast\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"TickOutOfBounds\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"previousOwner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"OwnershipTransferred\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"int16\",\"name\":\"lendingStateTick\",\"type\":\"int16\"}],\"name\":\"UpdateLendingTick\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"name\":\"log\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"name\":\"log_address\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint256[]\",\"name\":\"val\",\"type\":\"uint256[]\"}],\"name\":\"log_array\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"int256[]\",\"name\":\"val\",\"type\":\"int256[]\"}],\"name\":\"log_array\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address[]\",\"name\":\"val\",\"type\":\"address[]\"}],\"name\":\"log_array\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"bytes\",\"name\":\"\",\"type\":\"bytes\"}],\"name\":\"log_bytes\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"name\":\"log_bytes32\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"int256\",\"name\":\"\",\"type\":\"int256\"}],\"name\":\"log_int\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"val\",\"type\":\"address\"}],\"name\":\"log_named_address\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"uint256[]\",\"name\":\"val\",\"type\":\"uint256[]\"}],\"name\":\"log_named_array\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"int256[]\",\"name\":\"val\",\"type\":\"int256[]\"}],\"name\":\"log_named_array\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"address[]\",\"name\":\"val\",\"type\":\"address[]\"}],\"name\":\"log_named_array\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"bytes\",\"name\":\"val\",\"type\":\"bytes\"}],\"name\":\"log_named_bytes\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"bytes32\",\"name\":\"val\",\"type\":\"bytes32\"}],\"name\":\"log_named_bytes32\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"int256\",\"name\":\"val\",\"type\":\"int256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"decimals\",\"type\":\"uint256\"}],\"name\":\"log_named_decimal_int\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"val\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"decimals\",\"type\":\"uint256\"}],\"name\":\"log_named_decimal_uint\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"int256\",\"name\":\"val\",\"type\":\"int256\"}],\"name\":\"log_named_int\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"string\",\"name\":\"val\",\"type\":\"string\"}],\"name\":\"log_named_string\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"val\",\"type\":\"uint256\"}],\"name\":\"log_named_uint\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"name\":\"log_string\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"name\":\"log_uint\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"bytes\",\"name\":\"\",\"type\":\"bytes\"}],\"name\":\"logs\",\"type\":\"event\"},{\"inputs\":[],\"name\":\"IS_TEST\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"externalLiquidity\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"duration\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"allAssetsDepositL\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"allAssetsBorrowL\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"allSharesBorrowL\",\"type\":\"uint256\"}],\"name\":\"accruePenalties\",\"outputs\":[{\"internalType\":\"uint112\",\"name\":\"penaltyInBorrowLShares\",\"type\":\"uint112\"},{\"internalType\":\"uint112\",\"name\":\"accountPenaltyInBorrowLShares\",\"type\":\"uint112\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"int16\",\"name\":\"newTick\",\"type\":\"int16\"}],\"name\":\"boundTick\",\"outputs\":[{\"internalType\":\"int16\",\"name\":\"\",\"type\":\"int16\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"components\":[{\"internalType\":\"uint256[6]\",\"name\":\"userAssets\",\"type\":\"uint256[6]\"},{\"internalType\":\"uint256\",\"name\":\"sqrtPriceMinInQ72\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"sqrtPriceMaxInQ72\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"activeLiquidityScalerInQ72\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"activeLiquidityAssets\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"reservesXAssets\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"reservesYAssets\",\"type\":\"uint256\"}],\"internalType\":\"struct Validation.InputParams\",\"name\":\"inputParams\",\"type\":\"tuple\"},{\"internalType\":\"uint256\",\"name\":\"liqSqrtPriceInXInQ72\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"liqSqrtPriceInYInQ72\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"pairAddress\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"calcSatChangeRatioBips\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"ratioNetXBips\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"ratioNetYBips\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"pairAddress\",\"type\":\"address\"},{\"internalType\":\"uint24\",\"name\":\"_longTermIntervalConfig\",\"type\":\"uint24\"}],\"name\":\"configLongTermInterval\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"excludeArtifacts\",\"outputs\":[{\"internalType\":\"string[]\",\"name\":\"excludedArtifacts_\",\"type\":\"string[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"excludeContracts\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"excludedContracts_\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"excludeSelectors\",\"outputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"addr\",\"type\":\"address\"},{\"internalType\":\"bytes4[]\",\"name\":\"selectors\",\"type\":\"bytes4[]\"}],\"internalType\":\"struct StdInvariant.FuzzSelector[]\",\"name\":\"excludedSelectors_\",\"type\":\"tuple[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"excludeSenders\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"excludedSenders_\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"failed\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"pairAddress\",\"type\":\"address\"},{\"internalType\":\"bool\",\"name\":\"netDebtX\",\"type\":\"bool\"},{\"internalType\":\"address\",\"name\":\"accountAddress\",\"type\":\"address\"}],\"name\":\"getAccount\",\"outputs\":[{\"components\":[{\"internalType\":\"bool\",\"name\":\"exists\",\"type\":\"bool\"},{\"internalType\":\"int16\",\"name\":\"lastTranche\",\"type\":\"int16\"},{\"internalType\":\"uint112\",\"name\":\"penaltyInBorrowLShares\",\"type\":\"uint112\"},{\"components\":[{\"internalType\":\"uint128\",\"name\":\"satInLAssets\",\"type\":\"uint128\"},{\"internalType\":\"uint128\",\"name\":\"satRelativeToL\",\"type\":\"uint128\"}],\"internalType\":\"struct Saturation.SaturationPair[]\",\"name\":\"satPairPerTranche\",\"type\":\"tuple[]\"},{\"internalType\":\"uint256[]\",\"name\":\"treePenaltyAtOnsetInBorrowLSharesPerSatInQ72PerTranche\",\"type\":\"uint256[]\"}],\"internalType\":\"struct Saturation.Account\",\"name\":\"\",\"type\":\"tuple\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"pairAddress\",\"type\":\"address\"},{\"internalType\":\"bool\",\"name\":\"netDebtX\",\"type\":\"bool\"},{\"internalType\":\"uint256\",\"name\":\"leafIndex\",\"type\":\"uint256\"}],\"name\":\"getLeafDetails\",\"outputs\":[{\"components\":[{\"internalType\":\"uint128\",\"name\":\"satInLAssets\",\"type\":\"uint128\"},{\"internalType\":\"uint128\",\"name\":\"satRelativeToL\",\"type\":\"uint128\"}],\"internalType\":\"struct Saturation.SaturationPair\",\"name\":\"saturation\",\"type\":\"tuple\"},{\"internalType\":\"uint256\",\"name\":\"currentPenaltyInBorrowLSharesPerSatInQ72\",\"type\":\"uint256\"},{\"internalType\":\"uint16[]\",\"name\":\"tranches\",\"type\":\"uint16[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"int56\",\"name\":\"newTick\",\"type\":\"int56\"},{\"internalType\":\"uint32\",\"name\":\"timeElapsedSinceUpdate\",\"type\":\"uint32\"},{\"internalType\":\"uint32\",\"name\":\"timeElapsedSinceLendingUpdate\",\"type\":\"uint32\"}],\"name\":\"getLendingStateTick\",\"outputs\":[{\"internalType\":\"int16\",\"name\":\"lendingStateTick\",\"type\":\"int16\"},{\"internalType\":\"uint256\",\"name\":\"maxSatInWads\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint32\",\"name\":\"timeElapsedSinceUpdate\",\"type\":\"uint32\"},{\"internalType\":\"uint32\",\"name\":\"timeElapsedSinceLendingUpdate\",\"type\":\"uint32\"}],\"name\":\"getLendingStateTickAndCheckpoint\",\"outputs\":[{\"internalType\":\"int16\",\"name\":\"lendingStateTick\",\"type\":\"int16\"},{\"internalType\":\"uint256\",\"name\":\"maxSatInWads\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"pairAddress\",\"type\":\"address\"}],\"name\":\"getObservations\",\"outputs\":[{\"components\":[{\"internalType\":\"bool\",\"name\":\"isMidTermBufferInitialized\",\"type\":\"bool\"},{\"internalType\":\"bool\",\"name\":\"isLongTermBufferInitialized\",\"type\":\"bool\"},{\"internalType\":\"uint8\",\"name\":\"midTermIndex\",\"type\":\"uint8\"},{\"internalType\":\"uint8\",\"name\":\"longTermIndex\",\"type\":\"uint8\"},{\"internalType\":\"int16\",\"name\":\"lastTick\",\"type\":\"int16\"},{\"internalType\":\"int16\",\"name\":\"lastLendingStateTick\",\"type\":\"int16\"},{\"internalType\":\"uint24\",\"name\":\"midTermIntervalConfig\",\"type\":\"uint24\"},{\"internalType\":\"uint24\",\"name\":\"longTermIntervalConfig\",\"type\":\"uint24\"},{\"internalType\":\"int56\",\"name\":\"lendingCumulativeSum\",\"type\":\"int56\"},{\"internalType\":\"int56[51]\",\"name\":\"midTermCumulativeSum\",\"type\":\"int56[51]\"},{\"internalType\":\"int56[9]\",\"name\":\"longTermCumulativeSum\",\"type\":\"int56[9]\"},{\"internalType\":\"uint32[51]\",\"name\":\"midTermTimeInterval\",\"type\":\"uint32[51]\"},{\"internalType\":\"uint32[9]\",\"name\":\"longTermTimeInterval\",\"type\":\"uint32[9]\"}],\"internalType\":\"struct GeometricTWAP.Observations\",\"name\":\"\",\"type\":\"tuple\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bool\",\"name\":\"isLongTermBufferInitialized\",\"type\":\"bool\"}],\"name\":\"getObservedMidTermTick\",\"outputs\":[{\"internalType\":\"int16\",\"name\":\"\",\"type\":\"int16\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"pair\",\"type\":\"address\"},{\"internalType\":\"int16\",\"name\":\"currentTick\",\"type\":\"int16\"},{\"internalType\":\"bool\",\"name\":\"includeLongTermTick\",\"type\":\"bool\"}],\"name\":\"getTickRange\",\"outputs\":[{\"internalType\":\"int16\",\"name\":\"\",\"type\":\"int16\"},{\"internalType\":\"int16\",\"name\":\"\",\"type\":\"int16\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"pairAddress\",\"type\":\"address\"},{\"internalType\":\"bool\",\"name\":\"netDebtX\",\"type\":\"bool\"},{\"internalType\":\"int16\",\"name\":\"tranche\",\"type\":\"int16\"}],\"name\":\"getTrancheDetails\",\"outputs\":[{\"internalType\":\"uint16\",\"name\":\"leaf\",\"type\":\"uint16\"},{\"components\":[{\"internalType\":\"uint128\",\"name\":\"satInLAssets\",\"type\":\"uint128\"},{\"internalType\":\"uint128\",\"name\":\"satRelativeToL\",\"type\":\"uint128\"}],\"internalType\":\"struct Saturation.SaturationPair\",\"name\":\"saturation\",\"type\":\"tuple\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"pairAddress\",\"type\":\"address\"},{\"internalType\":\"bool\",\"name\":\"netDebtX\",\"type\":\"bool\"}],\"name\":\"getTreeDetails\",\"outputs\":[{\"internalType\":\"uint16\",\"name\":\"\",\"type\":\"uint16\"},{\"internalType\":\"uint128\",\"name\":\"\",\"type\":\"uint128\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"int16\",\"name\":\"firstTick\",\"type\":\"int16\"}],\"name\":\"init\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"components\":[{\"internalType\":\"uint256[6]\",\"name\":\"userAssets\",\"type\":\"uint256[6]\"},{\"internalType\":\"uint256\",\"name\":\"sqrtPriceMinInQ72\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"sqrtPriceMaxInQ72\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"activeLiquidityScalerInQ72\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"activeLiquidityAssets\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"reservesXAssets\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"reservesYAssets\",\"type\":\"uint256\"}],\"internalType\":\"struct Validation.InputParams\",\"name\":\"inputParams\",\"type\":\"tuple\"},{\"internalType\":\"address\",\"name\":\"borrower\",\"type\":\"address\"},{\"components\":[{\"internalType\":\"uint256\",\"name\":\"depositLToBeTransferredInLAssets\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"depositXToBeTransferredInXAssets\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"depositYToBeTransferredInYAssets\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"repayLXInXAssets\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"repayLYInYAssets\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"repayXInXAssets\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"repayYInYAssets\",\"type\":\"uint256\"}],\"internalType\":\"struct Liquidation.HardLiquidationParams\",\"name\":\"hardLiquidationParams\",\"type\":\"tuple\"},{\"internalType\":\"uint256\",\"name\":\"actualRepaidLiquidityAssets\",\"type\":\"uint256\"}],\"name\":\"liquidationCheckHardPremiums\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"badDebt\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"longTermIntervalConfig\",\"outputs\":[{\"internalType\":\"uint24\",\"name\":\"\",\"type\":\"uint24\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"midTermIntervalConfig\",\"outputs\":[{\"internalType\":\"uint24\",\"name\":\"\",\"type\":\"uint24\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"owner\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"int16\",\"name\":\"newTick\",\"type\":\"int16\"},{\"internalType\":\"uint32\",\"name\":\"timeElapsed\",\"type\":\"uint32\"}],\"name\":\"recordObservation\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"renounceOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"pair\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"maxDesiredSaturationMag2\",\"type\":\"uint256\"}],\"name\":\"setNewPositionSaturation\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"int16\",\"name\":\"tickMin\",\"type\":\"int16\"},{\"internalType\":\"int16\",\"name\":\"tickMax\",\"type\":\"int16\"}],\"name\":\"setTickRange\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetArtifactSelectors\",\"outputs\":[{\"components\":[{\"internalType\":\"string\",\"name\":\"artifact\",\"type\":\"string\"},{\"internalType\":\"bytes4[]\",\"name\":\"selectors\",\"type\":\"bytes4[]\"}],\"internalType\":\"struct StdInvariant.FuzzArtifactSelector[]\",\"name\":\"targetedArtifactSelectors_\",\"type\":\"tuple[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetArtifacts\",\"outputs\":[{\"internalType\":\"string[]\",\"name\":\"targetedArtifacts_\",\"type\":\"string[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetContracts\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"targetedContracts_\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetInterfaces\",\"outputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"addr\",\"type\":\"address\"},{\"internalType\":\"string[]\",\"name\":\"artifacts\",\"type\":\"string[]\"}],\"internalType\":\"struct StdInvariant.FuzzInterface[]\",\"name\":\"targetedInterfaces_\",\"type\":\"tuple[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetSelectors\",\"outputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"addr\",\"type\":\"address\"},{\"internalType\":\"bytes4[]\",\"name\":\"selectors\",\"type\":\"bytes4[]\"}],\"internalType\":\"struct StdInvariant.FuzzSelector[]\",\"name\":\"targetedSelectors_\",\"type\":\"tuple[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetSenders\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"targetedSenders_\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"transferOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"components\":[{\"internalType\":\"uint256[6]\",\"name\":\"userAssets\",\"type\":\"uint256[6]\"},{\"internalType\":\"uint256\",\"name\":\"sqrtPriceMinInQ72\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"sqrtPriceMaxInQ72\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"activeLiquidityScalerInQ72\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"activeLiquidityAssets\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"reservesXAssets\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"reservesYAssets\",\"type\":\"uint256\"}],\"internalType\":\"struct Validation.InputParams\",\"name\":\"inputParams\",\"type\":\"tuple\"},{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"update\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"errors\":{\"OwnableInvalidOwner(address)\":[{\"details\":\"The owner is not a valid owner account. (eg. `address(0)`)\"}],\"OwnableUnauthorizedAccount(address)\":[{\"details\":\"The caller account is not authorized to perform an operation.\"}],\"SafeCastOverflowedUintDowncast(uint8,uint256)\":[{\"details\":\"Value doesn't fit in an uint of `bits` size.\"}]},\"events\":{\"UpdateLendingTick(int16)\":{\"details\":\"Emitted when `lendingStateTick` is updated\",\"params\":{\"lendingStateTick\":\"The updated value for lending state tick\"}}},\"kind\":\"dev\",\"methods\":{\"accruePenalties(address,uint256,uint256,uint256,uint256,uint256)\":{\"params\":{\"allAssetsBorrowL\":\"allAsset[BORROW_L]\",\"allAssetsDepositL\":\"allAsset[DEPOSIT_L]\",\"allSharesBorrowL\":\"allShares[BORROW_L]\",\"duration\":\"since last accrual of penalties\",\"externalLiquidity\":\"Swap liquidity outside this pool\"}},\"boundTick(int16)\":{\"details\":\"The function ensures that `newTick` stays within the bounds      determined by `lastTick` and a dynamically calculated factor.\",\"params\":{\"newTick\":\"The proposed new tick value to be adjusted within valid bounds.\"},\"returns\":{\"_0\":\"The adjusted tick value constrained within the allowable range.\"}},\"calcSatChangeRatioBips((uint256[6],uint256,uint256,uint256,uint256,uint256,uint256),uint256,uint256,address,address)\":{\"params\":{\"account\":\"The account for which we are calculating the saturation change ratio.\",\"inputParams\":\"The params containing the position of `account`.\",\"liqSqrtPriceInXInQ72\":\"The liquidation price.\",\"pairAddress\":\"The address of the pair\"},\"returns\":{\"ratioNetXBips\":\"The ratio representing the change in netX saturation for account.\",\"ratioNetYBips\":\"The ratio representing the change in netY saturation for account.\"}},\"configLongTermInterval(address,uint24)\":{\"details\":\"This function is used to set the long-term interval between observations for the long-term buffer.\",\"params\":{\"_longTermIntervalConfig\":\"The desired duration for each long-term period.      The size is set as a factor of the mid-term interval to ensure a sufficient buffer, requiring      at least 16 * 12 = 192 seconds per period, resulting in a total of ~25 minutes (192 * 8 = 1536 seconds)      for the long-term buffer.\",\"pairAddress\":\"The address of the pair for which the long-term interval is being configured.\"}},\"getLendingStateTick(int56,uint32,uint32)\":{\"params\":{\"newTick\":\"The new tick value to be recorded, representing the most recent update of reserveXAssets and reserveYAssets.\",\"timeElapsedSinceLendingUpdate\":\"The time elapsed since the last lending update.\",\"timeElapsedSinceUpdate\":\"The time elapsed since the last price update.\"},\"returns\":{\"lendingStateTick\":\"The tick value representing the TWAP since the last lending update.\",\"maxSatInWads\":\"The maximum saturation in WADs.\"}},\"getLendingStateTickAndCheckpoint(uint32,uint32)\":{\"details\":\"See `getLendingStateTick` for implementation details which was      separated to allow view access without any state updates.\",\"params\":{\"timeElapsedSinceLendingUpdate\":\"The time elapsed since the last lending update.\",\"timeElapsedSinceUpdate\":\"The time elapsed since the last price update.\"},\"returns\":{\"lendingStateTick\":\"The tick value representing the TWAP since the last lending update.\"}},\"getObservedMidTermTick(bool)\":{\"details\":\"Retrieves the mid-term tick value based on the stored observations.\",\"params\":{\"isLongTermBufferInitialized\":\"Boolean value which represents whether long-term buffer is filled or not.\"},\"returns\":{\"_0\":\"midTermTick The mid-term tick value.\"}},\"getTickRange(address,int16,bool)\":{\"details\":\"This function calculates the minimum and maximum tick values among three observed ticks:          long-term tick, mid-term tick, and current tick.\",\"params\":{\"currentTick\":\"The current (most recent) tick based on the current reserves.\",\"includeLongTermTick\":\"Boolean value indicating whether to include the long-term tick in the range.\",\"pair\":\"The address of the pair for which the tick range is being calculated.\"},\"returns\":{\"_0\":\"minTick The minimum tick value among the three observed ticks.\",\"_1\":\"maxTick The maximum tick value among the three observed ticks.\"}},\"init(int16)\":{\"details\":\"initCheck can be removed once the tree structure is fixed\"},\"owner()\":{\"details\":\"Returns the address of the current owner.\"},\"recordObservation(int16,uint32)\":{\"details\":\"This function is used to record new observation data for the contract. It ensures that      the provided tick value is stored appropriately in both mid-term and long-term      observations, updates interval counters, and handles tick cumulative values based      on the current interval configuration. Ensures that this function is called in      chronological order, with increasing timestamps. Returns in case the      provided block timestamp is less than or equal to the last recorded timestamp.\",\"params\":{\"newTick\":\"The new tick value to be recorded, representing the most recent update of      reserveXAssets and reserveYAssets.\",\"timeElapsed\":\"The time elapsed since the last observation.\"},\"returns\":{\"_0\":\"bool indicating whether the observation was recorded or not.\"}},\"renounceOwnership()\":{\"details\":\"Leaves the contract without owner. It will not be possible to call `onlyOwner` functions. Can only be called by the current owner. NOTE: Renouncing ownership will leave the contract without an owner, thereby disabling any functionality that is only available to the owner.\"},\"transferOwnership(address)\":{\"details\":\"Transfers ownership of the contract to a new account (`newOwner`). Can only be called by the current owner.\"},\"update((uint256[6],uint256,uint256,uint256,uint256,uint256,uint256),address)\":{\"details\":\"run accruePenalties before running this function\",\"params\":{\"account\":\"for which is position is being updated\",\"inputParams\":\"contains the position and pair params, like account borrows/deposits, current price and active liquidity\"}}},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{\"accruePenalties(address,uint256,uint256,uint256,uint256,uint256)\":{\"notice\":\"accrue penalties since last accrual based on all over saturated positions\"},\"calcSatChangeRatioBips((uint256[6],uint256,uint256,uint256,uint256,uint256,uint256),uint256,uint256,address,address)\":{\"notice\":\"Calculate the ratio by which the saturation has changed for `account`.\"},\"configLongTermInterval(address,uint24)\":{\"notice\":\"Configures the interval of long-term observations.\"},\"getLendingStateTick(int56,uint32,uint32)\":{\"notice\":\"Gets the tick value representing the TWAP since the last lending update.\"},\"getLendingStateTickAndCheckpoint(uint32,uint32)\":{\"notice\":\"Gets the tick value representing the TWAP since the last         lending update and checkpoints the current lending cumulative sum         as `self.lendingCumulativeSum` and the current block timestamp as `self.lastLendingTimestamp`.\"},\"getTickRange(address,int16,bool)\":{\"notice\":\"Gets the min and max range of tick values from the stored oracle observations.\"},\"init(int16)\":{\"notice\":\"initializes the sat and TWAP struct\"},\"recordObservation(int16,uint32)\":{\"notice\":\"Records a new observation tick value and updates the observation data.\"},\"update((uint256[6],uint256,uint256,uint256,uint256,uint256,uint256),address)\":{\"notice\":\"update the borrow position of an account and potentially check (and revert) if the resulting sat is too high\"}},\"notice\":\"Test to ignore build size.\",\"version\":1}},\"settings\":{\"compilationTarget\":{\"test/shared/FactoryPairTestFixture.sol\":\"StubSaturationAndGeometricTWAPState\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":1inch/=lib/1inch/\",\":@1inch/=lib/1inch/\",\":@mangrovedao/mangrove-core/=lib/mangrove-core/\",\":@mgv/forge-std/=lib/mangrove-core/lib/forge-std/src/\",\":@mgv/lib/=lib/mangrove-core/lib/\",\":@mgv/script/=lib/mangrove-core/script/\",\":@mgv/src/=lib/mangrove-core/src/\",\":@mgv/test/=lib/mangrove-core/test/\",\":@morpho-org/morpho-blue/=lib/morpho-blue/\",\":@openzeppelin/=lib/openzeppelin-contracts/\",\":ExcessivelySafeCall/=lib/ExcessivelySafeCall/src/\",\":core/=lib/mangrove-core/lib/core/\",\":ds-test/=lib/ExcessivelySafeCall/lib/ds-test/src/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":forge-std/=lib/openzeppelin-contracts/lib/forge-std/src/\",\":halmos-cheatcodes/=lib/morpho-blue/lib/halmos-cheatcodes/src/\",\":mangrove-core/=lib/mangrove-core/\",\":morpho-blue/=lib/morpho-blue/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":preprocessed/=lib/mangrove-core/lib/preprocessed/\"]},\"sources\":{\"contracts/AmmalgamPair.sol\":{\"keccak256\":\"0xe8f98844a55a216605e6c6dd6837977fafda458a6b5d0cfa1f3a18d25e9432e4\",\"urls\":[\"bzz-raw://65dda1a1de8dd64e31c666b13de3d0583b4b0da923c67065cadcddefe47562a2\",\"dweb:/ipfs/Qmaev9WFa4yyL8fXVoWkXwNsTTY8wY7jTBGDoKJbdwSCzS\"]},\"contracts/SaturationAndGeometricTWAPState.sol\":{\"keccak256\":\"0x5e293a35668bb216a99379ea2176894314cc0f1ac68644fcf4c07017da1a4419\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://00349bb86f1b657010919b4bc3f616ad56ef4883b99ab0eead36815dae93dc76\",\"dweb:/ipfs/QmbEd9GD2JxuDntX35YcfbSCcpRstDU9GDPUkBKGzsxvqE\"]},\"contracts/factories/AmmalgamFactory.sol\":{\"keccak256\":\"0xe0d9baf63d9538a7ecb8bd24ea61a8cdf6fc9c1e9eb028f343548adeb8b93e4e\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://bfca24db47fbbaeef8bc36996cbfed78eb48771ac71d2800f081fb58a8e8c92b\",\"dweb:/ipfs/QmUfYEwfmrjSmchqXi58SnpSina4qKQvD2Jbk5RqYsaoCa\"]},\"contracts/factories/ERC20DebtLiquidityTokenFactory.sol\":{\"keccak256\":\"0x72e3ada6a2f0792a353b730c1b45ae832f9ce2f58f0bda039383f8890cb2a4f7\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://4949e7b66647313aaba2e11d7edde06eb87345b476c1a20f890659c1af827b2b\",\"dweb:/ipfs/Qmf3emVXfGp1oc8iVYxnVqpJ88vnxxdj7WqPm1vzVKb1SD\"]},\"contracts/factories/ERC20LiquidityTokenFactory.sol\":{\"keccak256\":\"0x762974ca1ed600e0930a92bd2eb3a1a5f9ef0469ab2e6e811e4674e098238762\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://5fd5f33537aeea9bac1f18c6fca2057899ec5f90cb8c756622eb436d5b13e27e\",\"dweb:/ipfs/QmfYznzzwN1AmdnuzNKe1R6t8UeztaZVGuzJ8vKfzjMXYN\"]},\"contracts/factories/ERC4626DebtTokenFactory.sol\":{\"keccak256\":\"0x7deeb7a40d26bc790112f29836da83050fa3554e471e1dce4dda6bf29ab9bf67\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://5a46a4c8270e0b8a731259328b6c35c84de270a14f2f69ba04bc58d18400efc6\",\"dweb:/ipfs/QmQ56QbX6S9GjQinsFYtTMns6HgpcTXW1wnvQT6QgiuW1Z\"]},\"contracts/factories/ERC4626DepositTokenFactory.sol\":{\"keccak256\":\"0xf84b75119f2680f8079bb9567b0c03c0ad49b71a8c00f968d03d5fca2a954035\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://c3fc7a9e300a935991746d5be835418b09e6d2b20b65e3e297d4faf28516469b\",\"dweb:/ipfs/QmQMr9MA5a3UcZCiP3e2haYqzBsbE8Pe6rDq6j6RJ3ub4Z\"]},\"contracts/factories/NewTokensFactory.sol\":{\"keccak256\":\"0x86cd420e1df8a59b11a4ab53a16971a44953f0a07741ef69d95baa4bd60126ac\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://d8cdd98060f059705b9ae2b64ab3e74395c0f3a24e12f5ac11ca7e509c6a7aa0\",\"dweb:/ipfs/QmahgKkRzuWHpQ73DHGZ4Kvd2MQG7MpfPShayJDRJQYSVr\"]},\"contracts/interfaces/IAmmalgamPair.sol\":{\"keccak256\":\"0xa17e45b2348d8920d9970c5d50b300fc0a1e8d03350cdd0d1a624494baa70337\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://8d252e89e5d49d1c15a0c0c0a495a325b9f8d608714b29279a7bacb1e4bf8795\",\"dweb:/ipfs/QmRkZ7a8JJQYEw6HQMJjjkuAK8b5Th1X1ET6BG1R8mx4qw\"]},\"contracts/interfaces/ISaturationAndGeometricTWAPState.sol\":{\"keccak256\":\"0xc9add2ad41f8edd9d360ced8d2cd7bd18dd500304794434fb2e309fa0f5af83c\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://8ecc810c544ac734ef26a2f6bebea3f3bd12d773965d297991e0e0e72892fa20\",\"dweb:/ipfs/QmarXc1Ut4FZzPRRZs2M2udbJjuZUJQHQ8fvmSr3bpHErR\"]},\"contracts/interfaces/callbacks/IAmmalgamCallee.sol\":{\"keccak256\":\"0x904b858859d460a61c9e644ca87009d8e32ba20482ef218801c89c7fb1ece339\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://1a7cedebbacc453e3e4e339fcc76fd3268247c13982de82b4930d59a44256c1d\",\"dweb:/ipfs/QmdjdvYabbwAYcV72xjiXyq278xQivFtiqD3eQ5P9Gk4f1\"]},\"contracts/interfaces/callbacks/ITransferValidator.sol\":{\"keccak256\":\"0x6d9028fc4ad1914e6b2091e6ba46a9f836f9e67ea435c4a8fef41363f2ceaf56\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://7ecaade4884d460168f6978edf35706f7b9e363de6002942b1d171a338dca6a4\",\"dweb:/ipfs/QmS5wgfDt5Pn68rpCytpzhiy57LcmivVFQ5XLGXUUP5Tt8\"]},\"contracts/interfaces/factories/IAmmalgamFactory.sol\":{\"keccak256\":\"0x1c80089901e8d7d7451775b5eaa92092eb2b65319cb92fa7884281bae49f52b8\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://bf1201399bb9d5046e0e788ff88394b2468771096a2a0d3500af542923e84628\",\"dweb:/ipfs/QmeUaPyQpbPbP5fyPUT2FfzeDgHcdyQAn1DaNg9uCuGoj9\"]},\"contracts/interfaces/factories/IFactoryCallback.sol\":{\"keccak256\":\"0x33250cf8351adb4846a3d133a9bc06568288e4c680bcf5b1085e3bca40a35e52\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://5663a39af4ed3040a58beaa5641425b9adca83c2683dd220e0c11e644fefe52b\",\"dweb:/ipfs/QmYB8Vf37WDzQfSpMDjv8hVicuaF1wMBzf7xjHRjGCy3wT\"]},\"contracts/interfaces/factories/INewTokensFactory.sol\":{\"keccak256\":\"0x3b2f1ee34106d2694a9ebbe600be692bed645f4247f4a24da3d5ec46025ab3e9\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://73143452a06db52deb593585fea6f2ef7c46e9ef6d649562dc39e79e4e5dca2b\",\"dweb:/ipfs/QmYQEy7BZWnfWKnuac8GB4QPhG5qJpaHQAfkTBoUDEuX1E\"]},\"contracts/interfaces/factories/ITokenFactory.sol\":{\"keccak256\":\"0xac23e5c0441599add526b0c308faa7787f90bf01603b6dbc231944c166ca32d6\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://ac574b98b2c1034786581137a218277ec58e06e9612f76814f34960383083626\",\"dweb:/ipfs/QmZgZqVnshjzuHBXJTR9g87S15CyLwJUSErGEDoJpBd4kg\"]},\"contracts/interfaces/tokens/IAmmalgamERC20.sol\":{\"keccak256\":\"0x44a376269170b4270ec221ce3cb31a609b394e216cc4d2e27b818361b4369829\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://c48bc7586631f27ede73d3d0b4c1d7a29b1653e6c501c8b7fc9877c125f8f57e\",\"dweb:/ipfs/QmTSLtqnsxr7h7ct524rqYssHUo4qursmCZ7g5q3J1qQPK\"]},\"contracts/interfaces/tokens/IERC20DebtToken.sol\":{\"keccak256\":\"0xc50c6be17633c8ac677b4eaac7c05a6de1f1c938237179b59ad5e65bcfbcb03a\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://6c75327e01d70a0c22662a9a8214aa64e45c517146971f8636e5aa5bf06e7696\",\"dweb:/ipfs/QmV2ydBQ5S9ZBtRuPgBMBdvd2Hcnn8quCGEMhhAAuic15b\"]},\"contracts/interfaces/tokens/IPluginRegistry.sol\":{\"keccak256\":\"0x9a677620d88ac7dc42afb21d82a7b7a89bd934c1cada5450cf2b6200bf374ccf\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://304091e5d54e9ad7db24ba1022e84e39dd9305d9cc72fd87423c71b40de4ab3d\",\"dweb:/ipfs/QmZEF5MfmUcxzLF9fcGCLvGMTTLLhcWdCMCDK2WyXj6s7X\"]},\"contracts/interfaces/tokens/ITokenController.sol\":{\"keccak256\":\"0x7778001aaf582fe10005240eb6023b2b6cee3f100b6c2222bf6b9ade93732624\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://91e5c4519207d6a450be1e0a8649157e86d20f8ef6a91ff6512a31cf5561a570\",\"dweb:/ipfs/QmUqZLW27JJZHFPf2fgLDYSWWj5gM158DdaxTTmDVukRAg\"]},\"contracts/libraries/Convert.sol\":{\"keccak256\":\"0x944776d31291de1a9cdc6a52154c23c22b43a01c3edebe7a4140e267edbba975\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://36c03749859077ba47a3acfd574f8c30f34f97def4ce81d7f4feac9a7b62794c\",\"dweb:/ipfs/QmdycZay5X2WrbS8qS7RycLpZbMQx7yKszWQzGU3rqidpH\"]},\"contracts/libraries/GeometricTWAP.sol\":{\"keccak256\":\"0x3860409daa0fdb5d96f0bfb8b49cbca058b9fe32c8e32457f85d4ee2c5cdcb1e\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://81fe70a80f4005a6529c7e93f1a92547ce0bf74c357c280a91e8778b378b18aa\",\"dweb:/ipfs/QmdRQ1DqsCu11zfbLAbrrzJ9Ups3oKgTGimYo3Zm3ALiCz\"]},\"contracts/libraries/Interest.sol\":{\"keccak256\":\"0xbc8bfa20d7295dd70e3c716fd3dbeb5b45d313e3c609d063d186042cbf000646\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://b015e8d4976d3b6d7eaca07dfcc487aeed3a7d8b4c41c8369a7476dcfb211194\",\"dweb:/ipfs/QmecH84UnZYxDZ2aL6rQtnrEExLEAfo7q4Y47yuBXdymeX\"]},\"contracts/libraries/Liquidation.sol\":{\"keccak256\":\"0x842bc44bc3cff80360ab82c5920070b12680edefe9267bdffc2d6c3c3a692d63\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://85ecd75568a0729aec06741d0575ed07dad8b7daebd7ba3114a93f6019250877\",\"dweb:/ipfs/QmQMvWdsPWsQ4t1yv6eyZy5TM7h1EZpSJdt5b8fDLcumCW\"]},\"contracts/libraries/QuadraticSwapFees.sol\":{\"keccak256\":\"0x00f6b7909be4fa1fc1ba426dd8ae659d1c5cb20c79665148898c973f55cfdccb\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://c64da0826a9b0ffc08319709f6db03339d22d24deda902a6540393251da0aecb\",\"dweb:/ipfs/QmSNwBbn2VAS8HPY4hNZusEc4DoKKZAZHtpPdjL9Gz3gs3\"]},\"contracts/libraries/Saturation.sol\":{\"keccak256\":\"0xf44bc610ece4bc7ebdb0730aa6ad69ea47647e19d4c1944c663d2d2eb4f10860\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://421fdf8d0b27132bc324a42ede9aaf23b476e5134e1073f84e824620a2a44f20\",\"dweb:/ipfs/QmbvSfMuMzDmrfPkCAEp7ydtRDWu5EUiXq4MyrGGjFErzE\"]},\"contracts/libraries/TickMath.sol\":{\"keccak256\":\"0x753813c7ed638d22edb71f48f8eb8b4283b3db2ba5b136b5c8909bd37ffa3f12\",\"license\":\"GPL-2.0-or-later\",\"urls\":[\"bzz-raw://04dd5085b72f6d73e1b17f58148e4d03639f654bdc4fdbc173b7c92ff102fc20\",\"dweb:/ipfs/QmSg4xTQPkngjNxs84428FZdSwH4AUQpwLXaASx7Qev6oG\"]},\"contracts/libraries/TokenSymbol.sol\":{\"keccak256\":\"0x628df064fdbdacfe6783964d7bf38cdf1b34e1ad07caa3cea39bf7468cc19b43\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://da6823ce0debaabe20f25281e81a4fc88de98d4df2942a5e276826ac381c227b\",\"dweb:/ipfs/QmNpEuQ25788xfcJwPk2xUB7fyP7fW5ENK2e9qgRqp1BcH\"]},\"contracts/libraries/Uint16Set.sol\":{\"keccak256\":\"0x26a714430fe1618d78386e953153b4bd2bf024baee54453ec9a7a0cc60e1534f\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://8667dd78541d656a09678e5f9cce4d49adc805955604ccaaec414e9b241f5e06\",\"dweb:/ipfs/QmZVWU2CzyDQfGit32HjJxDphBJMKG3d6JRuxbC682Z1gy\"]},\"contracts/libraries/Validation.sol\":{\"keccak256\":\"0x294848b2af973dbcd8b83732a57b67f14fd15e4af0668de05a2928b8eca5a463\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://fab25c941e87f6924b31e3f20742ca6b5ec1b7e4251543f4a61567a04ef4d778\",\"dweb:/ipfs/Qmf4ChH8afdHc3SfXkFPpNGp3e1hscyvnujPAMza3yuXeA\"]},\"contracts/libraries/constants.sol\":{\"keccak256\":\"0x0dfb294985a8f48287ff13e8476718ddb5334b1d8bf6bfa59a5db1dbcf6ca7c4\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://4bedcfdb2850cfb22b5daa768ab8125b4ccab97c90068d1d0ad4495bf942b362\",\"dweb:/ipfs/Qmf9p88yQN2JYRBR5D7q9BLmwhDJWpFk47ZuayrKqCyHat\"]},\"contracts/tokens/ERC20Base.sol\":{\"keccak256\":\"0xdd3db9eaa855b6ff747ffaa0e74dd2a64dd5b0d704356acb75c2690c3fc6bc2b\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://8f9e05ae3078bc3a29ef43861b006fb290606c1c9b2676baaed3ab98ecdb2d59\",\"dweb:/ipfs/QmZrvgxYtUD6jQVBvM9rT7jj5Vzb5csiThGj3ZwHSPryAL\"]},\"contracts/tokens/ERC20DebtBase.sol\":{\"keccak256\":\"0xc0a59cd54fcd847b160d662aa45a5fe7d24ed90c8030fe17fd5f9def427ed19a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://365c7f18505af36b2806404b1b3f2d897de6ac18e255ecfbb4ccc491cac7e444\",\"dweb:/ipfs/QmUqx8EBwRb6W1YQPb9MjwAhEEHNpZTCopbGWb1vbyuUpp\"]},\"contracts/tokens/ERC20DebtLiquidityToken.sol\":{\"keccak256\":\"0xf222ad5562ed41d74b0cfb5b4aad84ec9f4cb91b6d71928b30b018bab494efe8\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://a8e8f3e7ded2eae04c63ce3ae7a86c051d48d6db697cb6929d7064a4ec9d7371\",\"dweb:/ipfs/QmU3EuwHU3xB1e6MxaRjSRJcDMK73wfZig9uGWqZPaHnTn\"]},\"contracts/tokens/ERC20LiquidityToken.sol\":{\"keccak256\":\"0x2bb2429e551c031034c747749373d2e4c451580e9b203b689d6eaf03ad896358\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://9ad5902756073578beee9068b74bd921e59a36b803cf34ef01570c670363689e\",\"dweb:/ipfs/QmTkT5K2XcB3ZbPDqd4ZAfnZMp2reCzu3Pv7JpRqhAtZHP\"]},\"contracts/tokens/ERC4626DebtToken.sol\":{\"keccak256\":\"0xe69b1ed2fb7b2d7c24c6838462001988b8e51795d215cfa74b9874d17257509e\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://c4f201e5f5621689046c58863ab9270cf770c68810d52269d1fc2ac93a7ccf96\",\"dweb:/ipfs/QmdtALf6LQdHhce3HsNdVtomQu8e5F5QcYU6S7H1PeBThZ\"]},\"contracts/tokens/ERC4626DepositToken.sol\":{\"keccak256\":\"0xd914aa43dc5e9f2f02f98b05561faf6f00853b701f51dfcd7a08a31feaf220be\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://8c2282d40855587b2ac70e89d4e0e147b9afe32a41245fffc96b923a9e5ce7ac\",\"dweb:/ipfs/QmVn3tBkZcXKnqjfnLTXFkKtu1EetDL1UF7rRjNrHdRCSM\"]},\"contracts/tokens/PluginRegistry.sol\":{\"keccak256\":\"0x9263d71fc32da7d0ca4f8d272f8d75d565c1f06281952481322983bae9d7b488\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://c9dcbc64172f4339547865b4f041826f0de5d464900f316edbe72e7d6bfb396d\",\"dweb:/ipfs/QmQykSWuY8xLJotWUPgG5JQDS5DmA2E5Hjb4c6Bz4YnbBQ\"]},\"contracts/tokens/TokenController.sol\":{\"keccak256\":\"0x8b76b9ebb9385f0c4b7c0b8210fb96b11a49a8c9a3a6e855752c32a5c12d54e6\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://de87bdae81940f397136f665d68907d4e4c32f35bf2cd0c9e9305a9fe190d159\",\"dweb:/ipfs/Qmce4hM6xofBYxzAXesHX4hkiHBexoGeQpCzpeCARctnCn\"]},\"contracts/utils/deployHelper.sol\":{\"keccak256\":\"0x9b9dd84e234bb2ffbf51da7e9ab42fe7b6329acf38de7f042d4f8abd146182f0\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://d07ded7de8e48a25ac7b0442c6e455338bd16ee483a89ad7f37585ab91865a3b\",\"dweb:/ipfs/QmeBAuZgRJEXeuX6qsGt46sTLovKNC5Pue8xFxbHMPtiBR\"]},\"lib/1inch/solidity-utils/contracts/libraries/AddressArray.sol\":{\"keccak256\":\"0x7895eaf7b55d9612b22ec586970488de51436c021b9f9414b3c27c3583c8856e\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://e43897dfeff84e2983b017ab100d74061a6b9bed4618ec37a7cbb68bf064ac22\",\"dweb:/ipfs/Qmejoj4CuNr1giwGBDG7SgRtfYmi64fgy2DsGP2AAW9gH9\"]},\"lib/1inch/solidity-utils/contracts/libraries/AddressSet.sol\":{\"keccak256\":\"0xbb8e2a541ac268f00f382c9fba9403b3ec5b58a48dc7236920d7c87817f93318\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://614b60ff60f75c46aa41d00ec5dd78136d42e0d6035aa2331d16f26d2f5f5368\",\"dweb:/ipfs/QmUJWqcx2heKcmBhxpHfAnpKrwnevqtAwaQKT7Bmpke5NB\"]},\"lib/1inch/token-plugins/contracts/ERC20Hooks.sol\":{\"keccak256\":\"0xd2657f278b2ed4667663344aa06c02b52b15862c69c215570de329aa1a4683a2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://4ffb90146369b7fa890489a0d3ecab5aa81c9f47b3ec7a1776fd8bc14eaa33d5\",\"dweb:/ipfs/QmWo24VHySo9jQBeXfE87Z3Hh576cNKcwccLrBAMsfax1c\"]},\"lib/1inch/token-plugins/contracts/interfaces/IERC20Hooks.sol\":{\"keccak256\":\"0x2fb4fcbf91a7edf36e7ada3f578a8de1aee7ebdd12460648d3e09d4448351875\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://d09a05543afcad3c0c2d2b9013647408cc44e1b17f362665ee20f9abed5964e8\",\"dweb:/ipfs/QmX3MMGsVJSfsrVoYY2jWK2BXd3i6scBhQQxytk461mizh\"]},\"lib/1inch/token-plugins/contracts/interfaces/IHook.sol\":{\"keccak256\":\"0x474893cc48ee17530ad0e978ecbccaa726943615a5736260187a37b8e133ee80\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://511ed52ec427cced459878f28453790817d4cbacc8601d77eb8dfa28e2e0b30d\",\"dweb:/ipfs/QmXvbB1bAZZanGepiUxWh3zZQUaHQZeYpR1UdaW4w5yKVS\"]},\"lib/1inch/token-plugins/contracts/libs/ReentrancyGuard.sol\":{\"keccak256\":\"0xa88ccab1ee6b34a9007986ca15ea5fd5580864029130eb38333183af1bc4f66c\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b5ad18c862e59b0f24edc900519a55c7aee5537a42d039d6ea5e34df40267bb0\",\"dweb:/ipfs/QmVMRSjUEPxCno1sFdhWvpRqAqoqns8zwVyd7yCHYC6P8Z\"]},\"lib/ExcessivelySafeCall/src/ExcessivelySafeCall.sol\":{\"keccak256\":\"0x7d9d432e8f02168bf3f790e3dabcf36402782acf7ffa476cabe86fc4d8962eb2\",\"license\":\"MIT OR Apache-2.0\",\"urls\":[\"bzz-raw://1adc13e7f399f500ea5f81480ad149a50408fde7990a2c6347e6377486f389dc\",\"dweb:/ipfs/QmSvm5TUBJqknsqNJLLHqNS4MLSH5k3vNrbquVg6ZKSfx9\"]},\"lib/mangrove-core/lib/core/BitLib.sol\":{\"keccak256\":\"0x80f6885268986b9e976b424993aa875cf7aab8464403ed675a86ade9e9be5ee3\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5a31b4e1e0dc95de9a1dbb114c40c44814de5db3e2d857c93c2524a61454f6c8\",\"dweb:/ipfs/QmRkgE9ue5rGwE6XDnszF2e2meWqAC9nnKM97xKHjHphQr\"]},\"lib/morpho-blue/src/libraries/MathLib.sol\":{\"keccak256\":\"0xa7354cbbcecef7bc0c94b61061c4e5da75515056b8e2db65e826b00d7369744a\",\"license\":\"GPL-2.0-or-later\",\"urls\":[\"bzz-raw://d7419c59bb906fcfa49320b68f265c3200090e5c30b194766256aee70b012e08\",\"dweb:/ipfs/Qmbo4uaW6XYnudya4bb6RU6riWXFk5M3CWJge5XzTTaEfd\"]},\"lib/openzeppelin-contracts/contracts/access/AccessControl.sol\":{\"keccak256\":\"0xb64ecf1154f183412bcde47168f3af245e4120846346a0b3872c631e361156d2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://85331049a60659bc4489733ccd3cbeb177b65691122a8cb637bf9267ab25e23d\",\"dweb:/ipfs/QmbGpDcdwKTirzSCoZfE4rHG7jBSWsE4K2iSb6UCYXtLJv\"]},\"lib/openzeppelin-contracts/contracts/access/IAccessControl.sol\":{\"keccak256\":\"0x5643a5cadd1278581308b20becb48a50946c159fc31c29fc407ea9a61fc865d1\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://c7d79f305a239207a24fa2174897c8ea8ff1e81cb790d440fd54c89a0e85f63e\",\"dweb:/ipfs/QmT847eeAMnRN3DaG1zsKNMn7qipNAidqv1REnKexPkrfA\"]},\"lib/openzeppelin-contracts/contracts/access/Ownable.sol\":{\"keccak256\":\"0xff6d0bb2e285473e5311d9d3caacb525ae3538a80758c10649a4d61029b017bb\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://8ed324d3920bb545059d66ab97d43e43ee85fd3bd52e03e401f020afb0b120f6\",\"dweb:/ipfs/QmfEckWLmZkDDcoWrkEvMWhms66xwTLff9DDhegYpvHo1a\"]},\"lib/openzeppelin-contracts/contracts/governance/utils/IVotes.sol\":{\"keccak256\":\"0xb541b133f2d85eb37ae866cb21123be93bd3671b6840c47f951da52d3a704548\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://22443f43ece107eaf453aa9a41a59410cece5429c2779c794fbd2c8b5aa29d38\",\"dweb:/ipfs/Qmbum2jwLYuT9aZ2fr9NMLwWFsVavonrGm2VnbAL9hP2jn\"]},\"lib/openzeppelin-contracts/contracts/governance/utils/Votes.sol\":{\"keccak256\":\"0x3f91c79d6f55db9e4fc36e1cfe6a483a7b0f5be60fecbd979555071673746d47\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://9b1e3c64cbeb2757a2a1a45c69f7f3984a93b0eadd1016341b64f9d94f89d7c4\",\"dweb:/ipfs/QmP1Mj14U4vMTFa2rv2nodMbWSCov2ac9Md8W2aUcgYdKX\"]},\"lib/openzeppelin-contracts/contracts/interfaces/IERC1363.sol\":{\"keccak256\":\"0xc15298eb2b9ba5e18a8c9d12f93ad17a3e162a5c1d9b85f54c8adb5827b0d4da\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://1f3c3d8f81d2daf1231890a6a2f897be365d6a479b53dcd52ec2527b5d3faf41\",\"dweb:/ipfs/QmeNdkd6u4at9pd2GAyyqxzrVGGvxfLpGmAKnFoYM5ya2e\"]},\"lib/openzeppelin-contracts/contracts/interfaces/IERC165.sol\":{\"keccak256\":\"0x81b022028c39007cce9920c394b9cddd1cb9f3a1c0398f254b4a6492df92ad2b\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://e0b61b8a5c69b4df993c3d6f94c174ab293aa8698d149bce7be2d88f82929beb\",\"dweb:/ipfs/QmbtacmB1k8ginfrHvAJpjVeqnjYGfXYrkXmMPYEb83z4t\"]},\"lib/openzeppelin-contracts/contracts/interfaces/IERC20.sol\":{\"keccak256\":\"0xb5d81383d40f4006d1ce4bbad0064e7a930e17302cbe2a745e09cb403f042733\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3fc4a5681c2f00f41f49260a36ae6bbe1121dd93d470ea24d51d556eff2980be\",\"dweb:/ipfs/QmUBW6TwVWtGP96ka9TfuGivd27kH8CtkXD8RQAAecSFiR\"]},\"lib/openzeppelin-contracts/contracts/interfaces/IERC3156FlashBorrower.sol\":{\"keccak256\":\"0xad94c8d7246a50210f7bcb54e5b91fc9f1c6e137263ac972ca5dd0f7f6d4d49d\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6938e96fbb0cf3d961788b6c3522400e255d8057d1b9f7e08a50e0b48486b007\",\"dweb:/ipfs/QmNXG3MPzDXjHJ9iWDYCz4vi9RBTgVBnZjndnfBwMfhkyD\"]},\"lib/openzeppelin-contracts/contracts/interfaces/IERC3156FlashLender.sol\":{\"keccak256\":\"0xd92910b862581523ad4e9b99f0bf738f4e62700a5e305953c7fda7db2cfd0f73\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://799f3f0f30822ac806800bbe7fe63b9991490c4e1d9edb75f5993d9350320819\",\"dweb:/ipfs/QmT8T4SokW6YxpDJQiafpeYNRGtjC5gFHxRqKTRXRyP6zB\"]},\"lib/openzeppelin-contracts/contracts/interfaces/IERC4626.sol\":{\"keccak256\":\"0x932fe72aeda629f70ee2ca902cfd8cce9faa0a13b39222c240979f42984c4541\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://4e9e0a320185800eb9a6d57ef0e4ccf3bb32a6b5dd44882f7552617c30d4a05e\",\"dweb:/ipfs/QmYJVpT7DDPWx3DWro8vtM6Gqre2AyufsyCYoHm9cfQ1vr\"]},\"lib/openzeppelin-contracts/contracts/interfaces/IERC5267.sol\":{\"keccak256\":\"0xf29e9088951d8a2074d872a733674618fe5c164df21b8b5cf4a6295f523ba7ad\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://562a1abc7ea505582827ce0c9a2f778360a1a8242742683af179930640020215\",\"dweb:/ipfs/QmPjx5f6KKaPfsDi1uV3ovQN9gHTAcNkMAFJZxE1Adw6VT\"]},\"lib/openzeppelin-contracts/contracts/interfaces/IERC5805.sol\":{\"keccak256\":\"0xc8960b7d3e504e98883de33856a917a473c05034cd61880df2a60b5c47c214fe\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://80542373fa695b68d65b4d7222e852df5bda035a82e86ee559336c93b2bf7051\",\"dweb:/ipfs/QmZgH14DPTnKfA5gMSTMiUa6ExuqFfAozmEtLXiWc1iDiw\"]},\"lib/openzeppelin-contracts/contracts/interfaces/IERC6372.sol\":{\"keccak256\":\"0xa602c8beaae2d9e2ab1ce585a54547a6d4da32d32e4d002d20ccba55b19258d8\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://ac6553b5b07788a0bb67cc53596837d795280233a9a5cb3a9b3e1fde56822f78\",\"dweb:/ipfs/QmVoHXoma4ZbPKVRJJRosvhipa4rtCMU9QQvWHWKiRUxvi\"]},\"lib/openzeppelin-contracts/contracts/interfaces/draft-IERC6093.sol\":{\"keccak256\":\"0x85cf779582f7860b00bba1d259be00e754bfbac3c2339e324d0113d682d9e9f9\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://2ddf369affd106e2a9e2b8a22a6ce3da8a6ccda14c6ba5b8c87f6b08169e6318\",\"dweb:/ipfs/QmNadAttd47ycHShxhk33JUJhrbzmyZQ7mHs7WEyG4Qkmp\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/ERC20.sol\":{\"keccak256\":\"0x5aad1745bddba11752c3962464a3b12e0af079310cc22d1f43f0388ae1aaf8db\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://577fad916bfdfe89aadf2685322fec7562cb0ed87722923085213cd9f85d7b79\",\"dweb:/ipfs/QmSM3J6PjrAUyEoNbdhq1ECZLXczKdCTzZTBUieKHsBYEL\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol\":{\"keccak256\":\"0xfe37358f223eddd65d61bb62b0b7bdb69d7101b5ec8d484292b8c1583a153b8a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://28dd43f30af3c12ae0fc08dd031b1250e906ef3c95f63f30fac6fd15aee2a662\",\"dweb:/ipfs/QmUkSyWsSRx36w1ti7U6qnGnQgJq16wpMhjeJrnyn9AXwG\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20Burnable.sol\":{\"keccak256\":\"0x2659248df25e34000ed214b3dc8da2160bc39874c992b477d9e2b1b3283dc073\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://c345af1b0e7ea28d1216d6a04ab28f5534a5229b9edf9ca3cd0e84950ae58d26\",\"dweb:/ipfs/QmY63jtSrYpLRe8Gj1ep2vMDCKxGNNG3hnNVKBVnrs2nmA\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20FlashMint.sol\":{\"keccak256\":\"0x4d43ed4b9ff9e4c671274976d59a58dbcc7b69bd7ac11b1710f5b7607cf15b74\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://0b47b42998f675cb6a51f2e74ef5906a6fa63ec6718f3fd56ee035d6f77143f9\",\"dweb:/ipfs/QmREnAXqPJBvAwfWfDzaFhNfSRWF4Jdy9ZrpHLw1KdQweY\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20Permit.sol\":{\"keccak256\":\"0x6485b101d7335f0fd25abc996c5e2fc965e72e5fbd0a7ad1a465bd3f012b5fd8\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://1f8d296121044e697bcd34c4cd394cebd46fc30b04ce94fccff37200872e6834\",\"dweb:/ipfs/QmTNdmLdoHgMzoCDZ8Txk9aYvwtuyeJYHf5mjLWgzGTZAu\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20Votes.sol\":{\"keccak256\":\"0x62dc9346044aabf22d78541bd495aa6ca05a7f5100aed26196ba35d40b59fcb5\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5221df4501c74cd4493fee1a0f0788e02c4dc78c3c601e9f557f557c5a53ea92\",\"dweb:/ipfs/QmZpzyYY9dKLrgvYhXSHT93jwqb1UGvtGNMQk5dpECY5pa\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC4626.sol\":{\"keccak256\":\"0x0d99c706d010fa15de36e7e7b7a03dd0fdc9bcec52f9f812ef80ec7f3fc6fa63\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://bee73c81a2964e8da0537de14082e60d64cd7b1cd9162adc04b58317e334c896\",\"dweb:/ipfs/QmbQ75T9PEJuiLk1kypX68rEBFtTaEzPWsy8Dv99buqVPH\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol\":{\"keccak256\":\"0xbaffa0bcc92adf28a53cc3b68551fc3632cb8f849a0028cb8d5c06e4677715e9\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://32e6f8f6b2e883c85e6a602c0882d9962ce2f92406961244e86cd974df815912\",\"dweb:/ipfs/Qmahvx6fPpecicq1aUE1JihCxV5ep1bfuPukzrxa8Ub5PS\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Permit.sol\":{\"keccak256\":\"0x093f32ab700c2b05373387263915a75f5455cdb0f09a7630cc621e27b7b50d04\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://d163e6ef21df143969df5557305e8c643a135c7660a678d0c65dca91772114a0\",\"dweb:/ipfs/QmTZUgiwEro5oLRhbJ2iSWyCqu1JTDekoFHALVUn4eHqYK\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol\":{\"keccak256\":\"0x1183b415155c1a7bf56d45edad5b17caf0da70935ac420698cbe8afb6750cbb2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://21d9edaeb3e5e8f93eb0fdab41530654e8169b1990b3bbfcf5e4527c52aa03f5\",\"dweb:/ipfs/QmWrqpNW3x5k3pTjvrT8XU1hauHnXTjqaPL2tfzMuWYosj\"]},\"lib/openzeppelin-contracts/contracts/utils/Context.sol\":{\"keccak256\":\"0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12\",\"dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF\"]},\"lib/openzeppelin-contracts/contracts/utils/Nonces.sol\":{\"keccak256\":\"0x0082767004fca261c332e9ad100868327a863a88ef724e844857128845ab350f\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://132dce9686a54e025eb5ba5d2e48208f847a1ec3e60a3e527766d7bf53fb7f9e\",\"dweb:/ipfs/QmXn1a2nUZMpu2z6S88UoTfMVtY2YNh86iGrzJDYmMkKeZ\"]},\"lib/openzeppelin-contracts/contracts/utils/Panic.sol\":{\"keccak256\":\"0xf7fe324703a64fc51702311dc51562d5cb1497734f074e4f483bfb6717572d7a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://c6a5ff4f9fd8649b7ee20800b7fa387d3465bd77cf20c2d1068cd5c98e1ed57a\",\"dweb:/ipfs/QmVSaVJf9FXFhdYEYeCEfjMVHrxDh5qL4CGkxdMWpQCrqG\"]},\"lib/openzeppelin-contracts/contracts/utils/Pausable.sol\":{\"keccak256\":\"0xdb484371dfbb848cb6f5d70464e9ac9b2900e4164ead76bbce4fef0b44bcc68f\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f9d6f6f6600a2bec622f699081b58350873b5e63ce05464d17d674a290bb8a7c\",\"dweb:/ipfs/QmQKVzSQY1PM3Bid4QhgVVZyx6B4Jx7XgaQzLKHj38vJz8\"]},\"lib/openzeppelin-contracts/contracts/utils/ShortStrings.sol\":{\"keccak256\":\"0x1fcf8cceb1a67e6c8512267e780933c4a3f63ef44756e6c818fda79be51c8402\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://617d7d57f6f9cd449068b4d23daf485676d083aae648e038d05eb3a13291de35\",\"dweb:/ipfs/QmPADWPiGaSzZDFNpFEUx4ZPqhzPkYncBpHyTfAGcfsqzy\"]},\"lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol\":{\"keccak256\":\"0xcf74f855663ce2ae00ed8352666b7935f6cddea2932fdf2c3ecd30a9b1cd0e97\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://9f660b1f351b757dfe01438e59888f31f33ded3afcf5cb5b0d9bf9aa6f320a8b\",\"dweb:/ipfs/QmarDJ5hZEgBtCmmrVzEZWjub9769eD686jmzb2XpSU1cM\"]},\"lib/openzeppelin-contracts/contracts/utils/Strings.sol\":{\"keccak256\":\"0x1402d9ac66fbca0a2b282cd938f01f3cd5fb1e4c696ed28b37839401674aef52\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://d3e6c46b6d1ea36bd73e0ac443a53504089167b98baa24923d702a865a38d211\",\"dweb:/ipfs/QmdutUpr5KktmvgtqG2v96Bo8nVKLJ3PgPedxbsRD42CuQ\"]},\"lib/openzeppelin-contracts/contracts/utils/cryptography/ECDSA.sol\":{\"keccak256\":\"0x6c29257484c0595ca5af8844fafe99cc5eace7447c9f5bced71d6b3a19a6a2a5\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://cce7ac0bdb05f73c0918e362dea2e52426e00ddf0a1018f14accdcf78c6eb6e4\",\"dweb:/ipfs/QmbkNq5dDxww27FzFFiKgW3S7C5VoZpjdZGpSCtsb9hP32\"]},\"lib/openzeppelin-contracts/contracts/utils/cryptography/EIP712.sol\":{\"keccak256\":\"0xda8013da608bda3c9eaa9e59053d38d7888e64bb40aa557e5929cd702f8de87e\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3ea13234c6b00ae79dc1a98e7e7f2faf38d37e76a687ccd0c95ad84b03ea570f\",\"dweb:/ipfs/QmWtdefDm5jiEzAjmfPMZ5B1NKVxFoMiD5ZoD68hcNTHun\"]},\"lib/openzeppelin-contracts/contracts/utils/cryptography/MessageHashUtils.sol\":{\"keccak256\":\"0x26670fef37d4adf55570ba78815eec5f31cb017e708f61886add4fc4da665631\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b16d45febff462bafd8a5669f904796a835baf607df58a8461916d3bf4f08c59\",\"dweb:/ipfs/QmU2eJFpjmT4vxeJWJyLeQb8Xht1kdB8Y6MKLDPFA9WPux\"]},\"lib/openzeppelin-contracts/contracts/utils/introspection/ERC165.sol\":{\"keccak256\":\"0x41ddfafe0d00dc22e35119d41cb0ca93673960689d35710fd12875139e64bd9f\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://49d90142e15cdc4ca00de16e1882fa0a0daad8b46403628beb90c67a3efe4fc4\",\"dweb:/ipfs/QmNizYnFNcGixHxsknEccr2cQWyyQBqFF7h2bXLmefQz6M\"]},\"lib/openzeppelin-contracts/contracts/utils/introspection/IERC165.sol\":{\"keccak256\":\"0x4ee0e04cc52827588793a141d5efb9830f179a17e80867cc332b3a30ceb30fd9\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://17d8f47fce493b34099ed9005c5aee3012488f063cfe1c34ed8f9e6fc3d576e5\",\"dweb:/ipfs/QmZco2GbZZhEMvG3BovyoGMAFKvfi2LhfNGQLn283LPrXf\"]},\"lib/openzeppelin-contracts/contracts/utils/math/Math.sol\":{\"keccak256\":\"0x1225214420c83ebcca88f2ae2b50f053aaa7df7bd684c3e878d334627f2edfc6\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6c5fab4970634f9ab9a620983dc1c8a30153981a0b1a521666e269d0a11399d3\",\"dweb:/ipfs/QmVRnBC575MESGkEHndjujtR7qub2FzU9RWy9eKLp4hPZB\"]},\"lib/openzeppelin-contracts/contracts/utils/math/SafeCast.sol\":{\"keccak256\":\"0x195533c86d0ef72bcc06456a4f66a9b941f38eb403739b00f21fd7c1abd1ae54\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b1d578337048cad08c1c03041cca5978eff5428aa130c781b271ad9e5566e1f8\",\"dweb:/ipfs/QmPFKL2r9CBsMwmUqqdcFPfHZB2qcs9g1HDrPxzWSxomvy\"]},\"lib/openzeppelin-contracts/contracts/utils/math/SignedMath.sol\":{\"keccak256\":\"0xb1970fac7b64e6c09611e6691791e848d5e3fe410fa5899e7df2e0afd77a99e3\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://db5fbb3dddd8b7047465b62575d96231ba8a2774d37fb4737fbf23340fabbb03\",\"dweb:/ipfs/QmVUSvooZKEdEdap619tcJjTLcAuH6QBdZqAzWwnAXZAWJ\"]},\"lib/openzeppelin-contracts/contracts/utils/structs/Checkpoints.sol\":{\"keccak256\":\"0x743aa2d21f6c26885e0aa6a1c84f7f7bc58fbd6df6bab32bed23f1a41f50454a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://a651d38b4261840d3744e571edf2b59455352a8c7dac5d35b019afefa343ea3b\",\"dweb:/ipfs/QmSy3UkTCQDYTjKtGwtqPRrXaofcqtVZxaF6j1dV44wqvr\"]},\"lib/openzeppelin-contracts/contracts/utils/types/Time.sol\":{\"keccak256\":\"0x36776530f012618bc7526ceb28e77b85e582cb12d9b9466a71d4bd6bf952e4cc\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://9f867d046908497287d8a67643dd5d7e38c4027af4ab0a74ffbe1d6790c383c6\",\"dweb:/ipfs/QmQ7s9gMP1nkwThFmoDifnGgpUMsMe5q5ZrAxGDsNnRGza\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/Base.sol\":{\"keccak256\":\"0x4ff1a785311017d1eedb1b4737956fa383067ad34eb439abfec1d989754dde1c\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f553622969b9fdb930246704a4c10dfaee6b1a4468c142fa7eb9dc292a438224\",\"dweb:/ipfs/QmcxqHnqdQsMVtgsfH9VNLmZ3g7GhgNagfq7yvNCDcCHFK\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/StdAssertions.sol\":{\"keccak256\":\"0x4584f551c3a875c26423c1e50a77179bc2f9c9c1ee6d0f0c6be0f89ca5ee8270\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://ef118876a862b60ba8a6612b8b3f3a6c226e11a0071a2e9695f021586d1bfdbe\",\"dweb:/ipfs/QmXd2vD91PkvoFWasFkgbDi89PctmgsHVxGvGNyhjmRK2b\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/StdChains.sol\":{\"keccak256\":\"0xcd3e64ec9ffa19a2c0715bbdaf7ddf28887cc418e079bec4373fd6a3f9961a7b\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://e981a2ab738590928e9efa5f3d95a408c718eb12d73a113d7675f3ed55a026a1\",\"dweb:/ipfs/QmTgSEkWWsBRy32goRCaUkraSgpZHtgbZoKC3iEFNz5RDc\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/StdCheats.sol\":{\"keccak256\":\"0x0fa6ec03602648b62cce41aab2096e6b7e052f2846075d967b6958dd586db746\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://cd84e2ca9c1eaed6b76768cc12bb8c1af8289170ea8b7706f58d516460d79c41\",\"dweb:/ipfs/QmQ7BK7co6DE4eWUqMyv11s5eHYkS1tyx8tDSZGZVtf2aK\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/StdError.sol\":{\"keccak256\":\"0xbf477b11a42d7611696956546bcfaa29317d1166bf65e402344599c05943fc77\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://bc2e117d1135e030862b96a6526a43feb38d396cc79857f1fb696d4eff0e5fd6\",\"dweb:/ipfs/QmdSuQ5RrQudTLsNmWXGEeVJX8gR5U9XPm6m4dwwuQnJrj\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/StdInvariant.sol\":{\"keccak256\":\"0x4dab3021edfa9511bbdd80c48f060ef62eaf457f99eaf841f561fc2557d9a08d\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://07668628673174cf8f27f8f4e1f862bab775013ec247eb34f698c5005f229391\",\"dweb:/ipfs/QmPJsiiYwmCZXMsHmQv5tg8VF3CAhNdat7WnKLTWZZH2v5\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/StdJson.sol\":{\"keccak256\":\"0xbc0132abe1c2accc2867c0f03667afffdf92f3e95a581bb03c9557eaa38ea500\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://eb6fab37dc73c219cfbb7b4f4998bcf7677ca5397a867e850f40232192073974\",\"dweb:/ipfs/QmUHsbVdp9SKmgek7ZfPcLTKrpZFXpqaqt4sVejzxGEQL3\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/StdMath.sol\":{\"keccak256\":\"0xd90ad4fd8aeaeb8929964e686e769fdedd5eded3fc3815df194a0ab9f91a3fb2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://7919b70f636c7b805223992f28ad1ad0145d6c1385b5931a3589aface5fe6c92\",\"dweb:/ipfs/QmY7FRaULwoGgFteF8GawjQJRfasNgpWnU2aiMsFrYpuTC\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/StdStorage.sol\":{\"keccak256\":\"0xb91ab24383a5872b894fc93325eef1add6cbbf981628f18e860068bf88bb7dcc\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://2651e33b9ac8fc5803ed0a43078c1bf4fa90a0b0347aafd95776b02daccdcc57\",\"dweb:/ipfs/QmbdRyPuGJdZgnCuMnp7c1WsBo1Spf7j1KMmws1Z5rd4BQ\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/StdStyle.sol\":{\"keccak256\":\"0x43e2a8a9b9c2574dabe74f11adf6f782df218f463540e3b5b563609fe108597d\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://51363ca97404cf4128e1141428949768c31929e75e014b02c85e887fbbb4f1b8\",\"dweb:/ipfs/QmVhtbQc2fU4rRmbcfBtz34mAgG4BAZBsbna1Ca4SkoPsK\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/StdToml.sol\":{\"keccak256\":\"0x58a72c765ed3f7ff6b105509689658795b8a3739b8931772a497155878381861\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b4a3746f4fabaeb980bd77d9e091d3904ee38a6c0e191bfa8ba6874c6f8558a3\",\"dweb:/ipfs/QmUfFDMEn461FgGEXt5HicyGD54sc28sLaQ9JRWDMBKed8\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/StdUtils.sol\":{\"keccak256\":\"0x7274081e11c05164fd8eadde4de8305c033e58a43008dea58065f3170ccf0737\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://e9801614b6c9d3e472982e6cc68f5f1ad03682f84eafb686be65633c7132d138\",\"dweb:/ipfs/QmcQSUcrm2A7XuektnxJjvYGmZtBeA6LKPxCXRqdXBX776\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/Test.sol\":{\"keccak256\":\"0x3b4bb409a156dee9ce261458117fe9f81080ca844a8a26c07c857c46d155effe\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5792c69fe24bdc063a14e08fe68275007fdb1e5e7e343840a77938cb7e95a64e\",\"dweb:/ipfs/QmcAMhaurUwzhytJFYix4vRNeZeV8g27b8LnV3t7dvYtiK\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/Vm.sol\":{\"keccak256\":\"0x44bfadcf5a89b8058f80258f2259585c740f9cc45669a0579f4f2753ff2c6354\",\"license\":\"MIT OR Apache-2.0\",\"urls\":[\"bzz-raw://bbc366c8b3499d5030e3b2e45bac23770531f2f5243a0e80e3d5a66b6f9a312c\",\"dweb:/ipfs/QmNxDEB3BaVnKzNaWedtdMshhvCEddB1AsdJZcsQx6jdtC\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/console.sol\":{\"keccak256\":\"0x4bbf47eb762cef93729d6ef15e78789957147039b113e5d4df48e3d3fd16d0f5\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://af9e3a7c3d82fb5b10b57ca4d1a82f2acbef80c077f6f6ef0cc0187c7bfd9f57\",\"dweb:/ipfs/QmR9VzmnBDJpgiDP6CHT6truehukF9HpYvuP6kRiJbDwPP\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/console2.sol\":{\"keccak256\":\"0x3b8fe79f48f065a4e4d35362171304a33784c3a90febae5f2787805a438de12f\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://61de63af08803549299e68b6e6e88d40f3c5afac450e4ee0a228c66a61ba003d\",\"dweb:/ipfs/QmWVoQ5rrVxnczD4ZZoPbD4PC9Z3uExJtzjD4awTqd14MZ\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/interfaces/IMulticall3.sol\":{\"keccak256\":\"0x7aac1389150499a922d1f9ef5749c908cef127cb2075b92fa17e9cb611263d0a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://d95ebb7c7c463e08ebc12dab639945752fb2480acfc6e86da32f72732a7fd0c0\",\"dweb:/ipfs/QmNXK8P8oPWwajsQHvAHw3JPyQidPLCGQN3hWu1Lk6PBL2\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/safeconsole.sol\":{\"keccak256\":\"0xbef9786cb49d3eade757bad87568c49c8c8f35721f0193c95ffb055d9e466e11\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3bafd2b0b2d28068d329f95ea8a1fbce3719c257fcb863fc01abcbafd8d531ab\",\"dweb:/ipfs/QmUeaFjKWTVDBsHVfSob4mwt6A5hTnKDz22HaUXeZhypa3\"]},\"test/InterestTests/InterestFixture.sol\":{\"keccak256\":\"0x458f1f72b1417a73ecdea81c25b269592e95c1808ca6aaa6b60a25243e143ed3\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://abeb9b791b75f44d48f898182c673d80ea1c0f513fffe48a6834fdebebc6fdbe\",\"dweb:/ipfs/QmU92joERfyZJaTonAknmtRBkTjs5Jb7S2zM8Zk1XAnhwj\"]},\"test/example/PeripheralDelegationContractExample.sol\":{\"keccak256\":\"0xf212fd0b2dd3a358c826623bd320e3aa0630892b9f6ba777b8126d3e2cfcfb14\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://2ad79b2d7eb1b46c69383b9e9090bf2031ff779e46637d850b881db3dc84d797\",\"dweb:/ipfs/QmTPx8qw1zdYXRzjBnmuzMCt8yiErwFiLBk47xnbTm1erP\"]},\"test/shared/FactoryPairTestFixture.sol\":{\"keccak256\":\"0x62487d7b3402461a61bd0c99be82302c8c1a94533c525a0ff6bcfe888af730a5\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://dfd509aaec3469ed23d1cda8c6f603b7f0163fc29ec4ba6e4b06b60ca8fdc042\",\"dweb:/ipfs/QmTPDPM7kt77VNWr61MVZGmNZp67RG8jKzdmz7zwWep4GE\"]},\"test/shared/StubErc20.sol\":{\"keccak256\":\"0xf3508dc98ae444d142d9993c52cebd856aba40c3e53d64bfeb63e71d190b12ee\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://0cc01d254b6d5569d1cb426250db9df1b01afde9dd7b52e1efa0691112fcd918\",\"dweb:/ipfs/QmPnL9wFpSKXprrEFS9kkC2WzK2kAgWSH1snom1wiorCxn\"]},\"test/shared/utilities.sol\":{\"keccak256\":\"0xc64b147bbe73bf59fdec4202c5b7c5dbcadd7550f4b2ea2390ea689e194d7cb8\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://ab03a14b75d4b9df7795eeefd7e6d4a1d7af7b58ce948741cdd5d056a2c30416\",\"dweb:/ipfs/QmShemddxGaLyTGtC3yLdMtdHf9Gj3H8rjf2umzbFmP6aG\"]},\"test/utils/DepletedAssetUtils.sol\":{\"keccak256\":\"0x2273187d5eb782fb341d44265bd6e8afcef18ab3cfabcb4a0b77a75f15298c42\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://04d0a060b217603f6c7e38efc49be20932f08c56e46b1f9cea54eb722595458e\",\"dweb:/ipfs/QmdJHgaFbbtGDHPpHEFucTvrj4p4LT1piMPjtbrWBMXzAR\"]},\"test/utils/constants.sol\":{\"keccak256\":\"0xe7d13ea4f26a2c43b7beed68c83a0e36555ead8f6bfd181430c74f853546fc34\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://5098f47b615afa3d6489c2c8c2576f6202601fb15b1f32e6900639986e44f1fd\",\"dweb:/ipfs/QmPU1Ejtv4yY7eqjW1SpVgvS8vMqwyEjMeNGCLax3Mwk9d\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "bool", "name": "_stubSaturation", "type": "bool"}, {"internalType": "bool", "name": "_stubTWAP", "type": "bool"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "type": "error", "name": "AmmalgamMaxSlippage"}, {"inputs": [], "type": "error", "name": "CannotUpdateZeroAddress"}, {"inputs": [], "type": "error", "name": "InvalidIntervalConfig"}, {"inputs": [], "type": "error", "name": "InvalidUserConfiguration"}, {"inputs": [], "type": "error", "name": "LiquidationPremiumTooHigh"}, {"inputs": [], "type": "error", "name": "MaxTrancheOverSaturated"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "type": "error", "name": "OwnableInvalidOwner"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "type": "error", "name": "OwnableUnauthorizedAccount"}, {"inputs": [], "type": "error", "name": "PairAlreadyExists"}, {"inputs": [], "type": "error", "name": "PairDoesNotExist"}, {"inputs": [], "type": "error", "name": "PriceOutOfBounds"}, {"inputs": [{"internalType": "uint8", "name": "bits", "type": "uint8"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "type": "error", "name": "SafeCastOverflowedUintDowncast"}, {"inputs": [], "type": "error", "name": "TickOutOfBounds"}, {"inputs": [{"internalType": "address", "name": "previousOwner", "type": "address", "indexed": true}, {"internalType": "address", "name": "new<PERSON>wner", "type": "address", "indexed": true}], "type": "event", "name": "OwnershipTransferred", "anonymous": false}, {"inputs": [{"internalType": "int16", "name": "lendingStateTick", "type": "int16", "indexed": false}], "type": "event", "name": "UpdateLendingTick", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "", "type": "string", "indexed": false}], "type": "event", "name": "log", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "", "type": "address", "indexed": false}], "type": "event", "name": "log_address", "anonymous": false}, {"inputs": [{"internalType": "uint256[]", "name": "val", "type": "uint256[]", "indexed": false}], "type": "event", "name": "log_array", "anonymous": false}, {"inputs": [{"internalType": "int256[]", "name": "val", "type": "int256[]", "indexed": false}], "type": "event", "name": "log_array", "anonymous": false}, {"inputs": [{"internalType": "address[]", "name": "val", "type": "address[]", "indexed": false}], "type": "event", "name": "log_array", "anonymous": false}, {"inputs": [{"internalType": "bytes", "name": "", "type": "bytes", "indexed": false}], "type": "event", "name": "log_bytes", "anonymous": false}, {"inputs": [{"internalType": "bytes32", "name": "", "type": "bytes32", "indexed": false}], "type": "event", "name": "log_bytes32", "anonymous": false}, {"inputs": [{"internalType": "int256", "name": "", "type": "int256", "indexed": false}], "type": "event", "name": "log_int", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "address", "name": "val", "type": "address", "indexed": false}], "type": "event", "name": "log_named_address", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "uint256[]", "name": "val", "type": "uint256[]", "indexed": false}], "type": "event", "name": "log_named_array", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "int256[]", "name": "val", "type": "int256[]", "indexed": false}], "type": "event", "name": "log_named_array", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "address[]", "name": "val", "type": "address[]", "indexed": false}], "type": "event", "name": "log_named_array", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "bytes", "name": "val", "type": "bytes", "indexed": false}], "type": "event", "name": "log_named_bytes", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "bytes32", "name": "val", "type": "bytes32", "indexed": false}], "type": "event", "name": "log_named_bytes32", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "int256", "name": "val", "type": "int256", "indexed": false}, {"internalType": "uint256", "name": "decimals", "type": "uint256", "indexed": false}], "type": "event", "name": "log_named_decimal_int", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "uint256", "name": "val", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "decimals", "type": "uint256", "indexed": false}], "type": "event", "name": "log_named_decimal_uint", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "int256", "name": "val", "type": "int256", "indexed": false}], "type": "event", "name": "log_named_int", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "string", "name": "val", "type": "string", "indexed": false}], "type": "event", "name": "log_named_string", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "uint256", "name": "val", "type": "uint256", "indexed": false}], "type": "event", "name": "log_named_uint", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "", "type": "string", "indexed": false}], "type": "event", "name": "log_string", "anonymous": false}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256", "indexed": false}], "type": "event", "name": "log_uint", "anonymous": false}, {"inputs": [{"internalType": "bytes", "name": "", "type": "bytes", "indexed": false}], "type": "event", "name": "logs", "anonymous": false}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "IS_TEST", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "uint256", "name": "externalLiquidity", "type": "uint256"}, {"internalType": "uint256", "name": "duration", "type": "uint256"}, {"internalType": "uint256", "name": "allAssetsDepositL", "type": "uint256"}, {"internalType": "uint256", "name": "allAssetsBorrowL", "type": "uint256"}, {"internalType": "uint256", "name": "allSharesBorrowL", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "accruePenalties", "outputs": [{"internalType": "uint112", "name": "penaltyInBorrowLShares", "type": "uint112"}, {"internalType": "uint112", "name": "accountPenaltyInBorrowLShares", "type": "uint112"}]}, {"inputs": [{"internalType": "int16", "name": "newTick", "type": "int16"}], "stateMutability": "view", "type": "function", "name": "boundTick", "outputs": [{"internalType": "int16", "name": "", "type": "int16"}]}, {"inputs": [{"internalType": "struct Validation.InputParams", "name": "inputParams", "type": "tuple", "components": [{"internalType": "uint256[6]", "name": "userAssets", "type": "uint256[6]"}, {"internalType": "uint256", "name": "sqrtPriceMinInQ72", "type": "uint256"}, {"internalType": "uint256", "name": "sqrtPriceMaxInQ72", "type": "uint256"}, {"internalType": "uint256", "name": "activeLiquidityScalerInQ72", "type": "uint256"}, {"internalType": "uint256", "name": "activeLiquidityAssets", "type": "uint256"}, {"internalType": "uint256", "name": "reservesXAssets", "type": "uint256"}, {"internalType": "uint256", "name": "reservesYAssets", "type": "uint256"}]}, {"internalType": "uint256", "name": "liqSqrtPriceInXInQ72", "type": "uint256"}, {"internalType": "uint256", "name": "liqSqrtPriceInYInQ72", "type": "uint256"}, {"internalType": "address", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address"}, {"internalType": "address", "name": "account", "type": "address"}], "stateMutability": "view", "type": "function", "name": "calcSatChangeRatioBips", "outputs": [{"internalType": "uint256", "name": "ratioNetXBips", "type": "uint256"}, {"internalType": "uint256", "name": "ratioNetYBips", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address"}, {"internalType": "uint24", "name": "_longTermIntervalConfig", "type": "uint24"}], "stateMutability": "nonpayable", "type": "function", "name": "configLongTermInterval"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "excludeArtifacts", "outputs": [{"internalType": "string[]", "name": "excludedArtifacts_", "type": "string[]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "excludeContracts", "outputs": [{"internalType": "address[]", "name": "excludedContracts_", "type": "address[]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "excludeSelectors", "outputs": [{"internalType": "struct StdInvariant.FuzzSelector[]", "name": "excludedSelectors_", "type": "tuple[]", "components": [{"internalType": "address", "name": "addr", "type": "address"}, {"internalType": "bytes4[]", "name": "selectors", "type": "bytes4[]"}]}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "excludeSenders", "outputs": [{"internalType": "address[]", "name": "excludedSenders_", "type": "address[]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "failed", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address"}, {"internalType": "bool", "name": "netDebtX", "type": "bool"}, {"internalType": "address", "name": "accountAddress", "type": "address"}], "stateMutability": "view", "type": "function", "name": "getAccount", "outputs": [{"internalType": "struct Saturation.Account", "name": "", "type": "tuple", "components": [{"internalType": "bool", "name": "exists", "type": "bool"}, {"internalType": "int16", "name": "lastTranche", "type": "int16"}, {"internalType": "uint112", "name": "penaltyInBorrowLShares", "type": "uint112"}, {"internalType": "struct Saturation.SaturationPair[]", "name": "satPairPerTranche", "type": "tuple[]", "components": [{"internalType": "uint128", "name": "satInLAssets", "type": "uint128"}, {"internalType": "uint128", "name": "satRelativeToL", "type": "uint128"}]}, {"internalType": "uint256[]", "name": "treePenaltyAtOnsetInBorrowLSharesPerSatInQ72PerTranche", "type": "uint256[]"}]}]}, {"inputs": [{"internalType": "address", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address"}, {"internalType": "bool", "name": "netDebtX", "type": "bool"}, {"internalType": "uint256", "name": "leafIndex", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "getLeafDetails", "outputs": [{"internalType": "struct Saturation.SaturationPair", "name": "saturation", "type": "tuple", "components": [{"internalType": "uint128", "name": "satInLAssets", "type": "uint128"}, {"internalType": "uint128", "name": "satRelativeToL", "type": "uint128"}]}, {"internalType": "uint256", "name": "currentPenaltyInBorrowLSharesPerSatInQ72", "type": "uint256"}, {"internalType": "uint16[]", "name": "tranches", "type": "uint16[]"}]}, {"inputs": [{"internalType": "int56", "name": "newTick", "type": "int56"}, {"internalType": "uint32", "name": "timeElapsedSinceUpdate", "type": "uint32"}, {"internalType": "uint32", "name": "timeElapsedSinceLendingUpdate", "type": "uint32"}], "stateMutability": "view", "type": "function", "name": "getLendingStateTick", "outputs": [{"internalType": "int16", "name": "lendingStateTick", "type": "int16"}, {"internalType": "uint256", "name": "maxSatInWads", "type": "uint256"}]}, {"inputs": [{"internalType": "uint32", "name": "timeElapsedSinceUpdate", "type": "uint32"}, {"internalType": "uint32", "name": "timeElapsedSinceLendingUpdate", "type": "uint32"}], "stateMutability": "nonpayable", "type": "function", "name": "getLendingStateTickAndCheckpoint", "outputs": [{"internalType": "int16", "name": "lendingStateTick", "type": "int16"}, {"internalType": "uint256", "name": "maxSatInWads", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address"}], "stateMutability": "view", "type": "function", "name": "getObservations", "outputs": [{"internalType": "struct GeometricTWAP.Observations", "name": "", "type": "tuple", "components": [{"internalType": "bool", "name": "isMidTermBufferInitialized", "type": "bool"}, {"internalType": "bool", "name": "isLongTermBufferInitialized", "type": "bool"}, {"internalType": "uint8", "name": "midTermIndex", "type": "uint8"}, {"internalType": "uint8", "name": "longTermIndex", "type": "uint8"}, {"internalType": "int16", "name": "lastTick", "type": "int16"}, {"internalType": "int16", "name": "lastLendingStateTick", "type": "int16"}, {"internalType": "uint24", "name": "midTermIntervalConfig", "type": "uint24"}, {"internalType": "uint24", "name": "longTermIntervalConfig", "type": "uint24"}, {"internalType": "int56", "name": "lendingCumulativeSum", "type": "int56"}, {"internalType": "int56[51]", "name": "midTermCumulativeSum", "type": "int56[51]"}, {"internalType": "int56[9]", "name": "longTermCumulativeSum", "type": "int56[9]"}, {"internalType": "uint32[51]", "name": "midTermTimeInterval", "type": "uint32[51]"}, {"internalType": "uint32[9]", "name": "longTermTimeInterval", "type": "uint32[9]"}]}]}, {"inputs": [{"internalType": "bool", "name": "isLongTermBufferInitialized", "type": "bool"}], "stateMutability": "view", "type": "function", "name": "getObservedMidTermTick", "outputs": [{"internalType": "int16", "name": "", "type": "int16"}]}, {"inputs": [{"internalType": "address", "name": "pair", "type": "address"}, {"internalType": "int16", "name": "currentTick", "type": "int16"}, {"internalType": "bool", "name": "includeLongTermTick", "type": "bool"}], "stateMutability": "view", "type": "function", "name": "getTickRange", "outputs": [{"internalType": "int16", "name": "", "type": "int16"}, {"internalType": "int16", "name": "", "type": "int16"}]}, {"inputs": [{"internalType": "address", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address"}, {"internalType": "bool", "name": "netDebtX", "type": "bool"}, {"internalType": "int16", "name": "tranche", "type": "int16"}], "stateMutability": "view", "type": "function", "name": "getTrancheDetails", "outputs": [{"internalType": "uint16", "name": "leaf", "type": "uint16"}, {"internalType": "struct Saturation.SaturationPair", "name": "saturation", "type": "tuple", "components": [{"internalType": "uint128", "name": "satInLAssets", "type": "uint128"}, {"internalType": "uint128", "name": "satRelativeToL", "type": "uint128"}]}]}, {"inputs": [{"internalType": "address", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address"}, {"internalType": "bool", "name": "netDebtX", "type": "bool"}], "stateMutability": "view", "type": "function", "name": "getTreeDetails", "outputs": [{"internalType": "uint16", "name": "", "type": "uint16"}, {"internalType": "uint128", "name": "", "type": "uint128"}]}, {"inputs": [{"internalType": "int16", "name": "firstTick", "type": "int16"}], "stateMutability": "nonpayable", "type": "function", "name": "init"}, {"inputs": [{"internalType": "struct Validation.InputParams", "name": "inputParams", "type": "tuple", "components": [{"internalType": "uint256[6]", "name": "userAssets", "type": "uint256[6]"}, {"internalType": "uint256", "name": "sqrtPriceMinInQ72", "type": "uint256"}, {"internalType": "uint256", "name": "sqrtPriceMaxInQ72", "type": "uint256"}, {"internalType": "uint256", "name": "activeLiquidityScalerInQ72", "type": "uint256"}, {"internalType": "uint256", "name": "activeLiquidityAssets", "type": "uint256"}, {"internalType": "uint256", "name": "reservesXAssets", "type": "uint256"}, {"internalType": "uint256", "name": "reservesYAssets", "type": "uint256"}]}, {"internalType": "address", "name": "borrower", "type": "address"}, {"internalType": "struct Liquidation.HardLiquidationParams", "name": "hardLiquidationParams", "type": "tuple", "components": [{"internalType": "uint256", "name": "depositLToBeTransferredInLAssets", "type": "uint256"}, {"internalType": "uint256", "name": "depositXToBeTransferredInXAssets", "type": "uint256"}, {"internalType": "uint256", "name": "depositYToBeTransferredInYAssets", "type": "uint256"}, {"internalType": "uint256", "name": "repayLXInXAssets", "type": "uint256"}, {"internalType": "uint256", "name": "repayLYInYAssets", "type": "uint256"}, {"internalType": "uint256", "name": "repayXInXAssets", "type": "uint256"}, {"internalType": "uint256", "name": "repayYInYAssets", "type": "uint256"}]}, {"internalType": "uint256", "name": "actualRepaidLiquidityAssets", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "liquidationCheckHardPremiums", "outputs": [{"internalType": "bool", "name": "badDebt", "type": "bool"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "longTermIntervalConfig", "outputs": [{"internalType": "uint24", "name": "", "type": "uint24"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "midTermIntervalConfig", "outputs": [{"internalType": "uint24", "name": "", "type": "uint24"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "int16", "name": "newTick", "type": "int16"}, {"internalType": "uint32", "name": "timeElapsed", "type": "uint32"}], "stateMutability": "nonpayable", "type": "function", "name": "recordObservation", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "renounceOwnership"}, {"inputs": [{"internalType": "address", "name": "pair", "type": "address"}, {"internalType": "uint256", "name": "maxDesiredSaturationMag2", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "setNewPositionSaturation"}, {"inputs": [{"internalType": "int16", "name": "tickMin", "type": "int16"}, {"internalType": "int16", "name": "tickMax", "type": "int16"}], "stateMutability": "nonpayable", "type": "function", "name": "setTickRange"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetArtifactSelectors", "outputs": [{"internalType": "struct StdInvariant.FuzzArtifactSelector[]", "name": "targetedArtifactSelectors_", "type": "tuple[]", "components": [{"internalType": "string", "name": "artifact", "type": "string"}, {"internalType": "bytes4[]", "name": "selectors", "type": "bytes4[]"}]}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetArtifacts", "outputs": [{"internalType": "string[]", "name": "targetedArtifacts_", "type": "string[]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetContracts", "outputs": [{"internalType": "address[]", "name": "targetedContracts_", "type": "address[]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetInterfaces", "outputs": [{"internalType": "struct StdInvariant.FuzzInterface[]", "name": "targetedInterfaces_", "type": "tuple[]", "components": [{"internalType": "address", "name": "addr", "type": "address"}, {"internalType": "string[]", "name": "artifacts", "type": "string[]"}]}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetSelectors", "outputs": [{"internalType": "struct StdInvariant.FuzzSelector[]", "name": "targetedSelectors_", "type": "tuple[]", "components": [{"internalType": "address", "name": "addr", "type": "address"}, {"internalType": "bytes4[]", "name": "selectors", "type": "bytes4[]"}]}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetSenders", "outputs": [{"internalType": "address[]", "name": "targetedSenders_", "type": "address[]"}]}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "transferOwnership"}, {"inputs": [{"internalType": "struct Validation.InputParams", "name": "inputParams", "type": "tuple", "components": [{"internalType": "uint256[6]", "name": "userAssets", "type": "uint256[6]"}, {"internalType": "uint256", "name": "sqrtPriceMinInQ72", "type": "uint256"}, {"internalType": "uint256", "name": "sqrtPriceMaxInQ72", "type": "uint256"}, {"internalType": "uint256", "name": "activeLiquidityScalerInQ72", "type": "uint256"}, {"internalType": "uint256", "name": "activeLiquidityAssets", "type": "uint256"}, {"internalType": "uint256", "name": "reservesXAssets", "type": "uint256"}, {"internalType": "uint256", "name": "reservesYAssets", "type": "uint256"}]}, {"internalType": "address", "name": "account", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "update"}], "devdoc": {"kind": "dev", "methods": {"accruePenalties(address,uint256,uint256,uint256,uint256,uint256)": {"params": {"allAssetsBorrowL": "allAsset[BORROW_L]", "allAssetsDepositL": "allAsset[DEPOSIT_L]", "allSharesBorrowL": "allShares[BORROW_L]", "duration": "since last accrual of penalties", "externalLiquidity": "Swap liquidity outside this pool"}}, "boundTick(int16)": {"details": "The function ensures that `newTick` stays within the bounds      determined by `lastTick` and a dynamically calculated factor.", "params": {"newTick": "The proposed new tick value to be adjusted within valid bounds."}, "returns": {"_0": "The adjusted tick value constrained within the allowable range."}}, "calcSatChangeRatioBips((uint256[6],uint256,uint256,uint256,uint256,uint256,uint256),uint256,uint256,address,address)": {"params": {"account": "The account for which we are calculating the saturation change ratio.", "inputParams": "The params containing the position of `account`.", "liqSqrtPriceInXInQ72": "The liquidation price.", "pairAddress": "The address of the pair"}, "returns": {"ratioNetXBips": "The ratio representing the change in netX saturation for account.", "ratioNetYBips": "The ratio representing the change in netY saturation for account."}}, "configLongTermInterval(address,uint24)": {"details": "This function is used to set the long-term interval between observations for the long-term buffer.", "params": {"_longTermIntervalConfig": "The desired duration for each long-term period.      The size is set as a factor of the mid-term interval to ensure a sufficient buffer, requiring      at least 16 * 12 = 192 seconds per period, resulting in a total of ~25 minutes (192 * 8 = 1536 seconds)      for the long-term buffer.", "pairAddress": "The address of the pair for which the long-term interval is being configured."}}, "getLendingStateTick(int56,uint32,uint32)": {"params": {"newTick": "The new tick value to be recorded, representing the most recent update of reserveXAssets and reserveYAssets.", "timeElapsedSinceLendingUpdate": "The time elapsed since the last lending update.", "timeElapsedSinceUpdate": "The time elapsed since the last price update."}, "returns": {"lendingStateTick": "The tick value representing the TWAP since the last lending update.", "maxSatInWads": "The maximum saturation in WADs."}}, "getLendingStateTickAndCheckpoint(uint32,uint32)": {"details": "See `getLendingStateTick` for implementation details which was      separated to allow view access without any state updates.", "params": {"timeElapsedSinceLendingUpdate": "The time elapsed since the last lending update.", "timeElapsedSinceUpdate": "The time elapsed since the last price update."}, "returns": {"lendingStateTick": "The tick value representing the TWAP since the last lending update."}}, "getObservedMidTermTick(bool)": {"details": "Retrieves the mid-term tick value based on the stored observations.", "params": {"isLongTermBufferInitialized": "Boolean value which represents whether long-term buffer is filled or not."}, "returns": {"_0": "midTermTick The mid-term tick value."}}, "getTickRange(address,int16,bool)": {"details": "This function calculates the minimum and maximum tick values among three observed ticks:          long-term tick, mid-term tick, and current tick.", "params": {"currentTick": "The current (most recent) tick based on the current reserves.", "includeLongTermTick": "Boolean value indicating whether to include the long-term tick in the range.", "pair": "The address of the pair for which the tick range is being calculated."}, "returns": {"_0": "minTick The minimum tick value among the three observed ticks.", "_1": "maxTick The maximum tick value among the three observed ticks."}}, "init(int16)": {"details": "initCheck can be removed once the tree structure is fixed"}, "owner()": {"details": "Returns the address of the current owner."}, "recordObservation(int16,uint32)": {"details": "This function is used to record new observation data for the contract. It ensures that      the provided tick value is stored appropriately in both mid-term and long-term      observations, updates interval counters, and handles tick cumulative values based      on the current interval configuration. Ensures that this function is called in      chronological order, with increasing timestamps. Returns in case the      provided block timestamp is less than or equal to the last recorded timestamp.", "params": {"newTick": "The new tick value to be recorded, representing the most recent update of      reserveXAssets and reserveYAssets.", "timeElapsed": "The time elapsed since the last observation."}, "returns": {"_0": "bool indicating whether the observation was recorded or not."}}, "renounceOwnership()": {"details": "Leaves the contract without owner. It will not be possible to call `onlyOwner` functions. Can only be called by the current owner. NOTE: Renouncing ownership will leave the contract without an owner, thereby disabling any functionality that is only available to the owner."}, "transferOwnership(address)": {"details": "Transfers ownership of the contract to a new account (`newOwner`). Can only be called by the current owner."}, "update((uint256[6],uint256,uint256,uint256,uint256,uint256,uint256),address)": {"details": "run accruePenalties before running this function", "params": {"account": "for which is position is being updated", "inputParams": "contains the position and pair params, like account borrows/deposits, current price and active liquidity"}}}, "version": 1}, "userdoc": {"kind": "user", "methods": {"accruePenalties(address,uint256,uint256,uint256,uint256,uint256)": {"notice": "accrue penalties since last accrual based on all over saturated positions"}, "calcSatChangeRatioBips((uint256[6],uint256,uint256,uint256,uint256,uint256,uint256),uint256,uint256,address,address)": {"notice": "Calculate the ratio by which the saturation has changed for `account`."}, "configLongTermInterval(address,uint24)": {"notice": "Configures the interval of long-term observations."}, "getLendingStateTick(int56,uint32,uint32)": {"notice": "Gets the tick value representing the TWAP since the last lending update."}, "getLendingStateTickAndCheckpoint(uint32,uint32)": {"notice": "Gets the tick value representing the TWAP since the last         lending update and checkpoints the current lending cumulative sum         as `self.lendingCumulativeSum` and the current block timestamp as `self.lastLendingTimestamp`."}, "getTickRange(address,int16,bool)": {"notice": "Gets the min and max range of tick values from the stored oracle observations."}, "init(int16)": {"notice": "initializes the sat and TWAP struct"}, "recordObservation(int16,uint32)": {"notice": "Records a new observation tick value and updates the observation data."}, "update((uint256[6],uint256,uint256,uint256,uint256,uint256,uint256),address)": {"notice": "update the borrow position of an account and potentially check (and revert) if the resulting sat is too high"}}, "version": 1}}, "settings": {"remappings": ["1inch/=lib/1inch/", "@1inch/=lib/1inch/", "@mangrovedao/mangrove-core/=lib/mangrove-core/", "@mgv/forge-std/=lib/mangrove-core/lib/forge-std/src/", "@mgv/lib/=lib/mangrove-core/lib/", "@mgv/script/=lib/mangrove-core/script/", "@mgv/src/=lib/mangrove-core/src/", "@mgv/test/=lib/mangrove-core/test/", "@morpho-org/morpho-blue/=lib/morpho-blue/", "@openzeppelin/=lib/openzeppelin-contracts/", "ExcessivelySafeCall/=lib/ExcessivelySafeCall/src/", "core/=lib/mangrove-core/lib/core/", "ds-test/=lib/ExcessivelySafeCall/lib/ds-test/src/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "forge-std/=lib/openzeppelin-contracts/lib/forge-std/src/", "halmos-cheatcodes/=lib/morpho-blue/lib/halmos-cheatcodes/src/", "mangrove-core/=lib/mangrove-core/", "morpho-blue/=lib/morpho-blue/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "preprocessed/=lib/mangrove-core/lib/preprocessed/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"test/shared/FactoryPairTestFixture.sol": "StubSaturationAndGeometricTWAPState"}, "evmVersion": "cancun", "libraries": {}}, "sources": {"contracts/AmmalgamPair.sol": {"keccak256": "0xe8f98844a55a216605e6c6dd6837977fafda458a6b5d0cfa1f3a18d25e9432e4", "urls": ["bzz-raw://65dda1a1de8dd64e31c666b13de3d0583b4b0da923c67065cadcddefe47562a2", "dweb:/ipfs/Qmaev9WFa4yyL8fXVoWkXwNsTTY8wY7jTBGDoKJbdwSCzS"], "license": null}, "contracts/SaturationAndGeometricTWAPState.sol": {"keccak256": "0x5e293a35668bb216a99379ea2176894314cc0f1ac68644fcf4c07017da1a4419", "urls": ["bzz-raw://00349bb86f1b657010919b4bc3f616ad56ef4883b99ab0eead36815dae93dc76", "dweb:/ipfs/QmbEd9GD2JxuDntX35YcfbSCcpRstDU9GDPUkBKGzsxvqE"], "license": "GPL-3.0-only"}, "contracts/factories/AmmalgamFactory.sol": {"keccak256": "0xe0d9baf63d9538a7ecb8bd24ea61a8cdf6fc9c1e9eb028f343548adeb8b93e4e", "urls": ["bzz-raw://bfca24db47fbbaeef8bc36996cbfed78eb48771ac71d2800f081fb58a8e8c92b", "dweb:/ipfs/QmUfYEwfmrjSmchqXi58SnpSina4qKQvD2Jbk5RqYsaoCa"], "license": "GPL-3.0-only"}, "contracts/factories/ERC20DebtLiquidityTokenFactory.sol": {"keccak256": "0x72e3ada6a2f0792a353b730c1b45ae832f9ce2f58f0bda039383f8890cb2a4f7", "urls": ["bzz-raw://4949e7b66647313aaba2e11d7edde06eb87345b476c1a20f890659c1af827b2b", "dweb:/ipfs/Qmf3emVXfGp1oc8iVYxnVqpJ88vnxxdj7WqPm1vzVKb1SD"], "license": "GPL-3.0-only"}, "contracts/factories/ERC20LiquidityTokenFactory.sol": {"keccak256": "0x762974ca1ed600e0930a92bd2eb3a1a5f9ef0469ab2e6e811e4674e098238762", "urls": ["bzz-raw://5fd5f33537aeea9bac1f18c6fca2057899ec5f90cb8c756622eb436d5b13e27e", "dweb:/ipfs/QmfYznzzwN1AmdnuzNKe1R6t8UeztaZVGuzJ8vKfzjMXYN"], "license": "GPL-3.0-only"}, "contracts/factories/ERC4626DebtTokenFactory.sol": {"keccak256": "0x7deeb7a40d26bc790112f29836da83050fa3554e471e1dce4dda6bf29ab9bf67", "urls": ["bzz-raw://5a46a4c8270e0b8a731259328b6c35c84de270a14f2f69ba04bc58d18400efc6", "dweb:/ipfs/QmQ56QbX6S9GjQinsFYtTMns6HgpcTXW1wnvQT6QgiuW1Z"], "license": "GPL-3.0-only"}, "contracts/factories/ERC4626DepositTokenFactory.sol": {"keccak256": "0xf84b75119f2680f8079bb9567b0c03c0ad49b71a8c00f968d03d5fca2a954035", "urls": ["bzz-raw://c3fc7a9e300a935991746d5be835418b09e6d2b20b65e3e297d4faf28516469b", "dweb:/ipfs/QmQMr9MA5a3UcZCiP3e2haYqzBsbE8Pe6rDq6j6RJ3ub4Z"], "license": "GPL-3.0-only"}, "contracts/factories/NewTokensFactory.sol": {"keccak256": "0x86cd420e1df8a59b11a4ab53a16971a44953f0a07741ef69d95baa4bd60126ac", "urls": ["bzz-raw://d8cdd98060f059705b9ae2b64ab3e74395c0f3a24e12f5ac11ca7e509c6a7aa0", "dweb:/ipfs/QmahgKkRzuWHpQ73DHGZ4Kvd2MQG7MpfPShayJDRJQYSVr"], "license": "GPL-3.0-only"}, "contracts/interfaces/IAmmalgamPair.sol": {"keccak256": "0xa17e45b2348d8920d9970c5d50b300fc0a1e8d03350cdd0d1a624494baa70337", "urls": ["bzz-raw://8d252e89e5d49d1c15a0c0c0a495a325b9f8d608714b29279a7bacb1e4bf8795", "dweb:/ipfs/QmRkZ7a8JJQYEw6HQMJjjkuAK8b5Th1X1ET6BG1R8mx4qw"], "license": "GPL-3.0-only"}, "contracts/interfaces/ISaturationAndGeometricTWAPState.sol": {"keccak256": "0xc9add2ad41f8edd9d360ced8d2cd7bd18dd500304794434fb2e309fa0f5af83c", "urls": ["bzz-raw://8ecc810c544ac734ef26a2f6bebea3f3bd12d773965d297991e0e0e72892fa20", "dweb:/ipfs/QmarXc1Ut4FZzPRRZs2M2udbJjuZUJQHQ8fvmSr3bpHErR"], "license": "GPL-3.0-only"}, "contracts/interfaces/callbacks/IAmmalgamCallee.sol": {"keccak256": "0x904b858859d460a61c9e644ca87009d8e32ba20482ef218801c89c7fb1ece339", "urls": ["bzz-raw://1a7cedebbacc453e3e4e339fcc76fd3268247c13982de82b4930d59a44256c1d", "dweb:/ipfs/QmdjdvYabbwAYcV72xjiXyq278xQivFtiqD3eQ5P9Gk4f1"], "license": "GPL-3.0-only"}, "contracts/interfaces/callbacks/ITransferValidator.sol": {"keccak256": "0x6d9028fc4ad1914e6b2091e6ba46a9f836f9e67ea435c4a8fef41363f2ceaf56", "urls": ["bzz-raw://7ecaade4884d460168f6978edf35706f7b9e363de6002942b1d171a338dca6a4", "dweb:/ipfs/QmS5wgfDt5Pn68rpCytpzhiy57LcmivVFQ5XLGXUUP5Tt8"], "license": "GPL-3.0-only"}, "contracts/interfaces/factories/IAmmalgamFactory.sol": {"keccak256": "0x1c80089901e8d7d7451775b5eaa92092eb2b65319cb92fa7884281bae49f52b8", "urls": ["bzz-raw://bf1201399bb9d5046e0e788ff88394b2468771096a2a0d3500af542923e84628", "dweb:/ipfs/QmeUaPyQpbPbP5fyPUT2FfzeDgHcdyQAn1DaNg9uCuGoj9"], "license": "GPL-3.0-only"}, "contracts/interfaces/factories/IFactoryCallback.sol": {"keccak256": "0x33250cf8351adb4846a3d133a9bc06568288e4c680bcf5b1085e3bca40a35e52", "urls": ["bzz-raw://5663a39af4ed3040a58beaa5641425b9adca83c2683dd220e0c11e644fefe52b", "dweb:/ipfs/QmYB8Vf37WDzQfSpMDjv8hVicuaF1wMBzf7xjHRjGCy3wT"], "license": "GPL-3.0-only"}, "contracts/interfaces/factories/INewTokensFactory.sol": {"keccak256": "0x3b2f1ee34106d2694a9ebbe600be692bed645f4247f4a24da3d5ec46025ab3e9", "urls": ["bzz-raw://73143452a06db52deb593585fea6f2ef7c46e9ef6d649562dc39e79e4e5dca2b", "dweb:/ipfs/QmYQEy7BZWnfWKnuac8GB4QPhG5qJpaHQAfkTBoUDEuX1E"], "license": "GPL-3.0-only"}, "contracts/interfaces/factories/ITokenFactory.sol": {"keccak256": "0xac23e5c0441599add526b0c308faa7787f90bf01603b6dbc231944c166ca32d6", "urls": ["bzz-raw://ac574b98b2c1034786581137a218277ec58e06e9612f76814f34960383083626", "dweb:/ipfs/QmZgZqVnshjzuHBXJTR9g87S15CyLwJUSErGEDoJpBd4kg"], "license": "GPL-3.0-only"}, "contracts/interfaces/tokens/IAmmalgamERC20.sol": {"keccak256": "0x44a376269170b4270ec221ce3cb31a609b394e216cc4d2e27b818361b4369829", "urls": ["bzz-raw://c48bc7586631f27ede73d3d0b4c1d7a29b1653e6c501c8b7fc9877c125f8f57e", "dweb:/ipfs/QmTSLtqnsxr7h7ct524rqYssHUo4qursmCZ7g5q3J1qQPK"], "license": "GPL-3.0-only"}, "contracts/interfaces/tokens/IERC20DebtToken.sol": {"keccak256": "0xc50c6be17633c8ac677b4eaac7c05a6de1f1c938237179b59ad5e65bcfbcb03a", "urls": ["bzz-raw://6c75327e01d70a0c22662a9a8214aa64e45c517146971f8636e5aa5bf06e7696", "dweb:/ipfs/QmV2ydBQ5S9ZBtRuPgBMBdvd2Hcnn8quCGEMhhAAuic15b"], "license": "GPL-3.0-only"}, "contracts/interfaces/tokens/IPluginRegistry.sol": {"keccak256": "0x9a677620d88ac7dc42afb21d82a7b7a89bd934c1cada5450cf2b6200bf374ccf", "urls": ["bzz-raw://304091e5d54e9ad7db24ba1022e84e39dd9305d9cc72fd87423c71b40de4ab3d", "dweb:/ipfs/QmZEF5MfmUcxzLF9fcGCLvGMTTLLhcWdCMCDK2WyXj6s7X"], "license": "MIT"}, "contracts/interfaces/tokens/ITokenController.sol": {"keccak256": "0x7778001aaf582fe10005240eb6023b2b6cee3f100b6c2222bf6b9ade93732624", "urls": ["bzz-raw://91e5c4519207d6a450be1e0a8649157e86d20f8ef6a91ff6512a31cf5561a570", "dweb:/ipfs/QmUqZLW27JJZHFPf2fgLDYSWWj5gM158DdaxTTmDVukRAg"], "license": "GPL-3.0-only"}, "contracts/libraries/Convert.sol": {"keccak256": "0x944776d31291de1a9cdc6a52154c23c22b43a01c3edebe7a4140e267edbba975", "urls": ["bzz-raw://36c03749859077ba47a3acfd574f8c30f34f97def4ce81d7f4feac9a7b62794c", "dweb:/ipfs/QmdycZay5X2WrbS8qS7RycLpZbMQx7yKszWQzGU3rqidpH"], "license": "GPL-3.0-only"}, "contracts/libraries/GeometricTWAP.sol": {"keccak256": "0x3860409daa0fdb5d96f0bfb8b49cbca058b9fe32c8e32457f85d4ee2c5cdcb1e", "urls": ["bzz-raw://81fe70a80f4005a6529c7e93f1a92547ce0bf74c357c280a91e8778b378b18aa", "dweb:/ipfs/QmdRQ1DqsCu11zfbLAbrrzJ9Ups3oKgTGimYo3Zm3ALiCz"], "license": "GPL-3.0-only"}, "contracts/libraries/Interest.sol": {"keccak256": "0xbc8bfa20d7295dd70e3c716fd3dbeb5b45d313e3c609d063d186042cbf000646", "urls": ["bzz-raw://b015e8d4976d3b6d7eaca07dfcc487aeed3a7d8b4c41c8369a7476dcfb211194", "dweb:/ipfs/QmecH84UnZYxDZ2aL6rQtnrEExLEAfo7q4Y47yuBXdymeX"], "license": "GPL-3.0-only"}, "contracts/libraries/Liquidation.sol": {"keccak256": "0x842bc44bc3cff80360ab82c5920070b12680edefe9267bdffc2d6c3c3a692d63", "urls": ["bzz-raw://85ecd75568a0729aec06741d0575ed07dad8b7daebd7ba3114a93f6019250877", "dweb:/ipfs/QmQMvWdsPWsQ4t1yv6eyZy5TM7h1EZpSJdt5b8fDLcumCW"], "license": "GPL-3.0-only"}, "contracts/libraries/QuadraticSwapFees.sol": {"keccak256": "0x00f6b7909be4fa1fc1ba426dd8ae659d1c5cb20c79665148898c973f55cfdccb", "urls": ["bzz-raw://c64da0826a9b0ffc08319709f6db03339d22d24deda902a6540393251da0aecb", "dweb:/ipfs/QmSNwBbn2VAS8HPY4hNZusEc4DoKKZAZHtpPdjL9Gz3gs3"], "license": "GPL-3.0-only"}, "contracts/libraries/Saturation.sol": {"keccak256": "0xf44bc610ece4bc7ebdb0730aa6ad69ea47647e19d4c1944c663d2d2eb4f10860", "urls": ["bzz-raw://421fdf8d0b27132bc324a42ede9aaf23b476e5134e1073f84e824620a2a44f20", "dweb:/ipfs/QmbvSfMuMzDmrfPkCAEp7ydtRDWu5EUiXq4MyrGGjFErzE"], "license": "GPL-3.0-only"}, "contracts/libraries/TickMath.sol": {"keccak256": "0x753813c7ed638d22edb71f48f8eb8b4283b3db2ba5b136b5c8909bd37ffa3f12", "urls": ["bzz-raw://04dd5085b72f6d73e1b17f58148e4d03639f654bdc4fdbc173b7c92ff102fc20", "dweb:/ipfs/QmSg4xTQPkngjNxs84428FZdSwH4AUQpwLXaASx7Qev6oG"], "license": "GPL-2.0-or-later"}, "contracts/libraries/TokenSymbol.sol": {"keccak256": "0x628df064fdbdacfe6783964d7bf38cdf1b34e1ad07caa3cea39bf7468cc19b43", "urls": ["bzz-raw://da6823ce0debaabe20f25281e81a4fc88de98d4df2942a5e276826ac381c227b", "dweb:/ipfs/QmNpEuQ25788xfcJwPk2xUB7fyP7fW5ENK2e9qgRqp1BcH"], "license": "GPL-3.0-only"}, "contracts/libraries/Uint16Set.sol": {"keccak256": "0x26a714430fe1618d78386e953153b4bd2bf024baee54453ec9a7a0cc60e1534f", "urls": ["bzz-raw://8667dd78541d656a09678e5f9cce4d49adc805955604ccaaec414e9b241f5e06", "dweb:/ipfs/QmZVWU2CzyDQfGit32HjJxDphBJMKG3d6JRuxbC682Z1gy"], "license": "GPL-3.0-only"}, "contracts/libraries/Validation.sol": {"keccak256": "0x294848b2af973dbcd8b83732a57b67f14fd15e4af0668de05a2928b8eca5a463", "urls": ["bzz-raw://fab25c941e87f6924b31e3f20742ca6b5ec1b7e4251543f4a61567a04ef4d778", "dweb:/ipfs/Qmf4ChH8afdHc3SfXkFPpNGp3e1hscyvnujPAMza3yuXeA"], "license": "GPL-3.0-only"}, "contracts/libraries/constants.sol": {"keccak256": "0x0dfb294985a8f48287ff13e8476718ddb5334b1d8bf6bfa59a5db1dbcf6ca7c4", "urls": ["bzz-raw://4bedcfdb2850cfb22b5daa768ab8125b4ccab97c90068d1d0ad4495bf942b362", "dweb:/ipfs/Qmf9p88yQN2JYRBR5D7q9BLmwhDJWpFk47ZuayrKqCyHat"], "license": "GPL-3.0-only"}, "contracts/tokens/ERC20Base.sol": {"keccak256": "0xdd3db9eaa855b6ff747ffaa0e74dd2a64dd5b0d704356acb75c2690c3fc6bc2b", "urls": ["bzz-raw://8f9e05ae3078bc3a29ef43861b006fb290606c1c9b2676baaed3ab98ecdb2d59", "dweb:/ipfs/QmZrvgxYtUD6jQVBvM9rT7jj5Vzb5csiThGj3ZwHSPryAL"], "license": "MIT"}, "contracts/tokens/ERC20DebtBase.sol": {"keccak256": "0xc0a59cd54fcd847b160d662aa45a5fe7d24ed90c8030fe17fd5f9def427ed19a", "urls": ["bzz-raw://365c7f18505af36b2806404b1b3f2d897de6ac18e255ecfbb4ccc491cac7e444", "dweb:/ipfs/QmUqx8EBwRb6W1YQPb9MjwAhEEHNpZTCopbGWb1vbyuUpp"], "license": "MIT"}, "contracts/tokens/ERC20DebtLiquidityToken.sol": {"keccak256": "0xf222ad5562ed41d74b0cfb5b4aad84ec9f4cb91b6d71928b30b018bab494efe8", "urls": ["bzz-raw://a8e8f3e7ded2eae04c63ce3ae7a86c051d48d6db697cb6929d7064a4ec9d7371", "dweb:/ipfs/QmU3EuwHU3xB1e6MxaRjSRJcDMK73wfZig9uGWqZPaHnTn"], "license": "MIT"}, "contracts/tokens/ERC20LiquidityToken.sol": {"keccak256": "0x2bb2429e551c031034c747749373d2e4c451580e9b203b689d6eaf03ad896358", "urls": ["bzz-raw://9ad5902756073578beee9068b74bd921e59a36b803cf34ef01570c670363689e", "dweb:/ipfs/QmTkT5K2XcB3ZbPDqd4ZAfnZMp2reCzu3Pv7JpRqhAtZHP"], "license": "MIT"}, "contracts/tokens/ERC4626DebtToken.sol": {"keccak256": "0xe69b1ed2fb7b2d7c24c6838462001988b8e51795d215cfa74b9874d17257509e", "urls": ["bzz-raw://c4f201e5f5621689046c58863ab9270cf770c68810d52269d1fc2ac93a7ccf96", "dweb:/ipfs/QmdtALf6LQdHhce3HsNdVtomQu8e5F5QcYU6S7H1PeBThZ"], "license": "MIT"}, "contracts/tokens/ERC4626DepositToken.sol": {"keccak256": "0xd914aa43dc5e9f2f02f98b05561faf6f00853b701f51dfcd7a08a31feaf220be", "urls": ["bzz-raw://8c2282d40855587b2ac70e89d4e0e147b9afe32a41245fffc96b923a9e5ce7ac", "dweb:/ipfs/QmVn3tBkZcXKnqjfnLTXFkKtu1EetDL1UF7rRjNrHdRCSM"], "license": "MIT"}, "contracts/tokens/PluginRegistry.sol": {"keccak256": "0x9263d71fc32da7d0ca4f8d272f8d75d565c1f06281952481322983bae9d7b488", "urls": ["bzz-raw://c9dcbc64172f4339547865b4f041826f0de5d464900f316edbe72e7d6bfb396d", "dweb:/ipfs/QmQykSWuY8xLJotWUPgG5JQDS5DmA2E5Hjb4c6Bz4YnbBQ"], "license": "MIT"}, "contracts/tokens/TokenController.sol": {"keccak256": "0x8b76b9ebb9385f0c4b7c0b8210fb96b11a49a8c9a3a6e855752c32a5c12d54e6", "urls": ["bzz-raw://de87bdae81940f397136f665d68907d4e4c32f35bf2cd0c9e9305a9fe190d159", "dweb:/ipfs/Qmce4hM6xofBYxzAXesHX4hkiHBexoGeQpCzpeCARctnCn"], "license": "GPL-3.0-only"}, "contracts/utils/deployHelper.sol": {"keccak256": "0x9b9dd84e234bb2ffbf51da7e9ab42fe7b6329acf38de7f042d4f8abd146182f0", "urls": ["bzz-raw://d07ded7de8e48a25ac7b0442c6e455338bd16ee483a89ad7f37585ab91865a3b", "dweb:/ipfs/QmeBAuZgRJEXeuX6qsGt46sTLovKNC5Pue8xFxbHMPtiBR"], "license": "GPL-3.0-only"}, "lib/1inch/solidity-utils/contracts/libraries/AddressArray.sol": {"keccak256": "0x7895eaf7b55d9612b22ec586970488de51436c021b9f9414b3c27c3583c8856e", "urls": ["bzz-raw://e43897dfeff84e2983b017ab100d74061a6b9bed4618ec37a7cbb68bf064ac22", "dweb:/ipfs/Qmejoj4CuNr1giwGBDG7SgRtfYmi64fgy2DsGP2AAW9gH9"], "license": "MIT"}, "lib/1inch/solidity-utils/contracts/libraries/AddressSet.sol": {"keccak256": "0xbb8e2a541ac268f00f382c9fba9403b3ec5b58a48dc7236920d7c87817f93318", "urls": ["bzz-raw://614b60ff60f75c46aa41d00ec5dd78136d42e0d6035aa2331d16f26d2f5f5368", "dweb:/ipfs/QmUJWqcx2heKcmBhxpHfAnpKrwnevqtAwaQKT7Bmpke5NB"], "license": "MIT"}, "lib/1inch/token-plugins/contracts/ERC20Hooks.sol": {"keccak256": "0xd2657f278b2ed4667663344aa06c02b52b15862c69c215570de329aa1a4683a2", "urls": ["bzz-raw://4ffb90146369b7fa890489a0d3ecab5aa81c9f47b3ec7a1776fd8bc14eaa33d5", "dweb:/ipfs/QmWo24VHySo9jQBeXfE87Z3Hh576cNKcwccLrBAMsfax1c"], "license": "MIT"}, "lib/1inch/token-plugins/contracts/interfaces/IERC20Hooks.sol": {"keccak256": "0x2fb4fcbf91a7edf36e7ada3f578a8de1aee7ebdd12460648d3e09d4448351875", "urls": ["bzz-raw://d09a05543afcad3c0c2d2b9013647408cc44e1b17f362665ee20f9abed5964e8", "dweb:/ipfs/QmX3MMGsVJSfsrVoYY2jWK2BXd3i6scBhQQxytk461mizh"], "license": "MIT"}, "lib/1inch/token-plugins/contracts/interfaces/IHook.sol": {"keccak256": "0x474893cc48ee17530ad0e978ecbccaa726943615a5736260187a37b8e133ee80", "urls": ["bzz-raw://511ed52ec427cced459878f28453790817d4cbacc8601d77eb8dfa28e2e0b30d", "dweb:/ipfs/QmXvbB1bAZZanGepiUxWh3zZQUaHQZeYpR1UdaW4w5yKVS"], "license": "MIT"}, "lib/1inch/token-plugins/contracts/libs/ReentrancyGuard.sol": {"keccak256": "0xa88ccab1ee6b34a9007986ca15ea5fd5580864029130eb38333183af1bc4f66c", "urls": ["bzz-raw://b5ad18c862e59b0f24edc900519a55c7aee5537a42d039d6ea5e34df40267bb0", "dweb:/ipfs/QmVMRSjUEPxCno1sFdhWvpRqAqoqns8zwVyd7yCHYC6P8Z"], "license": "MIT"}, "lib/ExcessivelySafeCall/src/ExcessivelySafeCall.sol": {"keccak256": "0x7d9d432e8f02168bf3f790e3dabcf36402782acf7ffa476cabe86fc4d8962eb2", "urls": ["bzz-raw://1adc13e7f399f500ea5f81480ad149a50408fde7990a2c6347e6377486f389dc", "dweb:/ipfs/QmSvm5TUBJqknsqNJLLHqNS4MLSH5k3vNrbquVg6ZKSfx9"], "license": "MIT OR Apache-2.0"}, "lib/mangrove-core/lib/core/BitLib.sol": {"keccak256": "0x80f6885268986b9e976b424993aa875cf7aab8464403ed675a86ade9e9be5ee3", "urls": ["bzz-raw://5a31b4e1e0dc95de9a1dbb114c40c44814de5db3e2d857c93c2524a61454f6c8", "dweb:/ipfs/QmRkgE9ue5rGwE6XDnszF2e2meWqAC9nnKM97xKHjHphQr"], "license": "MIT"}, "lib/morpho-blue/src/libraries/MathLib.sol": {"keccak256": "0xa7354cbbcecef7bc0c94b61061c4e5da75515056b8e2db65e826b00d7369744a", "urls": ["bzz-raw://d7419c59bb906fcfa49320b68f265c3200090e5c30b194766256aee70b012e08", "dweb:/ipfs/Qmbo4uaW6XYnudya4bb6RU6riWXFk5M3CWJge5XzTTaEfd"], "license": "GPL-2.0-or-later"}, "lib/openzeppelin-contracts/contracts/access/AccessControl.sol": {"keccak256": "0xb64ecf1154f183412bcde47168f3af245e4120846346a0b3872c631e361156d2", "urls": ["bzz-raw://85331049a60659bc4489733ccd3cbeb177b65691122a8cb637bf9267ab25e23d", "dweb:/ipfs/QmbGpDcdwKTirzSCoZfE4rHG7jBSWsE4K2iSb6UCYXtLJv"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/access/IAccessControl.sol": {"keccak256": "0x5643a5cadd1278581308b20becb48a50946c159fc31c29fc407ea9a61fc865d1", "urls": ["bzz-raw://c7d79f305a239207a24fa2174897c8ea8ff1e81cb790d440fd54c89a0e85f63e", "dweb:/ipfs/QmT847eeAMnRN3DaG1zsKNMn7qipNAidqv1REnKexPkrfA"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/access/Ownable.sol": {"keccak256": "0xff6d0bb2e285473e5311d9d3caacb525ae3538a80758c10649a4d61029b017bb", "urls": ["bzz-raw://8ed324d3920bb545059d66ab97d43e43ee85fd3bd52e03e401f020afb0b120f6", "dweb:/ipfs/QmfEckWLmZkDDcoWrkEvMWhms66xwTLff9DDhegYpvHo1a"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/governance/utils/IVotes.sol": {"keccak256": "0xb541b133f2d85eb37ae866cb21123be93bd3671b6840c47f951da52d3a704548", "urls": ["bzz-raw://22443f43ece107eaf453aa9a41a59410cece5429c2779c794fbd2c8b5aa29d38", "dweb:/ipfs/Qmbum2jwLYuT9aZ2fr9NMLwWFsVavonrGm2VnbAL9hP2jn"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/governance/utils/Votes.sol": {"keccak256": "0x3f91c79d6f55db9e4fc36e1cfe6a483a7b0f5be60fecbd979555071673746d47", "urls": ["bzz-raw://9b1e3c64cbeb2757a2a1a45c69f7f3984a93b0eadd1016341b64f9d94f89d7c4", "dweb:/ipfs/QmP1Mj14U4vMTFa2rv2nodMbWSCov2ac9Md8W2aUcgYdKX"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/IERC1363.sol": {"keccak256": "0xc15298eb2b9ba5e18a8c9d12f93ad17a3e162a5c1d9b85f54c8adb5827b0d4da", "urls": ["bzz-raw://1f3c3d8f81d2daf1231890a6a2f897be365d6a479b53dcd52ec2527b5d3faf41", "dweb:/ipfs/QmeNdkd6u4at9pd2GAyyqxzrVGGvxfLpGmAKnFoYM5ya2e"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/IERC165.sol": {"keccak256": "0x81b022028c39007cce9920c394b9cddd1cb9f3a1c0398f254b4a6492df92ad2b", "urls": ["bzz-raw://e0b61b8a5c69b4df993c3d6f94c174ab293aa8698d149bce7be2d88f82929beb", "dweb:/ipfs/QmbtacmB1k8ginfrHvAJpjVeqnjYGfXYrkXmMPYEb83z4t"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/IERC20.sol": {"keccak256": "0xb5d81383d40f4006d1ce4bbad0064e7a930e17302cbe2a745e09cb403f042733", "urls": ["bzz-raw://3fc4a5681c2f00f41f49260a36ae6bbe1121dd93d470ea24d51d556eff2980be", "dweb:/ipfs/QmUBW6TwVWtGP96ka9TfuGivd27kH8CtkXD8RQAAecSFiR"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/IERC3156FlashBorrower.sol": {"keccak256": "0xad94c8d7246a50210f7bcb54e5b91fc9f1c6e137263ac972ca5dd0f7f6d4d49d", "urls": ["bzz-raw://6938e96fbb0cf3d961788b6c3522400e255d8057d1b9f7e08a50e0b48486b007", "dweb:/ipfs/QmNXG3MPzDXjHJ9iWDYCz4vi9RBTgVBnZjndnfBwMfhkyD"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/IERC3156FlashLender.sol": {"keccak256": "0xd92910b862581523ad4e9b99f0bf738f4e62700a5e305953c7fda7db2cfd0f73", "urls": ["bzz-raw://799f3f0f30822ac806800bbe7fe63b9991490c4e1d9edb75f5993d9350320819", "dweb:/ipfs/QmT8T4SokW6YxpDJQiafpeYNRGtjC5gFHxRqKTRXRyP6zB"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/IERC4626.sol": {"keccak256": "0x932fe72aeda629f70ee2ca902cfd8cce9faa0a13b39222c240979f42984c4541", "urls": ["bzz-raw://4e9e0a320185800eb9a6d57ef0e4ccf3bb32a6b5dd44882f7552617c30d4a05e", "dweb:/ipfs/QmYJVpT7DDPWx3DWro8vtM6Gqre2AyufsyCYoHm9cfQ1vr"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/IERC5267.sol": {"keccak256": "0xf29e9088951d8a2074d872a733674618fe5c164df21b8b5cf4a6295f523ba7ad", "urls": ["bzz-raw://562a1abc7ea505582827ce0c9a2f778360a1a8242742683af179930640020215", "dweb:/ipfs/QmPjx5f6KKaPfsDi1uV3ovQN9gHTAcNkMAFJZxE1Adw6VT"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/IERC5805.sol": {"keccak256": "0xc8960b7d3e504e98883de33856a917a473c05034cd61880df2a60b5c47c214fe", "urls": ["bzz-raw://80542373fa695b68d65b4d7222e852df5bda035a82e86ee559336c93b2bf7051", "dweb:/ipfs/QmZgH14DPTnKfA5gMSTMiUa6ExuqFfAozmEtLXiWc1iDiw"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/IERC6372.sol": {"keccak256": "0xa602c8beaae2d9e2ab1ce585a54547a6d4da32d32e4d002d20ccba55b19258d8", "urls": ["bzz-raw://ac6553b5b07788a0bb67cc53596837d795280233a9a5cb3a9b3e1fde56822f78", "dweb:/ipfs/QmVoHXoma4ZbPKVRJJRosvhipa4rtCMU9QQvWHWKiRUxvi"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/draft-IERC6093.sol": {"keccak256": "0x85cf779582f7860b00bba1d259be00e754bfbac3c2339e324d0113d682d9e9f9", "urls": ["bzz-raw://2ddf369affd106e2a9e2b8a22a6ce3da8a6ccda14c6ba5b8c87f6b08169e6318", "dweb:/ipfs/QmNadAttd47ycHShxhk33JUJhrbzmyZQ7mHs7WEyG4Qkmp"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/ERC20.sol": {"keccak256": "0x5aad1745bddba11752c3962464a3b12e0af079310cc22d1f43f0388ae1aaf8db", "urls": ["bzz-raw://577fad916bfdfe89aadf2685322fec7562cb0ed87722923085213cd9f85d7b79", "dweb:/ipfs/QmSM3J6PjrAUyEoNbdhq1ECZLXczKdCTzZTBUieKHsBYEL"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol": {"keccak256": "0xfe37358f223eddd65d61bb62b0b7bdb69d7101b5ec8d484292b8c1583a153b8a", "urls": ["bzz-raw://28dd43f30af3c12ae0fc08dd031b1250e906ef3c95f63f30fac6fd15aee2a662", "dweb:/ipfs/QmUkSyWsSRx36w1ti7U6qnGnQgJq16wpMhjeJrnyn9AXwG"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20Burnable.sol": {"keccak256": "0x2659248df25e34000ed214b3dc8da2160bc39874c992b477d9e2b1b3283dc073", "urls": ["bzz-raw://c345af1b0e7ea28d1216d6a04ab28f5534a5229b9edf9ca3cd0e84950ae58d26", "dweb:/ipfs/QmY63jtSrYpLRe8Gj1ep2vMDCKxGNNG3hnNVKBVnrs2nmA"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20FlashMint.sol": {"keccak256": "0x4d43ed4b9ff9e4c671274976d59a58dbcc7b69bd7ac11b1710f5b7607cf15b74", "urls": ["bzz-raw://0b47b42998f675cb6a51f2e74ef5906a6fa63ec6718f3fd56ee035d6f77143f9", "dweb:/ipfs/QmREnAXqPJBvAwfWfDzaFhNfSRWF4Jdy9ZrpHLw1KdQweY"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20Permit.sol": {"keccak256": "0x6485b101d7335f0fd25abc996c5e2fc965e72e5fbd0a7ad1a465bd3f012b5fd8", "urls": ["bzz-raw://1f8d296121044e697bcd34c4cd394cebd46fc30b04ce94fccff37200872e6834", "dweb:/ipfs/QmTNdmLdoHgMzoCDZ8Txk9aYvwtuyeJYHf5mjLWgzGTZAu"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20Votes.sol": {"keccak256": "0x62dc9346044aabf22d78541bd495aa6ca05a7f5100aed26196ba35d40b59fcb5", "urls": ["bzz-raw://5221df4501c74cd4493fee1a0f0788e02c4dc78c3c601e9f557f557c5a53ea92", "dweb:/ipfs/QmZpzyYY9dKLrgvYhXSHT93jwqb1UGvtGNMQk5dpECY5pa"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC4626.sol": {"keccak256": "0x0d99c706d010fa15de36e7e7b7a03dd0fdc9bcec52f9f812ef80ec7f3fc6fa63", "urls": ["bzz-raw://bee73c81a2964e8da0537de14082e60d64cd7b1cd9162adc04b58317e334c896", "dweb:/ipfs/QmbQ75T9PEJuiLk1kypX68rEBFtTaEzPWsy8Dv99buqVPH"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol": {"keccak256": "0xbaffa0bcc92adf28a53cc3b68551fc3632cb8f849a0028cb8d5c06e4677715e9", "urls": ["bzz-raw://32e6f8f6b2e883c85e6a602c0882d9962ce2f92406961244e86cd974df815912", "dweb:/ipfs/Qmahvx6fPpecicq1aUE1JihCxV5ep1bfuPukzrxa8Ub5PS"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Permit.sol": {"keccak256": "0x093f32ab700c2b05373387263915a75f5455cdb0f09a7630cc621e27b7b50d04", "urls": ["bzz-raw://d163e6ef21df143969df5557305e8c643a135c7660a678d0c65dca91772114a0", "dweb:/ipfs/QmTZUgiwEro5oLRhbJ2iSWyCqu1JTDekoFHALVUn4eHqYK"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol": {"keccak256": "0x1183b415155c1a7bf56d45edad5b17caf0da70935ac420698cbe8afb6750cbb2", "urls": ["bzz-raw://21d9edaeb3e5e8f93eb0fdab41530654e8169b1990b3bbfcf5e4527c52aa03f5", "dweb:/ipfs/QmWrqpNW3x5k3pTjvrT8XU1hauHnXTjqaPL2tfzMuWYosj"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Context.sol": {"keccak256": "0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2", "urls": ["bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12", "dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Nonces.sol": {"keccak256": "0x0082767004fca261c332e9ad100868327a863a88ef724e844857128845ab350f", "urls": ["bzz-raw://132dce9686a54e025eb5ba5d2e48208f847a1ec3e60a3e527766d7bf53fb7f9e", "dweb:/ipfs/QmXn1a2nUZMpu2z6S88UoTfMVtY2YNh86iGrzJDYmMkKeZ"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Panic.sol": {"keccak256": "0xf7fe324703a64fc51702311dc51562d5cb1497734f074e4f483bfb6717572d7a", "urls": ["bzz-raw://c6a5ff4f9fd8649b7ee20800b7fa387d3465bd77cf20c2d1068cd5c98e1ed57a", "dweb:/ipfs/QmVSaVJf9FXFhdYEYeCEfjMVHrxDh5qL4CGkxdMWpQCrqG"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Pausable.sol": {"keccak256": "0xdb484371dfbb848cb6f5d70464e9ac9b2900e4164ead76bbce4fef0b44bcc68f", "urls": ["bzz-raw://f9d6f6f6600a2bec622f699081b58350873b5e63ce05464d17d674a290bb8a7c", "dweb:/ipfs/QmQKVzSQY1PM3Bid4QhgVVZyx6B4Jx7XgaQzLKHj38vJz8"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/ShortStrings.sol": {"keccak256": "0x1fcf8cceb1a67e6c8512267e780933c4a3f63ef44756e6c818fda79be51c8402", "urls": ["bzz-raw://617d7d57f6f9cd449068b4d23daf485676d083aae648e038d05eb3a13291de35", "dweb:/ipfs/QmPADWPiGaSzZDFNpFEUx4ZPqhzPkYncBpHyTfAGcfsqzy"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol": {"keccak256": "0xcf74f855663ce2ae00ed8352666b7935f6cddea2932fdf2c3ecd30a9b1cd0e97", "urls": ["bzz-raw://9f660b1f351b757dfe01438e59888f31f33ded3afcf5cb5b0d9bf9aa6f320a8b", "dweb:/ipfs/QmarDJ5hZEgBtCmmrVzEZWjub9769eD686jmzb2XpSU1cM"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Strings.sol": {"keccak256": "0x1402d9ac66fbca0a2b282cd938f01f3cd5fb1e4c696ed28b37839401674aef52", "urls": ["bzz-raw://d3e6c46b6d1ea36bd73e0ac443a53504089167b98baa24923d702a865a38d211", "dweb:/ipfs/QmdutUpr5KktmvgtqG2v96Bo8nVKLJ3PgPedxbsRD42CuQ"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/cryptography/ECDSA.sol": {"keccak256": "0x6c29257484c0595ca5af8844fafe99cc5eace7447c9f5bced71d6b3a19a6a2a5", "urls": ["bzz-raw://cce7ac0bdb05f73c0918e362dea2e52426e00ddf0a1018f14accdcf78c6eb6e4", "dweb:/ipfs/QmbkNq5dDxww27FzFFiKgW3S7C5VoZpjdZGpSCtsb9hP32"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/cryptography/EIP712.sol": {"keccak256": "0xda8013da608bda3c9eaa9e59053d38d7888e64bb40aa557e5929cd702f8de87e", "urls": ["bzz-raw://3ea13234c6b00ae79dc1a98e7e7f2faf38d37e76a687ccd0c95ad84b03ea570f", "dweb:/ipfs/QmWtdefDm5jiEzAjmfPMZ5B1NKVxFoMiD5ZoD68hcNTHun"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/cryptography/MessageHashUtils.sol": {"keccak256": "0x26670fef37d4adf55570ba78815eec5f31cb017e708f61886add4fc4da665631", "urls": ["bzz-raw://b16d45febff462bafd8a5669f904796a835baf607df58a8461916d3bf4f08c59", "dweb:/ipfs/QmU2eJFpjmT4vxeJWJyLeQb8Xht1kdB8Y6MKLDPFA9WPux"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/introspection/ERC165.sol": {"keccak256": "0x41ddfafe0d00dc22e35119d41cb0ca93673960689d35710fd12875139e64bd9f", "urls": ["bzz-raw://49d90142e15cdc4ca00de16e1882fa0a0daad8b46403628beb90c67a3efe4fc4", "dweb:/ipfs/QmNizYnFNcGixHxsknEccr2cQWyyQBqFF7h2bXLmefQz6M"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/introspection/IERC165.sol": {"keccak256": "0x4ee0e04cc52827588793a141d5efb9830f179a17e80867cc332b3a30ceb30fd9", "urls": ["bzz-raw://17d8f47fce493b34099ed9005c5aee3012488f063cfe1c34ed8f9e6fc3d576e5", "dweb:/ipfs/QmZco2GbZZhEMvG3BovyoGMAFKvfi2LhfNGQLn283LPrXf"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/math/Math.sol": {"keccak256": "0x1225214420c83ebcca88f2ae2b50f053aaa7df7bd684c3e878d334627f2edfc6", "urls": ["bzz-raw://6c5fab4970634f9ab9a620983dc1c8a30153981a0b1a521666e269d0a11399d3", "dweb:/ipfs/QmVRnBC575MESGkEHndjujtR7qub2FzU9RWy9eKLp4hPZB"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/math/SafeCast.sol": {"keccak256": "0x195533c86d0ef72bcc06456a4f66a9b941f38eb403739b00f21fd7c1abd1ae54", "urls": ["bzz-raw://b1d578337048cad08c1c03041cca5978eff5428aa130c781b271ad9e5566e1f8", "dweb:/ipfs/QmPFKL2r9CBsMwmUqqdcFPfHZB2qcs9g1HDrPxzWSxomvy"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/math/SignedMath.sol": {"keccak256": "0xb1970fac7b64e6c09611e6691791e848d5e3fe410fa5899e7df2e0afd77a99e3", "urls": ["bzz-raw://db5fbb3dddd8b7047465b62575d96231ba8a2774d37fb4737fbf23340fabbb03", "dweb:/ipfs/QmVUSvooZKEdEdap619tcJjTLcAuH6QBdZqAzWwnAXZAWJ"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/structs/Checkpoints.sol": {"keccak256": "0x743aa2d21f6c26885e0aa6a1c84f7f7bc58fbd6df6bab32bed23f1a41f50454a", "urls": ["bzz-raw://a651d38b4261840d3744e571edf2b59455352a8c7dac5d35b019afefa343ea3b", "dweb:/ipfs/QmSy3UkTCQDYTjKtGwtqPRrXaofcqtVZxaF6j1dV44wqvr"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/types/Time.sol": {"keccak256": "0x36776530f012618bc7526ceb28e77b85e582cb12d9b9466a71d4bd6bf952e4cc", "urls": ["bzz-raw://9f867d046908497287d8a67643dd5d7e38c4027af4ab0a74ffbe1d6790c383c6", "dweb:/ipfs/QmQ7s9gMP1nkwThFmoDifnGgpUMsMe5q5ZrAxGDsNnRGza"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/Base.sol": {"keccak256": "0x4ff1a785311017d1eedb1b4737956fa383067ad34eb439abfec1d989754dde1c", "urls": ["bzz-raw://f553622969b9fdb930246704a4c10dfaee6b1a4468c142fa7eb9dc292a438224", "dweb:/ipfs/QmcxqHnqdQsMVtgsfH9VNLmZ3g7GhgNagfq7yvNCDcCHFK"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/StdAssertions.sol": {"keccak256": "0x4584f551c3a875c26423c1e50a77179bc2f9c9c1ee6d0f0c6be0f89ca5ee8270", "urls": ["bzz-raw://ef118876a862b60ba8a6612b8b3f3a6c226e11a0071a2e9695f021586d1bfdbe", "dweb:/ipfs/QmXd2vD91PkvoFWasFkgbDi89PctmgsHVxGvGNyhjmRK2b"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/StdChains.sol": {"keccak256": "0xcd3e64ec9ffa19a2c0715bbdaf7ddf28887cc418e079bec4373fd6a3f9961a7b", "urls": ["bzz-raw://e981a2ab738590928e9efa5f3d95a408c718eb12d73a113d7675f3ed55a026a1", "dweb:/ipfs/QmTgSEkWWsBRy32goRCaUkraSgpZHtgbZoKC3iEFNz5RDc"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/StdCheats.sol": {"keccak256": "0x0fa6ec03602648b62cce41aab2096e6b7e052f2846075d967b6958dd586db746", "urls": ["bzz-raw://cd84e2ca9c1eaed6b76768cc12bb8c1af8289170ea8b7706f58d516460d79c41", "dweb:/ipfs/QmQ7BK7co6DE4eWUqMyv11s5eHYkS1tyx8tDSZGZVtf2aK"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/StdError.sol": {"keccak256": "0xbf477b11a42d7611696956546bcfaa29317d1166bf65e402344599c05943fc77", "urls": ["bzz-raw://bc2e117d1135e030862b96a6526a43feb38d396cc79857f1fb696d4eff0e5fd6", "dweb:/ipfs/QmdSuQ5RrQudTLsNmWXGEeVJX8gR5U9XPm6m4dwwuQnJrj"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/StdInvariant.sol": {"keccak256": "0x4dab3021edfa9511bbdd80c48f060ef62eaf457f99eaf841f561fc2557d9a08d", "urls": ["bzz-raw://07668628673174cf8f27f8f4e1f862bab775013ec247eb34f698c5005f229391", "dweb:/ipfs/QmPJsiiYwmCZXMsHmQv5tg8VF3CAhNdat7WnKLTWZZH2v5"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/StdJson.sol": {"keccak256": "0xbc0132abe1c2accc2867c0f03667afffdf92f3e95a581bb03c9557eaa38ea500", "urls": ["bzz-raw://eb6fab37dc73c219cfbb7b4f4998bcf7677ca5397a867e850f40232192073974", "dweb:/ipfs/QmUHsbVdp9SKmgek7ZfPcLTKrpZFXpqaqt4sVejzxGEQL3"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/StdMath.sol": {"keccak256": "0xd90ad4fd8aeaeb8929964e686e769fdedd5eded3fc3815df194a0ab9f91a3fb2", "urls": ["bzz-raw://7919b70f636c7b805223992f28ad1ad0145d6c1385b5931a3589aface5fe6c92", "dweb:/ipfs/QmY7FRaULwoGgFteF8GawjQJRfasNgpWnU2aiMsFrYpuTC"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/StdStorage.sol": {"keccak256": "0xb91ab24383a5872b894fc93325eef1add6cbbf981628f18e860068bf88bb7dcc", "urls": ["bzz-raw://2651e33b9ac8fc5803ed0a43078c1bf4fa90a0b0347aafd95776b02daccdcc57", "dweb:/ipfs/QmbdRyPuGJdZgnCuMnp7c1WsBo1Spf7j1KMmws1Z5rd4BQ"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/StdStyle.sol": {"keccak256": "0x43e2a8a9b9c2574dabe74f11adf6f782df218f463540e3b5b563609fe108597d", "urls": ["bzz-raw://51363ca97404cf4128e1141428949768c31929e75e014b02c85e887fbbb4f1b8", "dweb:/ipfs/QmVhtbQc2fU4rRmbcfBtz34mAgG4BAZBsbna1Ca4SkoPsK"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/StdToml.sol": {"keccak256": "0x58a72c765ed3f7ff6b105509689658795b8a3739b8931772a497155878381861", "urls": ["bzz-raw://b4a3746f4fabaeb980bd77d9e091d3904ee38a6c0e191bfa8ba6874c6f8558a3", "dweb:/ipfs/QmUfFDMEn461FgGEXt5HicyGD54sc28sLaQ9JRWDMBKed8"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/StdUtils.sol": {"keccak256": "0x7274081e11c05164fd8eadde4de8305c033e58a43008dea58065f3170ccf0737", "urls": ["bzz-raw://e9801614b6c9d3e472982e6cc68f5f1ad03682f84eafb686be65633c7132d138", "dweb:/ipfs/QmcQSUcrm2A7XuektnxJjvYGmZtBeA6LKPxCXRqdXBX776"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/Test.sol": {"keccak256": "0x3b4bb409a156dee9ce261458117fe9f81080ca844a8a26c07c857c46d155effe", "urls": ["bzz-raw://5792c69fe24bdc063a14e08fe68275007fdb1e5e7e343840a77938cb7e95a64e", "dweb:/ipfs/QmcAMhaurUwzhytJFYix4vRNeZeV8g27b8LnV3t7dvYtiK"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/Vm.sol": {"keccak256": "0x44bfadcf5a89b8058f80258f2259585c740f9cc45669a0579f4f2753ff2c6354", "urls": ["bzz-raw://bbc366c8b3499d5030e3b2e45bac23770531f2f5243a0e80e3d5a66b6f9a312c", "dweb:/ipfs/QmNxDEB3BaVnKzNaWedtdMshhvCEddB1AsdJZcsQx6jdtC"], "license": "MIT OR Apache-2.0"}, "lib/openzeppelin-contracts/lib/forge-std/src/console.sol": {"keccak256": "0x4bbf47eb762cef93729d6ef15e78789957147039b113e5d4df48e3d3fd16d0f5", "urls": ["bzz-raw://af9e3a7c3d82fb5b10b57ca4d1a82f2acbef80c077f6f6ef0cc0187c7bfd9f57", "dweb:/ipfs/QmR9VzmnBDJpgiDP6CHT6truehukF9HpYvuP6kRiJbDwPP"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/console2.sol": {"keccak256": "0x3b8fe79f48f065a4e4d35362171304a33784c3a90febae5f2787805a438de12f", "urls": ["bzz-raw://61de63af08803549299e68b6e6e88d40f3c5afac450e4ee0a228c66a61ba003d", "dweb:/ipfs/QmWVoQ5rrVxnczD4ZZoPbD4PC9Z3uExJtzjD4awTqd14MZ"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/interfaces/IMulticall3.sol": {"keccak256": "0x7aac1389150499a922d1f9ef5749c908cef127cb2075b92fa17e9cb611263d0a", "urls": ["bzz-raw://d95ebb7c7c463e08ebc12dab639945752fb2480acfc6e86da32f72732a7fd0c0", "dweb:/ipfs/QmNXK8P8oPWwajsQHvAHw3JPyQidPLCGQN3hWu1Lk6PBL2"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/safeconsole.sol": {"keccak256": "0xbef9786cb49d3eade757bad87568c49c8c8f35721f0193c95ffb055d9e466e11", "urls": ["bzz-raw://3bafd2b0b2d28068d329f95ea8a1fbce3719c257fcb863fc01abcbafd8d531ab", "dweb:/ipfs/QmUeaFjKWTVDBsHVfSob4mwt6A5hTnKDz22HaUXeZhypa3"], "license": "MIT"}, "test/InterestTests/InterestFixture.sol": {"keccak256": "0x458f1f72b1417a73ecdea81c25b269592e95c1808ca6aaa6b60a25243e143ed3", "urls": ["bzz-raw://abeb9b791b75f44d48f898182c673d80ea1c0f513fffe48a6834fdebebc6fdbe", "dweb:/ipfs/QmU92joERfyZJaTonAknmtRBkTjs5Jb7S2zM8Zk1XAnhwj"], "license": "GPL-3.0-only"}, "test/example/PeripheralDelegationContractExample.sol": {"keccak256": "0xf212fd0b2dd3a358c826623bd320e3aa0630892b9f6ba777b8126d3e2cfcfb14", "urls": ["bzz-raw://2ad79b2d7eb1b46c69383b9e9090bf2031ff779e46637d850b881db3dc84d797", "dweb:/ipfs/QmTPx8qw1zdYXRzjBnmuzMCt8yiErwFiLBk47xnbTm1erP"], "license": "GPL-3.0-only"}, "test/shared/FactoryPairTestFixture.sol": {"keccak256": "0x62487d7b3402461a61bd0c99be82302c8c1a94533c525a0ff6bcfe888af730a5", "urls": ["bzz-raw://dfd509aaec3469ed23d1cda8c6f603b7f0163fc29ec4ba6e4b06b60ca8fdc042", "dweb:/ipfs/QmTPDPM7kt77VNWr61MVZGmNZp67RG8jKzdmz7zwWep4GE"], "license": "GPL-3.0-only"}, "test/shared/StubErc20.sol": {"keccak256": "0xf3508dc98ae444d142d9993c52cebd856aba40c3e53d64bfeb63e71d190b12ee", "urls": ["bzz-raw://0cc01d254b6d5569d1cb426250db9df1b01afde9dd7b52e1efa0691112fcd918", "dweb:/ipfs/QmPnL9wFpSKXprrEFS9kkC2WzK2kAgWSH1snom1wiorCxn"], "license": "MIT"}, "test/shared/utilities.sol": {"keccak256": "0xc64b147bbe73bf59fdec4202c5b7c5dbcadd7550f4b2ea2390ea689e194d7cb8", "urls": ["bzz-raw://ab03a14b75d4b9df7795eeefd7e6d4a1d7af7b58ce948741cdd5d056a2c30416", "dweb:/ipfs/QmShemddxGaLyTGtC3yLdMtdHf9Gj3H8rjf2umzbFmP6aG"], "license": "GPL-3.0-only"}, "test/utils/DepletedAssetUtils.sol": {"keccak256": "0x2273187d5eb782fb341d44265bd6e8afcef18ab3cfabcb4a0b77a75f15298c42", "urls": ["bzz-raw://04d0a060b217603f6c7e38efc49be20932f08c56e46b1f9cea54eb722595458e", "dweb:/ipfs/QmdJHgaFbbtGDHPpHEFucTvrj4p4LT1piMPjtbrWBMXzAR"], "license": "GPL-3.0-only"}, "test/utils/constants.sol": {"keccak256": "0xe7d13ea4f26a2c43b7beed68c83a0e36555ead8f6bfd181430c74f853546fc34", "urls": ["bzz-raw://5098f47b615afa3d6489c2c8c2576f6202601fb15b1f32e6900639986e44f1fd", "dweb:/ipfs/QmPU1Ejtv4yY7eqjW1SpVgvS8vMqwyEjMeNGCLax3Mwk9d"], "license": "GPL-3.0-only"}}, "version": 1}, "id": 177}