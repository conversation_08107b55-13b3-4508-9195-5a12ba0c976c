{"abi": [{"type": "constructor", "inputs": [{"name": "midTermIntervalConfig", "type": "uint24", "internalType": "uint24"}, {"name": "longTermIntervalConfigFactor", "type": "uint24", "internalType": "uint24"}, {"name": "firstTick", "type": "int16", "internalType": "int16"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "IS_TEST", "inputs": [], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "changeInterval", "inputs": [{"name": "longTermIntervalConfig", "type": "uint24", "internalType": "uint24"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "excludeArtifacts", "inputs": [], "outputs": [{"name": "excludedArtifacts_", "type": "string[]", "internalType": "string[]"}], "stateMutability": "view"}, {"type": "function", "name": "excludeContracts", "inputs": [], "outputs": [{"name": "excludedContracts_", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "excludeSelectors", "inputs": [], "outputs": [{"name": "excludedSelectors_", "type": "tuple[]", "internalType": "struct StdInvariant.FuzzSelector[]", "components": [{"name": "addr", "type": "address", "internalType": "address"}, {"name": "selectors", "type": "bytes4[]", "internalType": "bytes4[]"}]}], "stateMutability": "view"}, {"type": "function", "name": "excludeSenders", "inputs": [], "outputs": [{"name": "excludedSenders_", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "failed", "inputs": [], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "generateObservations", "inputs": [{"name": "blocks", "type": "uint256", "internalType": "uint256"}, {"name": "tick", "type": "int16", "internalType": "int16"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "generatePastBlocks", "inputs": [{"name": "blocks", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "getLendingStateTickAndCheckpoint", "inputs": [{"name": "timeElapsedSinceUpdate", "type": "uint32", "internalType": "uint32"}, {"name": "timeElapsedSinceLendingUpdate", "type": "uint32", "internalType": "uint32"}], "outputs": [{"name": "tick", "type": "int16", "internalType": "int16"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "getObservationStruct", "inputs": [], "outputs": [{"name": "", "type": "tuple", "internalType": "struct GeometricTWAP.Observations", "components": [{"name": "isMidTermBufferInitialized", "type": "bool", "internalType": "bool"}, {"name": "isLongTermBufferInitialized", "type": "bool", "internalType": "bool"}, {"name": "midTermIndex", "type": "uint8", "internalType": "uint8"}, {"name": "longTermIndex", "type": "uint8", "internalType": "uint8"}, {"name": "lastTick", "type": "int16", "internalType": "int16"}, {"name": "lastLendingStateTick", "type": "int16", "internalType": "int16"}, {"name": "midTermIntervalConfig", "type": "uint24", "internalType": "uint24"}, {"name": "longTermIntervalConfig", "type": "uint24", "internalType": "uint24"}, {"name": "lendingCumulativeSum", "type": "int56", "internalType": "int56"}, {"name": "midTermCumulativeSum", "type": "int56[51]", "internalType": "int56[51]"}, {"name": "longTermCumulativeSum", "type": "int56[9]", "internalType": "int56[9]"}, {"name": "midTermTimeInterval", "type": "uint32[51]", "internalType": "uint32[51]"}, {"name": "longTermTimeInterval", "type": "uint32[9]", "internalType": "uint32[9]"}]}], "stateMutability": "view"}, {"type": "function", "name": "getObservedTicks", "inputs": [{"name": "isLongTermBufferInitialized", "type": "bool", "internalType": "bool"}], "outputs": [{"name": "longTermTick", "type": "int16", "internalType": "int16"}, {"name": "midTermTick", "type": "int16", "internalType": "int16"}, {"name": "lastTick", "type": "int16", "internalType": "int16"}], "stateMutability": "view"}, {"type": "function", "name": "getTicks", "inputs": [{"name": "currentTick", "type": "int16", "internalType": "int16"}], "outputs": [{"name": "minTick", "type": "int16", "internalType": "int16"}, {"name": "maxTick", "type": "int16", "internalType": "int16"}, {"name": "lastTick", "type": "int16", "internalType": "int16"}], "stateMutability": "view"}, {"type": "function", "name": "intMax", "inputs": [{"name": "a", "type": "int16", "internalType": "int16"}, {"name": "b", "type": "int16", "internalType": "int16"}], "outputs": [{"name": "", "type": "int16", "internalType": "int16"}], "stateMutability": "pure"}, {"type": "function", "name": "intMin", "inputs": [{"name": "a", "type": "int16", "internalType": "int16"}, {"name": "b", "type": "int16", "internalType": "int16"}], "outputs": [{"name": "", "type": "int16", "internalType": "int16"}], "stateMutability": "pure"}, {"type": "function", "name": "mineBlock", "inputs": [{"name": "blockStep", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "obs", "inputs": [], "outputs": [{"name": "isMidTermBufferInitialized", "type": "bool", "internalType": "bool"}, {"name": "isLongTermBufferInitialized", "type": "bool", "internalType": "bool"}, {"name": "midTermIndex", "type": "uint8", "internalType": "uint8"}, {"name": "longTermIndex", "type": "uint8", "internalType": "uint8"}, {"name": "lastTick", "type": "int16", "internalType": "int16"}, {"name": "lastLendingStateTick", "type": "int16", "internalType": "int16"}, {"name": "midTermIntervalConfig", "type": "uint24", "internalType": "uint24"}, {"name": "longTermIntervalConfig", "type": "uint24", "internalType": "uint24"}, {"name": "lendingCumulativeSum", "type": "int56", "internalType": "int56"}], "stateMutability": "view"}, {"type": "function", "name": "targetArtifactSelectors", "inputs": [], "outputs": [{"name": "targetedArtifactSelectors_", "type": "tuple[]", "internalType": "struct StdInvariant.FuzzArtifactSelector[]", "components": [{"name": "artifact", "type": "string", "internalType": "string"}, {"name": "selectors", "type": "bytes4[]", "internalType": "bytes4[]"}]}], "stateMutability": "view"}, {"type": "function", "name": "targetArtifacts", "inputs": [], "outputs": [{"name": "targetedArtifacts_", "type": "string[]", "internalType": "string[]"}], "stateMutability": "view"}, {"type": "function", "name": "targetContracts", "inputs": [], "outputs": [{"name": "targetedContracts_", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "targetInterfaces", "inputs": [], "outputs": [{"name": "targetedInterfaces_", "type": "tuple[]", "internalType": "struct StdInvariant.FuzzInterface[]", "components": [{"name": "addr", "type": "address", "internalType": "address"}, {"name": "artifacts", "type": "string[]", "internalType": "string[]"}]}], "stateMutability": "view"}, {"type": "function", "name": "targetSelectors", "inputs": [], "outputs": [{"name": "targetedSelectors_", "type": "tuple[]", "internalType": "struct StdInvariant.FuzzSelector[]", "components": [{"name": "addr", "type": "address", "internalType": "address"}, {"name": "selectors", "type": "bytes4[]", "internalType": "bytes4[]"}]}], "stateMutability": "view"}, {"type": "function", "name": "targetSenders", "inputs": [], "outputs": [{"name": "targetedSenders_", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "timestamp", "inputs": [], "outputs": [{"name": "", "type": "uint32", "internalType": "uint32"}], "stateMutability": "view"}, {"type": "function", "name": "verifyIndexes", "inputs": [{"name": "duration", "type": "uint256", "internalType": "uint256"}, {"name": "interval", "type": "uint24", "internalType": "uint24"}], "outputs": [], "stateMutability": "view"}, {"type": "function", "name": "verifyTicks", "inputs": [{"name": "longTermTick", "type": "int16", "internalType": "int16"}, {"name": "midTermTick", "type": "int16", "internalType": "int16"}, {"name": "_blockTick", "type": "int16", "internalType": "int16"}, {"name": "_currentTick", "type": "int16", "internalType": "int16"}], "outputs": [], "stateMutability": "view"}, {"type": "function", "name": "verifyTicks", "inputs": [{"name": "tick", "type": "int16", "internalType": "int16"}], "outputs": [], "stateMutability": "view"}, {"type": "event", "name": "UpdateLendingTick", "inputs": [{"name": "lendingStateTick", "type": "int16", "indexed": false, "internalType": "int16"}], "anonymous": false}, {"type": "event", "name": "log", "inputs": [{"name": "", "type": "string", "indexed": false, "internalType": "string"}], "anonymous": false}, {"type": "event", "name": "log_address", "inputs": [{"name": "", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "log_array", "inputs": [{"name": "val", "type": "uint256[]", "indexed": false, "internalType": "uint256[]"}], "anonymous": false}, {"type": "event", "name": "log_array", "inputs": [{"name": "val", "type": "int256[]", "indexed": false, "internalType": "int256[]"}], "anonymous": false}, {"type": "event", "name": "log_array", "inputs": [{"name": "val", "type": "address[]", "indexed": false, "internalType": "address[]"}], "anonymous": false}, {"type": "event", "name": "log_bytes", "inputs": [{"name": "", "type": "bytes", "indexed": false, "internalType": "bytes"}], "anonymous": false}, {"type": "event", "name": "log_bytes32", "inputs": [{"name": "", "type": "bytes32", "indexed": false, "internalType": "bytes32"}], "anonymous": false}, {"type": "event", "name": "log_int", "inputs": [{"name": "", "type": "int256", "indexed": false, "internalType": "int256"}], "anonymous": false}, {"type": "event", "name": "log_named_address", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "log_named_array", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "uint256[]", "indexed": false, "internalType": "uint256[]"}], "anonymous": false}, {"type": "event", "name": "log_named_array", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "int256[]", "indexed": false, "internalType": "int256[]"}], "anonymous": false}, {"type": "event", "name": "log_named_array", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "address[]", "indexed": false, "internalType": "address[]"}], "anonymous": false}, {"type": "event", "name": "log_named_bytes", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "bytes", "indexed": false, "internalType": "bytes"}], "anonymous": false}, {"type": "event", "name": "log_named_bytes32", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "bytes32", "indexed": false, "internalType": "bytes32"}], "anonymous": false}, {"type": "event", "name": "log_named_decimal_int", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "int256", "indexed": false, "internalType": "int256"}, {"name": "decimals", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "log_named_decimal_uint", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "decimals", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "log_named_int", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "int256", "indexed": false, "internalType": "int256"}], "anonymous": false}, {"type": "event", "name": "log_named_string", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "string", "indexed": false, "internalType": "string"}], "anonymous": false}, {"type": "event", "name": "log_named_uint", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "log_string", "inputs": [{"name": "", "type": "string", "indexed": false, "internalType": "string"}], "anonymous": false}, {"type": "event", "name": "log_uint", "inputs": [{"name": "", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "logs", "inputs": [{"name": "", "type": "bytes", "indexed": false, "internalType": "bytes"}], "anonymous": false}, {"type": "error", "name": "InvalidIntervalConfig", "inputs": []}, {"type": "error", "name": "SafeCastOverflowedIntDowncast", "inputs": [{"name": "bits", "type": "uint8", "internalType": "uint8"}, {"name": "value", "type": "int256", "internalType": "int256"}]}], "bytecode": {"object": "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", "sourceMap": "576:4468:178:-:0;;;3126:44:97;;;3166:4;-1:-1:-1;;3126:44:97;;;;;;;;1016:26:107;;;;;;;;;;;754:521:178;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;1007:3;:53;;-1:-1:-1;;;;1007:53:178;;;;;1070:84;;1102:21;1125:28;1070:31;:84::i;:::-;1164:47;:3;1201:9;1164:36;:47::i;:::-;-1:-1:-1;;1221:9:178;:47;;-1:-1:-1;;1221:47:178;26839:21:21;:15;:21;1221:47:178;;;-1:-1:-1;576:4468:178;;4643:571:21;4815:54;;-1:-1:-1;;;;4815:54:21;;;;;4897:25;;;;;:125;;-1:-1:-1;4966:56:21;;;;1418:2;4966:56;:::i;:::-;4942:21;:80;;;4897:125;4880:208;;;5054:23;;-1:-1:-1;;;5054:23:21;;;;;;;;;;;4880:208;5097:49;;-1:-1:-1;;;;5156:51:21;5097:49;;;;;;-1:-1:-1;;;;5156:51:21;;;;;;;;;;;4643:571::o;5220:277::-;5398:47;5417:4;5423:9;5434:1;;5440:4;5398:18;:47::i;:::-;5455:35;5471:4;5477:9;5488:1;5455:15;:35::i;:::-;5220:277;;:::o;21208:1913::-;26839:21;:15;:21;21571;21522:25;;;21548:19;21522:46;;;;;;;:::i;:::-;;;;;;;;;;;;:70;;;;;;;;;;;;;;;;;;;;21650:16;21602:4;:24;;21627:19;21602:45;;;;;;;:::i;:::-;;;;;;;;;;;;:64;;;;;;;;;;;;;;;;;;21724:17;21764:56;21777:19;637:2;21764:12;;;:56;;:::i;:::-;21744:76;;;;;;;;;-1:-1:-1;;21744:76:21;;;;;;;;-1:-1:-1;21836:31:21;;;;;21835:32;:50;;;;-1:-1:-1;21871:14:21;;21835:50;21831:119;;;21901:38;;-1:-1:-1;;21901:38:21;21935:4;21901:38;;;21831:119;22067:27;;;;;;;;22036:28;;22216:25;;;;22242:60;;22255:18;;;;;901:26;22242:12;:60::i;:::-;22216:87;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;22177:16;:126;22166:137;;22347:21;22335:33;;:8;:33;;;;:48;;;;22372:11;22335:48;22331:689;;;22434:18;;;;;;;22578:21;22527:26;;;22434:18;22527:48;;;;;;;:::i;:::-;;;;;;;;;;;;:72;;;;;;;;;;;;;;;;;;;;22667:16;22617:4;:25;;22643:20;22617:47;;;;;;;:::i;:::-;;;;;;;;;;;;:66;;;;;;;;;;;;;;;;;;22792:58;22805:20;696:1;22792:12;;;:58;;:::i;:::-;22771:79;;-1:-1:-1;;22771:79:21;;;;;;;;;;;;;;;;;;-1:-1:-1;22771:79:21;22874:32;;;22873:33;:51;;;;-1:-1:-1;22910:14:21;;22873:51;22869:137;;;22948:39;;-1:-1:-1;;22948:39:21;;;;;22869:137;22385:635;22331:689;-1:-1:-1;;23091:23:21;;;;;;;;-1:-1:-1;;;;23091:23:21;;;;;;;;;;-1:-1:-1;;;;;21208:1913:21:o;20408:282::-;20530:44;;-1:-1:-1;;;;;;;;20584:48:21;20530:44;;;;;-1:-1:-1;;;;20584:48:21;;-1:-1:-1;;;20584:48:21;;;;;;;20648:35;;-1:-1:-1;1326:21:190;;;1308:40;;20648:35:21;;1296:2:190;1281:18;20648:35:21;;;;;;;20408:282;;;:::o;16399:191::-;16486:5;16561:11;16541:12;16556:1;16541:16;16540:32;;;;;:::i;:::-;;16527:46;;16399:191;;;;;:::o;16214:179::-;16292:7;16342:10;;:34;;16375:1;16367:5;:9;16342:34;;;16355:9;16342:34;16335:41;16214:179;-1:-1:-1;;;16214:179:21:o;14:165:190:-;92:13;;145:8;134:20;;124:31;;114:59;;169:1;166;159:12;114:59;14:165;;;:::o;184:443::-;268:6;276;284;337:2;325:9;316:7;312:23;308:32;305:52;;;353:1;350;343:12;305:52;376:39;405:9;376:39;:::i;:::-;366:49;;434:48;478:2;467:9;463:18;434:48;:::i;:::-;424:58;;525:2;514:9;510:18;504:25;572:5;569:1;558:20;551:5;548:31;538:59;;593:1;590;583:12;538:59;616:5;606:15;;;184:443;;;;;:::o;632:265::-;705:9;;;736;;753:15;;;747:22;;733:37;723:168;;813:10;808:3;804:20;801:1;794:31;848:4;845:1;838:15;876:4;873:1;866:15;902:127;963:10;958:3;954:20;951:1;944:31;994:4;991:1;984:15;1018:4;1015:1;1008:15;1034:127;1095:10;1090:3;1086:20;1083:1;1076:31;1126:4;1123:1;1116:15;1150:4;1147:1;1140:15;1166:188;576:4468:178;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "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", "sourceMap": "576:4468:178:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;681:37;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;681:37:178;;;;;;;-1:-1:-1;;;681:37:178;;;;;;;-1:-1:-1;;;681:37:178;;;;;-1:-1:-1;;;681:37:178;;;;;;-1:-1:-1;;;681:37:178;;;;;;;;;;839:14:190;;832:22;814:41;;898:14;;891:22;886:2;871:18;;864:50;962:4;950:17;;;930:18;;;923:45;;;;1004:17;;;;999:2;984:18;;977:45;1070:1;1059:21;;;1053:3;1038:19;;1031:50;1118:21;;1112:3;1097:19;;1090:50;1189:8;1177:21;;;1171:3;1156:19;;1149:50;1236:21;1230:3;1215:19;;1208:50;1306:1;1295:21;1289:3;1274:19;;1267:50;801:3;786:19;681:37:178;;;;;;;;1622:276;;;;;;:::i;:::-;;:::i;:::-;;;1928:1:190;1917:21;;;;1899:40;;1887:2;1872:18;1622:276:178;1757:188:190;4672:160:178;;;;;;:::i;:::-;;:::i;:::-;;2277:256;;;;;;:::i;:::-;;:::i;:::-;;;;2678:1:190;2667:21;;;2649:40;;2725:21;;;2720:2;2705:18;;2698:49;2783:21;;2763:18;;;2756:49;;;;2637:2;2622:18;2277:256:178;2459:352:190;2907:134:100;;;:::i;:::-;;;;;;;:::i;3823:151::-;;;:::i;:::-;;;;;;;:::i;2660:146:178:-;;;;;;:::i;:::-;;:::i;:::-;;;5522:25:190;;;5510:2;5495:18;2660:146:178;5376:177:190;3241:777:178;;;;;;:::i;:::-;;:::i;3684:133:100:-;;;:::i;3385:141::-;;;:::i;3193:186::-;;;:::i;:::-;;;;;;;:::i;1281:335:178:-;;;;;;:::i;:::-;;:::i;3047:140:100:-;;;:::i;:::-;;;;;;;:::i;2053:218:178:-;;;;;;:::i;:::-;;:::i;3532:146:100:-;;;:::i;:::-;;;;;;;:::i;1904:143:178:-;;;;;;:::i;:::-;;:::i;2754:147:100:-;;;:::i;2459:141::-;;;:::i;724:23:178:-;;;;;;;;;;;;10062:10:190;10050:23;;;10032:42;;10020:2;10005:18;724:23:178;9888:192:190;1243:204:96;;;:::i;:::-;;;10250:14:190;;10243:22;10225:41;;10213:2;10198:18;1243:204:96;10085:187:190;4943:99:178;;;;;;:::i;:::-;;:::i;4838:::-;;;;;;:::i;:::-;;:::i;2606:142:100:-;;;:::i;2539:115:178:-;;;:::i;:::-;;;;;;;:::i;3124:111::-;;;;;;:::i;:::-;;:::i;2812:306::-;;;;;;:::i;:::-;;:::i;1016:26:107:-;;;;;;;;;1622:276:178;1771:10;1800:91;:3;1837:22;1861:29;1800:36;:91::i;:::-;1793:98;;1622:276;;;;;:::o;4672:160::-;4741:7;;4749:24;4764:9;4749:12;:24;:::i;:::-;4741:33;;;;;;;;;;;;;5522:25:190;;5510:2;5495:18;;5376:177;4741:33:178;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;4784:7:178;;-1:-1:-1;4784:7:178;;-1:-1:-1;4810:14:178;;-1:-1:-1;4810:9:178;4822:2;4810:14;:::i;:::-;4792:32;;:15;:32;:::i;:::-;4784:41;;;;;;;;;;;;;5522:25:190;;5510:2;5495:18;;5376:177;4784:41:178;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4672:160;:::o;2277:256::-;2372:18;;;2477:49;:3;2498:27;2477:20;:49::i;:::-;2437:89;;;;-1:-1:-1;2437:89:178;;-1:-1:-1;2277:256:178;-1:-1:-1;;2277:256:178:o;2907:134:100:-;2954:33;3018:16;2999:35;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;2999:35:100;;;;;;;;;;;;;;;;;;;;;;;2907:134;:::o;3823:151::-;3872:42;3948:19;3926:41;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;3926:41:100;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3823:151;:::o;2660:146:178:-;2734:7;2753:17;2763:6;2753:9;:17::i;:::-;-1:-1:-1;2787:12:178;;2660:146;-1:-1:-1;2660:146:178:o;3241:777::-;3362:14;3378;3396:65;3409:12;3396:65;;3423:11;3396:65;;3436:10;3396:65;;3448:12;3396:65;;:12;:65::i;:::-;3361:100;;-1:-1:-1;3361:100:178;-1:-1:-1;3472:11:178;3486:19;3361:100;;3486:19;:::i;:::-;3472:33;-1:-1:-1;3589:45:178;-1:-1:-1;;3615:18:178;3472:33;3615:10;:18;:::i;3589:45::-;3578:56;-1:-1:-1;3655:49:178;1234:6:26;3681:18:178;3694:5;3681:10;:18;:::i;:::-;:22;;3702:1;3681:22;:::i;3655:49::-;3644:60;;3754:13;3769;3784:15;3803:22;3812:12;3803:8;:22::i;:::-;3753:72;;;;;;3836:49;3845:7;3836:49;;3854:8;3836:49;;;;;;;;;;;;;;;-1:-1:-1;;;3836:49:178;;;:8;:49::i;:::-;3895;3904:7;3895:49;;3913:8;3895:49;;;;;;;;;;;;;;;-1:-1:-1;;;3895:49:178;;;:8;:49::i;:::-;3954:57;3963:9;3954:57;;3974:10;3954:57;;;;;;;;;;;;;;;-1:-1:-1;;;3954:57:178;;;:8;:57::i;:::-;3351:667;;;;;;3241:777;;;;:::o;3684:133:100:-;3730:33;3794:16;3775:35;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;3775:35:100;;;;;;;;;;;;;;;;;;;;;;3684:133;:::o;3385:141::-;3433:35;3501:18;3480:39;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;3480:39:100;;;;;;;;;;;;;;;;;;;;;;3385:141;:::o;3193:186::-;3249:56;3346:26;3317:55;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;3317:55:100;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1281:335:178;1361:9;1356:254;1380:6;1376:1;:10;1356:254;;;1407:12;1417:1;1407:9;:12::i;:::-;1559:9;;26839:21:21;:15;:21;;;1512:57:178;;1534:4;;1540:28;;1559:9;26839:21:21;1540:28:178;:::i;:::-;1512:3;;:57;:21;:57::i;:::-;1508:91;;;1571:9;:28;;-1:-1:-1;;1571:28:178;;;;;;;1508:91;-1:-1:-1;1388:3:178;;1356:254;;;;1281:335;;:::o;3047:140:100:-;3095:34;3162:18;3141:39;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2053:218:178;2125:13;;;2202:29;:3;2219:11;2202:16;:29::i;:::-;2252:3;:12;2181:50;;;;-1:-1:-1;;;;2252:12:178;;;;;;-1:-1:-1;2053:218:178;-1:-1:-1;;2053:218:178:o;3532:146:100:-;3580:40;3653:18;3632:39;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;3632:39:100;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;3632:39:100;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1904:143:178;1990:50;:3;2017:22;1990:26;:50::i;:::-;1904:143;:::o;2754:147:100:-;2803:40;2876:18;2855:39;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;2855:39:100;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;2855:39:100;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2459:141;2508:34;2575:18;2554:39;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1243:204:96;1302:7;;1282:4;;1302:7;;1298:143;;;-1:-1:-1;1332:7:96;;;;;1243:204::o;1298:143::-;1377:39;;-1:-1:-1;;;1377:39:96;;:7;:39;;;15447:51:190;;;-1:-1:-1;;;15514:18:190;;;15507:34;1428:1:96;;1377:7;;15420:18:190;;1377:39:96;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;:53;;1370:60;;1243:204;:::o;4943:99:178:-;4998:5;5026:1;5022:5;;:1;:5;;;:13;;5034:1;5022:13;;;-1:-1:-1;5030:1:178;;4943:99;-1:-1:-1;4943:99:178:o;4838:::-;4893:5;4921:1;4917:5;;:1;:5;;;:13;;4929:1;4917:13;;2606:142:100;2655:35;2723:18;2702:39;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;2702:39:100;;;;;;;;;;;;;;;;;;;;;;2606:142;:::o;2539:115:178:-;2592:33;;:::i;:::-;2637:10;;;;;;;;2644:3;2637:10;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;2637:10:178;;;;;;;;;;-1:-1:-1;;;2637:10:178;;;;;;;;;-1:-1:-1;;;2637:10:178;;;;;;;;-1:-1:-1;;;2637:10:178;;;;;;;-1:-1:-1;;;2637:10:178;;;;;;;;;;;;;;;;;;;;2644:3;;2637:10;;;;;;;;;-1:-1:-1;2637:10:178;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;2637:10:178;;;-1:-1:-1;;2637:10:178;;;;;;;;;;;;;;;;-1:-1:-1;2637:10:178;;;;;;;-1:-1:-1;2637:10:178;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;2637:10:178;;;-1:-1:-1;;2637:10:178;;;;;;;;;;;;;;;;-1:-1:-1;2637:10:178;;;;;;;-1:-1:-1;2637:10:178;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;2637:10:178;;;-1:-1:-1;;2637:10:178;;;;;;;;;;;;;;;;-1:-1:-1;2637:10:178;;;;;;;-1:-1:-1;2637:10:178;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2539:115;:::o;3124:111::-;3193:35;3205:4;3211;3217;3223;3193:11;:35::i;2812:306::-;2893:20;2915:21;2940:34;2955:8;2965;2940:14;:34::i;:::-;2892:82;;;;2984:57;2993:3;:16;;;;;;;;;;;;2984:57;;3011:13;2984:57;;;;;;;;;;;;;;;-1:-1:-1;;;2984:57:178;;;:8;:57::i;:::-;3051:60;3060:3;:17;;;;;;;;;;;;3051:60;;3079:14;3051:60;;;;;;;;;;;;;;;-1:-1:-1;;;3051:60:178;;;:8;:60::i;:::-;2882:236;;2812:306;;:::o;17342:648:21:-;17528:5;17721:22;17745:26;17787:90;17807:4;17813:1;17816:22;17840:29;17871:5;17787:19;:90::i;:::-;17720:157;;;;17888:61;17904:4;17910:16;17928:20;17888:15;:61::i;:::-;-1:-1:-1;17967:16:21;-1:-1:-1;17342:648:21;;;;;;:::o;10630:357::-;10762:5;10769;10776;10814:58;10838:4;10844:27;10814:23;:58::i;:::-;10886:57;10909:4;10915:27;10886:22;:57::i;:::-;10957:13;;10793:187;;-1:-1:-1;;;10957:13:21;;;;;;-1:-1:-1;10630:357:21;-1:-1:-1;;;10630:357:21:o;11189:1141:182:-;11322:13;11337;11368:471;11409:9;11394:12;:24;:439;;11679:9;11665:11;:23;11664:169;;11795:11;11783:9;:23;:49;;11821:11;11368:16;:471::i;11783:49::-;11809:9;11368:16;:471::i;11664:169::-;11723:11;11709;:25;:53;;11751:11;11368:16;:471::i;11709:53::-;11737:11;11368:16;:471::i;11394:439::-;11465:12;11451:11;:26;:184;;11594:11;11579:12;:26;:55;;11623:11;11368:16;:471::i;11579:55::-;11608:12;11368:16;:471::i;:::-;11358:481;;11856:471;11897:9;11882:12;:24;:439;;12167:9;12153:11;:23;12152:169;;12283:11;12271:9;:23;:49;;12309:11;11368:16;:471::i;12152:169::-;12211:11;12197;:25;:53;;12239:11;11368:16;:471::i;11882:439::-;11953:12;11939:11;:26;:184;;12082:11;12067:12;:26;:55;;12111:11;11368:16;:471::i;11856:::-;11846:481;;11189:1141;;;;;;;:::o;2980:132:96:-;3076:29;;-1:-1:-1;;;3076:29:96;;:11;;;;:29;;3088:4;;3094:5;;3101:3;;3076:29;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2980:132;;;:::o;7377:845:21:-;7662:26;;7514:12;;-1:-1:-1;;;7662:26:21;;;;7647:41;;;;7643:573;;7734:17;;;;;;;7776:24;7734:17;7792:7;7776:9;:24::i;:::-;7766:34;;7876:27;7906:48;7928:4;7934:19;7906:21;:48::i;:::-;8021:14;;;;8044:19;;;8021:43;7996:68;;-1:-1:-1;8093:84:21;8112:4;8027:7;7996:68;8150:19;8171:5;8093:18;:84::i;:::-;-1:-1:-1;8201:4:21;;7377:845;-1:-1:-1;;;;;7377:845:21:o;8850:490::-;9000:32;;8941:5;;;;9000:32;;;;;8941:5;;;9101:51;9000:32;;9101:16;:51::i;:::-;9042:110;;;;;;9162:14;9179:58;9203:4;9209:27;9179:23;:58::i;:::-;9162:75;;9254:79;9275:12;9289:11;9302:9;9313:11;9326:6;9254:20;:79::i;:::-;9247:86;;;;;;;;;8850:490;;;;;:::o;5864:493::-;6004:26;;-1:-1:-1;;;6004:26:21;;;;6081:57;6004:26;1418:2;6081:57;:::i;:::-;6057:21;:81;;;:127;;;-1:-1:-1;6158:26:21;;6057:127;6040:250;;;6256:23;;-1:-1:-1;;;6256:23:21;;;;;;;;;;;6040:250;-1:-1:-1;6299:51:21;;;;;;-1:-1:-1;;;6299:51:21;-1:-1:-1;;;;6299:51:21;;;;;;5864:493::o;4024:642:178:-;4127:19;;637:2:21;4487:13:178;4498:2;4487:8;:13;:::i;:::-;:17;;4503:1;4487:17;:::i;:::-;4486:57;;;;:::i;:::-;4464:80;-1:-1:-1;696:1:21;4595:19:178;;;;:8;:19;:::i;:::-;:23;;4617:1;4595:23;:::i;:::-;4594:64;;;;:::i;:::-;4571:88;;4024:642;;;;;:::o;2386:134:96:-;2484:29;;-1:-1:-1;;;2484:29:96;;:11;;;;:29;;2496:4;;2502:5;;2509:3;;2484:29;;;:::i;18563:909:21:-;18873:17;;18792:5;;;;;;18845:46;;18873:17;;;;;;;18845:21;:46::i;:::-;18816:75;;18906:13;:69;;;;-1:-1:-1;18949:26:21;;-1:-1:-1;;;18949:26:21;;;;18923:52;;;;;18906:69;18902:284;;;19130:30;;;19107:54;;19083:78;18902:284;19218:34;;;;19217:204;;19364:25;;19316:105;;19342:20;;-1:-1:-1;;;19364:25:21;;;;19391:29;19316:25;:105::i;:::-;19217:204;;;19272:25;;-1:-1:-1;;;19272:25:21;;;;19217:204;19196:269;19435:20;;-1:-1:-1;18563:909:21;-1:-1:-1;;;;;;18563:909:21:o;20408:282::-;20530:44;;-1:-1:-1;;20584:48:21;-1:-1:-1;;;20530:44:21;;;;-1:-1:-1;;;;20584:48:21;;-1:-1:-1;;;20584:48:21;;;;;;;20648:35;;-1:-1:-1;1917:21:190;;;1899:40;;20648:35:21;;1887:2:190;1872:18;20648:35:21;;;;;;;20408:282;;;:::o;13019:1164::-;13218:18;;13157;;13218;;;;;13157;13277:62;13218:18;901:26;926:1;696;901:26;:::i;:::-;13277:12;:62::i;:::-;13246:93;;13355:27;13354:28;:57;;;;-1:-1:-1;13386:25:21;;13354:57;13350:827;;;13442:13;;-1:-1:-1;;;13442:13:21;;;;;-1:-1:-1;13350:827:21;;;13600:552;13642:4;:26;;13669:20;13642:48;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;13712:27;:89;;13799:1;13712:89;;;13742:4;:26;;13769:20;13742:48;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;13712:89;13936:27;:172;;14080:25;;;:28;;;13936:172;;;13998:4;:25;;14024:20;13998:47;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;13936:172;13823:311;;13831:4;:25;;13857:20;13831:47;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;13823:56;;:311;13600:20;:552::i;:::-;13585:567;;13350:827;13177:1006;;13019:1164;;;;:::o;11300:1403::-;11497:17;;11438;;11497;;;;;11438;11569:60;11497:17;815:25;839:1;637:2;815:25;:::i;11569:60::-;11539:90;;11639:31;11673:27;:62;;;-1:-1:-1;11704:31:21;;;;11673:62;11639:96;;11751:26;11750:27;:55;;;;-1:-1:-1;11781:24:21;;11750:55;11746:951;;;11835:13;;-1:-1:-1;;;11835:13:21;;;;;-1:-1:-1;11746:951:21;;;12129:543;12171:4;:25;;12197:19;12171:46;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;12240:26;:86;;12324:1;12240:86;;;12269:4;:25;;12295:19;12269:46;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;12240:86;12460:26;:168;;12601:24;;;:27;;;12460:168;;;12521:4;:24;;12546:19;12521:45;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;12460:168;12349:305;;12357:4;:24;;12382:19;12357:45;;;;;;;:::i;12129:543::-;12115:557;;11746:951;11457:1246;;;11300:1403;;;;:::o;33455:220:90:-;33556:5;33576:19;;;;;;33572:97;;33618:40;;-1:-1:-1;;;33618:40:90;;33648:2;33618:40;;;17289:36:190;17341:18;;;17334:34;;;17262:18;;33618:40:90;;;;;;;33572:97;33455:220;;;:::o;25619:909:21:-;25814:32;;25703:5;;3685:1:30;;25814:32:21;;;;;;25857:186;;25973:58;25997:4;26003:27;25973:23;:58::i;:::-;901:26;926:1;696;901:26;:::i;:::-;25944:87;;;;:::i;:::-;25905:127;;25857:186;26069:13;;-1:-1:-1;;;26069:13:21;;;;;;;3542:2:30;26138:30:21;;;26204:23;;;;26263;;;;26305:22;;;;-1:-1:-1;26301:186:21;;;26363:12;26347:29;;26301:186;;;26411:12;26401:7;:22;;;26397:90;;;26459:12;26443:29;;26397:90;-1:-1:-1;26514:7:21;;25619:909;-1:-1:-1;;;;;;;25619:909:21:o;16009:199::-;16104:5;16128:4;:25;;16154:46;16167:5;839:1;637:2;815:25;;;;:::i;16154:46::-;16128:73;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;16121:80;;16009:199;;;;:::o;21208:1913::-;26839:21;:15;:21;21571;21522:25;;;21548:19;21522:46;;;;;;;:::i;:::-;;;;;;;;;;;;:70;;;;;;;;;;;;;;;;;;;;21650:16;21602:4;:24;;21627:19;21602:45;;;;;;;:::i;:::-;;;;;;;;;;;;:64;;;;;;;;;;;;;;;;;;21724:17;21764:56;21777:19;637:2;21764:12;:56::i;:::-;21744:76;;;;;;;;;-1:-1:-1;;21744:76:21;;;;;;;;-1:-1:-1;21836:31:21;;;;;21835:32;:50;;;;-1:-1:-1;21871:14:21;;21835:50;21831:119;;;21901:38;;-1:-1:-1;;21901:38:21;21935:4;21901:38;;;21831:119;22067:27;;-1:-1:-1;;;22067:27:21;;;;;22036:28;;22216:25;;;;22242:60;;22255:18;;;;;901:26;22242:12;:60::i;:::-;22216:87;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;22177:16;:126;22166:137;;22347:21;22335:33;;:8;:33;;;;:48;;;;22372:11;22335:48;22331:689;;;22434:18;;;;;;;22578:21;22527:26;;;22434:18;22527:48;;;;;;;:::i;:::-;;;;;;;;;;;;:72;;;;;;;;;;;;;;;;;;;;22667:16;22617:4;:25;;22643:20;22617:47;;;;;;;:::i;:::-;;;;;;;;;;;;:66;;;;;;;;;;;;;;;;;;22792:58;22805:20;696:1;22792:12;:58::i;:::-;22771:79;;-1:-1:-1;;22771:79:21;;;;;;;;;;;;;;;;;;-1:-1:-1;22771:79:21;22874:32;;;22873:33;:51;;;;-1:-1:-1;22910:14:21;;22873:51;22869:137;;;22948:39;;-1:-1:-1;;22948:39:21;;;;;22869:137;22385:635;22331:689;-1:-1:-1;;23091:23:21;;;;;;-1:-1:-1;;;23091:23:21;-1:-1:-1;;23091:23:21;;;;;;;;;;-1:-1:-1;;;;;21208:1913:21:o;24491:496::-;24629:14;901:26;926:1;696;901:26;:::i;:::-;24846:35;;24896:27;24891:90;;24948:18;;:22;;24969:1;;24948:18;;;;;:22;:::i;:::-;24939:31;;;24491:496;-1:-1:-1;;;24491:496:21:o;14247:1265::-;14437:13;14452;14498:69;14517:12;14531:11;14544:9;14555:11;14498:18;:69::i;:::-;14477:90;;-1:-1:-1;14477:90:21;-1:-1:-1;14771:25:21;;;;:15;;;;:25;14747:13;14880:154;752:3;901:26;14942:35;;;;14747:13;14880:14;:154::i;:::-;14852:5;:182;14812:236;;-1:-1:-1;;15256:25:21;;15247:6;15234:9;15227:17;;:26;:54;:102;;-1:-1:-1;;15227:102:21;;;15302:6;15290:9;:18;;;15227:102;15201:128;-1:-1:-1;1234:6:26;15386:25:21;15353:17;;;:26;;:30;:58;:142;;1234:6:26;15353:142:21;;;15448:6;15436:9;:18;;;15457:1;15436:22;15353:142;15343:152;;14676:830;;14247:1265;;;;;;;;:::o;20023:379::-;20198:5;20219:29;:34;;20252:1;20219:34;20215:73;;-1:-1:-1;;;20255:33:21;;20215:73;20306:89;20327:13;20342:21;20365:29;20306:89;;:20;:89::i;:::-;20299:96;20023:379;-1:-1:-1;;;;20023:379:21:o;16214:179::-;16292:7;16342:10;;:34;;16375:1;16367:5;:9;16342:34;;;-1:-1:-1;16355:9:21;16214:179;-1:-1:-1;16214:179:21:o;23736:342::-;23897:10;24047:12;24015:21;23992:20;:44;23991:69;;;;;;;:::i;:::-;;;23736:342;-1:-1:-1;;;;23736:342:21:o;16399:191::-;16486:5;16561:11;16541:12;16556:1;16541:16;16540:32;;;;;:::i;:::-;;;16399:191;-1:-1:-1;;;16399:191:21:o;15518:221::-;15604:9;15615;15649:28;15669:1;15672;15675;15649:19;:28::i;:::-;15636:41;;-1:-1:-1;15636:41:21;-1:-1:-1;15700:32:21;15720:1;15636:41;;15700:19;:32::i;:::-;15687:45;;;;-1:-1:-1;15518:221:21;-1:-1:-1;;;;;15518:221:21:o;1908:204:20:-;1997:14;2032:5;2036:1;2032;:5;:::i;:::-;2023:14;;2056:10;:49;;2095:10;2104:1;2095:6;:10;:::i;:::-;2056:49;;;2069:23;2082:6;2090:1;2069:12;:23::i;:::-;2047:58;1908:204;-1:-1:-1;;;;;1908:204:20:o;15745:258:21:-;15823:9;15834;15872:1;15868:5;;:1;:5;;;:23;;15886:1;15889;15868:23;;;15877:1;15880;15868:23;15855:36;;-1:-1:-1;15855:36:21;-1:-1:-1;15905:7:21;;;;;;;;;15901:96;;;15934:1;15928:7;;15901:96;;;15962:1;15956:7;;:3;:7;;;15952:45;;;-1:-1:-1;15985:1:21;15952:45;15745:258;;;;;;:::o;6215:704:89:-;6277:7;6300:1;6305;6300:6;6296:150;;6400:35;1035:4:79;6400:11:89;:35::i;:::-;6896:1;6891;6887;:5;6886:11;;;;;:::i;:::-;;6900:1;6886:15;6876:5;;;6860:42;;6215:704;-1:-1:-1;;;6215:704:89:o;1776:194:79:-;1881:10;1875:4;1868:24;1918:4;1912;1905:18;1949:4;1943;1936:18;-1:-1:-1;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;:::i;:::-;;;;:::o;:::-;;;;;;;;;;;;;;;;;;;;;;;;:::o;:::-;;;;;;;;;;;;;;;;;;;;;;;;:::o;1328:163:190:-;1395:20;;1455:10;1444:22;;1434:33;;1424:61;;1481:1;1478;1471:12;1496:256;1562:6;1570;1623:2;1611:9;1602:7;1598:23;1594:32;1591:52;;;1639:1;1636;1629:12;1591:52;1662:28;1680:9;1662:28;:::i;:::-;1652:38;;1709:37;1742:2;1731:9;1727:18;1709:37;:::i;1950:226::-;2009:6;2062:2;2050:9;2041:7;2037:23;2033:32;2030:52;;;2078:1;2075;2068:12;2030:52;-1:-1:-1;2123:23:190;;1950:226;-1:-1:-1;1950:226:190:o;2181:273::-;2237:6;2290:2;2278:9;2269:7;2265:23;2261:32;2258:52;;;2306:1;2303;2296:12;2258:52;2345:9;2332:23;2398:5;2391:13;2384:21;2377:5;2374:32;2364:60;;2420:1;2417;2410:12;2816:637;3006:2;3018:21;;;3088:13;;2991:18;;;3110:22;;;2958:4;;3189:15;;;3163:2;3148:18;;;2958:4;3232:195;3246:6;3243:1;3240:13;3232:195;;;3311:13;;-1:-1:-1;;;;;3307:39:190;3295:52;;3376:2;3402:15;;;;3367:12;;;;3343:1;3261:9;3232:195;;;-1:-1:-1;3444:3:190;;2816:637;-1:-1:-1;;;;;2816:637:190:o;3458:289::-;3500:3;3538:5;3532:12;3565:6;3560:3;3553:19;3621:6;3614:4;3607:5;3603:16;3596:4;3591:3;3587:14;3581:47;3673:1;3666:4;3657:6;3652:3;3648:16;3644:27;3637:38;3736:4;3729:2;3725:7;3720:2;3712:6;3708:15;3704:29;3699:3;3695:39;3691:50;3684:57;;;3458:289;;;;:::o;3752:579::-;3804:3;3835;3867:5;3861:12;3894:6;3889:3;3882:19;3926:4;3921:3;3917:14;3910:21;;3984:4;3974:6;3971:1;3967:14;3960:5;3956:26;3952:37;4023:4;4016:5;4012:16;4046:1;4056:249;4070:6;4067:1;4064:13;4056:249;;;4157:2;4153:7;4145:5;4139:4;4135:16;4131:30;4126:3;4119:43;4183:38;4216:4;4207:6;4201:13;4183:38;:::i;:::-;4256:4;4281:14;;;;4175:46;;-1:-1:-1;4244:17:190;;;;;4092:1;4085:9;4056:249;;;-1:-1:-1;4321:4:190;;3752:579;-1:-1:-1;;;;;;3752:579:190:o;4336:1035::-;4542:4;4590:2;4579:9;4575:18;4620:2;4609:9;4602:21;4643:6;4678;4672:13;4709:6;4701;4694:22;4747:2;4736:9;4732:18;4725:25;;4809:2;4799:6;4796:1;4792:14;4781:9;4777:30;4773:39;4759:53;;4847:2;4839:6;4835:15;4868:1;4878:464;4892:6;4889:1;4886:13;4878:464;;;4957:22;;;-1:-1:-1;;4953:36:190;4941:49;;5013:13;;5058:9;;-1:-1:-1;;;;;5054:35:190;5039:51;;5137:2;5129:11;;;5123:18;5178:2;5161:15;;;5154:27;;;5123:18;5204:58;;5246:15;;5123:18;5204:58;:::i;:::-;5194:68;-1:-1:-1;;5297:2:190;5320:12;;;;5285:15;;;;;4914:1;4907:9;4878:464;;;-1:-1:-1;5359:6:190;;4336:1035;-1:-1:-1;;;;;;4336:1035:190:o;5558:160::-;5624:20;;5684:1;5673:20;;;5663:31;;5653:59;;5708:1;5705;5698:12;5723:393;5801:6;5809;5817;5825;5878:3;5866:9;5857:7;5853:23;5849:33;5846:53;;;5895:1;5892;5885:12;5846:53;5918:27;5935:9;5918:27;:::i;:::-;5908:37;;5964:36;5996:2;5985:9;5981:18;5964:36;:::i;:::-;5954:46;;6019:36;6051:2;6040:9;6036:18;6019:36;:::i;:::-;6009:46;;6074:36;6106:2;6095:9;6091:18;6074:36;:::i;:::-;6064:46;;5723:393;;;;;;;:::o;6121:446::-;6173:3;6211:5;6205:12;6238:6;6233:3;6226:19;6270:4;6265:3;6261:14;6254:21;;6309:4;6302:5;6298:16;6332:1;6342:200;6356:6;6353:1;6350:13;6342:200;;;6421:13;;-1:-1:-1;;;;;;6417:40:190;6405:53;;6487:4;6478:14;;;;6515:17;;;;6378:1;6371:9;6342:200;;;-1:-1:-1;6558:3:190;;6121:446;-1:-1:-1;;;;6121:446:190:o;6572:1145::-;6792:4;6840:2;6829:9;6825:18;6870:2;6859:9;6852:21;6893:6;6928;6922:13;6959:6;6951;6944:22;6997:2;6986:9;6982:18;6975:25;;7059:2;7049:6;7046:1;7042:14;7031:9;7027:30;7023:39;7009:53;;7097:2;7089:6;7085:15;7118:1;7128:560;7142:6;7139:1;7136:13;7128:560;;;7235:2;7231:7;7219:9;7211:6;7207:22;7203:36;7198:3;7191:49;7269:6;7263:13;7315:2;7309:9;7346:2;7338:6;7331:18;7376:48;7420:2;7412:6;7408:15;7394:12;7376:48;:::i;:::-;7362:62;;7473:2;7469;7465:11;7459:18;7437:40;;7526:6;7518;7514:19;7509:2;7501:6;7497:15;7490:44;7557:51;7601:6;7585:14;7557:51;:::i;:::-;7547:61;-1:-1:-1;;;7643:2:190;7666:12;;;;7631:15;;;;;7164:1;7157:9;7128:560;;7722:296;7788:6;7796;7849:2;7837:9;7828:7;7824:23;7820:32;7817:52;;;7865:1;7862;7855:12;7817:52;7910:23;;;-1:-1:-1;7976:36:190;8008:2;7993:18;;7976:36;:::i;8023:280::-;8222:2;8211:9;8204:21;8185:4;8242:55;8293:2;8282:9;8278:18;8270:6;8242:55;:::i;8308:182::-;8365:6;8418:2;8406:9;8397:7;8393:23;8389:32;8386:52;;;8434:1;8431;8424:12;8386:52;8457:27;8474:9;8457:27;:::i;8495:1033::-;8699:4;8747:2;8736:9;8732:18;8777:2;8766:9;8759:21;8800:6;8835;8829:13;8866:6;8858;8851:22;8904:2;8893:9;8889:18;8882:25;;8966:2;8956:6;8953:1;8949:14;8938:9;8934:30;8930:39;8916:53;;9004:2;8996:6;8992:15;9025:1;9035:464;9049:6;9046:1;9043:13;9035:464;;;9114:22;;;-1:-1:-1;;9110:36:190;9098:49;;9170:13;;9215:9;;-1:-1:-1;;;;;9211:35:190;9196:51;;9294:2;9286:11;;;9280:18;9335:2;9318:15;;;9311:27;;;9280:18;9361:58;;9403:15;;9280:18;9361:58;:::i;:::-;9351:68;-1:-1:-1;;9454:2:190;9477:12;;;;9442:15;;;;;9071:1;9064:9;9035:464;;9533:161;9600:20;;9660:8;9649:20;;9639:31;;9629:59;;9684:1;9681;9674:12;9699:184;9757:6;9810:2;9798:9;9789:7;9785:23;9781:32;9778:52;;;9826:1;9823;9816:12;9778:52;9849:28;9867:9;9849:28;:::i;10277:252::-;10341:6;10349;10402:2;10390:9;10381:7;10377:23;10373:32;10370:52;;;10418:1;10415;10408:12;10370:52;10441:27;10458:9;10441:27;:::i;:::-;10431:37;;10487:36;10519:2;10508:9;10504:18;10487:36;:::i;10534:327::-;10636:5;10659:1;10669:186;10683:4;10680:1;10677:11;10669:186;;;10756:13;;10753:1;10742:28;10730:41;;10800:4;10791:14;;;;10828:17;;;;10703:1;10696:9;10669:186;;10866:316;10957:5;10980:1;10990:186;11004:4;11001:1;10998:11;10990:186;;;11077:13;;11074:1;11063:28;11051:41;;11121:4;11112:14;;;;11149:17;;;;11024:1;11017:9;10990:186;;11187:330;11290:5;11313:1;11323:188;11337:4;11334:1;11331:11;11323:188;;;11400:13;;11415:10;11396:30;11384:43;;11456:4;11447:14;;;;11484:17;;;;11357:1;11350:9;11323:188;;11522:319;11614:5;11637:1;11647:188;11661:4;11658:1;11655:11;11647:188;;;11724:13;;11739:10;11720:30;11708:43;;11780:4;11771:14;;;;11808:17;;;;11681:1;11674:9;11647:188;;11846:1740;12070:13;;84;77:21;65:34;;12040:4;12025:20;;12142:4;12134:6;12130:17;12124:24;12157:51;12202:4;12191:9;12187:20;12173:12;84:13;77:21;65:34;;14:91;12157:51;;12257:4;12249:6;12245:17;12239:24;12272:54;12320:4;12309:9;12305:20;12289:14;177:4;166:16;154:29;;110:75;12272:54;;12375:4;12367:6;12363:17;12357:24;12390:54;12438:4;12427:9;12423:20;12407:14;177:4;166:16;154:29;;110:75;12390:54;;12493:4;12485:6;12481:17;12475:24;12508:54;12556:4;12545:9;12541:20;12525:14;265:1;254:20;242:33;;190:91;12508:54;;12611:4;12603:6;12599:17;12593:24;12626:54;12674:4;12663:9;12659:20;12643:14;265:1;254:20;242:33;;190:91;12626:54;;12729:4;12721:6;12717:17;12711:24;12744:55;12793:4;12782:9;12778:20;12762:14;362:8;351:20;339:33;;286:92;12744:55;;12848:4;12840:6;12836:17;12830:24;12863:55;12912:4;12901:9;12897:20;12881:14;362:8;351:20;339:33;;286:92;12863:55;;12967:6;12959;12955:19;12949:26;12984:56;13032:6;13021:9;13017:22;13001:14;458:1;447:20;435:33;;383:91;12984:56;;13089:6;13081;13077:19;13071:26;13106:73;13171:6;13160:9;13156:22;13140:14;13106:73;:::i;:::-;;13228:6;13220;13216:19;13210:26;13245:62;13299:6;13288:9;13284:22;13268:14;13245:62;:::i;:::-;;13357:6;13349;13345:19;13339:26;13374:75;13441:6;13430:9;13426:22;13409:15;13374:75;:::i;:::-;;13499:6;13491;13487:19;13481:26;13516:64;13572:6;13561:9;13557:22;13540:15;13516:64;:::i;:::-;;11846:1740;;;;:::o;13591:298::-;13658:6;13666;13719:2;13707:9;13698:7;13694:23;13690:32;13687:52;;;13735:1;13732;13725:12;13687:52;13780:23;;;-1:-1:-1;13846:37:190;13879:2;13864:18;;13846:37;:::i;13894:127::-;13955:10;13950:3;13946:20;13943:1;13936:31;13986:4;13983:1;13976:15;14010:4;14007:1;14000:15;14026:125;14091:9;;;14112:10;;;14109:36;;;14125:18;;:::i;14156:168::-;14229:9;;;14260;;14277:15;;;14271:22;;14257:37;14247:71;;14298:18;;:::i;14329:380::-;14408:1;14404:12;;;;14451;;;14472:61;;14526:4;14518:6;14514:17;14504:27;;14472:61;14579:2;14571:6;14568:14;14548:18;14545:38;14542:161;;14625:10;14620:3;14616:20;14613:1;14606:31;14660:4;14657:1;14650:15;14688:4;14685:1;14678:15;14714:189;14812:1;14801:16;;;14783;;;;14779:39;-1:-1:-1;;14833:21:190;;14866:6;14856:17;;14830:44;14827:70;;;14877:18;;:::i;14908:185::-;15004:1;14975:16;;;14993;;;;14971:39;15056:5;15025:16;;-1:-1:-1;;15043:20:190;;15022:42;15019:68;;;15067:18;;:::i;15098:170::-;15195:10;15188:18;;;15168;;;15164:43;;15219:20;;15216:46;;;15242:18;;:::i;15552:184::-;15622:6;15675:2;15663:9;15654:7;15650:23;15646:32;15643:52;;;15691:1;15688;15681:12;15643:52;-1:-1:-1;15714:16:190;;15552:184;-1:-1:-1;15552:184:190:o;15741:358::-;15942:6;15931:9;15924:25;15985:6;15980:2;15969:9;15965:18;15958:34;16028:2;16023;16012:9;16008:18;16001:30;15905:4;16048:45;16089:2;16078:9;16074:18;16066:6;16048:45;:::i;16104:127::-;16165:10;16160:3;16156:20;16153:1;16146:31;16196:4;16193:1;16186:15;16220:4;16217:1;16210:15;16236:120;16276:1;16302;16292:35;;16307:18;;:::i;:::-;-1:-1:-1;16341:9:190;;16236:120::o;16361:112::-;16393:1;16419;16409:35;;16424:18;;:::i;:::-;-1:-1:-1;16458:9:190;;16361:112::o;16845:128::-;16912:9;;;16933:11;;;16930:37;;;16947:18;;:::i;16978:127::-;17039:10;17034:3;17030:20;17027:1;17020:31;17070:4;17067:1;17060:15;17094:4;17091:1;17084:15;17379:151;17469:4;17462:12;;;17448;;;17444:31;;17487:14;;17484:40;;;17504:18;;:::i", "linkReferences": {}}, "methodIdentifiers": {"IS_TEST()": "fa7626d4", "changeInterval(uint24)": "9c76a2af", "excludeArtifacts()": "b5508aa9", "excludeContracts()": "e20c9f71", "excludeSelectors()": "b0464fdc", "excludeSenders()": "1ed7831c", "failed()": "ba414fa6", "generateObservations(uint256,int16)": "711d6518", "generatePastBlocks(uint256)": "2f7b0f83", "getLendingStateTickAndCheckpoint(uint32,uint32)": "15f0cc2f", "getObservationStruct()": "e7c1ddc4", "getObservedTicks(bool)": "173c0555", "getTicks(int16)": "8e62be5b", "intMax(int16,int16)": "d5b3bc0b", "intMin(int16,int16)": "dc2a75f0", "mineBlock(uint256)": "1720b1bb", "obs()": "072d57d6", "targetArtifactSelectors()": "66d9a9a0", "targetArtifacts()": "85226c81", "targetContracts()": "3f7286f4", "targetInterfaces()": "2ade3880", "targetSelectors()": "916a17c6", "targetSenders()": "3e5e3c23", "timestamp()": "b80777ea", "verifyIndexes(uint256,uint24)": "f85782b0", "verifyTicks(int16)": "efae6212", "verifyTicks(int16,int16,int16,int16)": "315065aa"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"uint24\",\"name\":\"midTermIntervalConfig\",\"type\":\"uint24\"},{\"internalType\":\"uint24\",\"name\":\"longTermIntervalConfigFactor\",\"type\":\"uint24\"},{\"internalType\":\"int16\",\"name\":\"firstTick\",\"type\":\"int16\"}],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"inputs\":[],\"name\":\"InvalidIntervalConfig\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"uint8\",\"name\":\"bits\",\"type\":\"uint8\"},{\"internalType\":\"int256\",\"name\":\"value\",\"type\":\"int256\"}],\"name\":\"SafeCastOverflowedIntDowncast\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"int16\",\"name\":\"lendingStateTick\",\"type\":\"int16\"}],\"name\":\"UpdateLendingTick\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"name\":\"log\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"name\":\"log_address\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint256[]\",\"name\":\"val\",\"type\":\"uint256[]\"}],\"name\":\"log_array\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"int256[]\",\"name\":\"val\",\"type\":\"int256[]\"}],\"name\":\"log_array\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address[]\",\"name\":\"val\",\"type\":\"address[]\"}],\"name\":\"log_array\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"bytes\",\"name\":\"\",\"type\":\"bytes\"}],\"name\":\"log_bytes\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"name\":\"log_bytes32\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"int256\",\"name\":\"\",\"type\":\"int256\"}],\"name\":\"log_int\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"val\",\"type\":\"address\"}],\"name\":\"log_named_address\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"uint256[]\",\"name\":\"val\",\"type\":\"uint256[]\"}],\"name\":\"log_named_array\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"int256[]\",\"name\":\"val\",\"type\":\"int256[]\"}],\"name\":\"log_named_array\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"address[]\",\"name\":\"val\",\"type\":\"address[]\"}],\"name\":\"log_named_array\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"bytes\",\"name\":\"val\",\"type\":\"bytes\"}],\"name\":\"log_named_bytes\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"bytes32\",\"name\":\"val\",\"type\":\"bytes32\"}],\"name\":\"log_named_bytes32\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"int256\",\"name\":\"val\",\"type\":\"int256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"decimals\",\"type\":\"uint256\"}],\"name\":\"log_named_decimal_int\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"val\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"decimals\",\"type\":\"uint256\"}],\"name\":\"log_named_decimal_uint\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"int256\",\"name\":\"val\",\"type\":\"int256\"}],\"name\":\"log_named_int\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"string\",\"name\":\"val\",\"type\":\"string\"}],\"name\":\"log_named_string\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"val\",\"type\":\"uint256\"}],\"name\":\"log_named_uint\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"name\":\"log_string\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"name\":\"log_uint\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"bytes\",\"name\":\"\",\"type\":\"bytes\"}],\"name\":\"logs\",\"type\":\"event\"},{\"inputs\":[],\"name\":\"IS_TEST\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint24\",\"name\":\"longTermIntervalConfig\",\"type\":\"uint24\"}],\"name\":\"changeInterval\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"excludeArtifacts\",\"outputs\":[{\"internalType\":\"string[]\",\"name\":\"excludedArtifacts_\",\"type\":\"string[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"excludeContracts\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"excludedContracts_\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"excludeSelectors\",\"outputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"addr\",\"type\":\"address\"},{\"internalType\":\"bytes4[]\",\"name\":\"selectors\",\"type\":\"bytes4[]\"}],\"internalType\":\"struct StdInvariant.FuzzSelector[]\",\"name\":\"excludedSelectors_\",\"type\":\"tuple[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"excludeSenders\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"excludedSenders_\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"failed\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"blocks\",\"type\":\"uint256\"},{\"internalType\":\"int16\",\"name\":\"tick\",\"type\":\"int16\"}],\"name\":\"generateObservations\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"blocks\",\"type\":\"uint256\"}],\"name\":\"generatePastBlocks\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint32\",\"name\":\"timeElapsedSinceUpdate\",\"type\":\"uint32\"},{\"internalType\":\"uint32\",\"name\":\"timeElapsedSinceLendingUpdate\",\"type\":\"uint32\"}],\"name\":\"getLendingStateTickAndCheckpoint\",\"outputs\":[{\"internalType\":\"int16\",\"name\":\"tick\",\"type\":\"int16\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getObservationStruct\",\"outputs\":[{\"components\":[{\"internalType\":\"bool\",\"name\":\"isMidTermBufferInitialized\",\"type\":\"bool\"},{\"internalType\":\"bool\",\"name\":\"isLongTermBufferInitialized\",\"type\":\"bool\"},{\"internalType\":\"uint8\",\"name\":\"midTermIndex\",\"type\":\"uint8\"},{\"internalType\":\"uint8\",\"name\":\"longTermIndex\",\"type\":\"uint8\"},{\"internalType\":\"int16\",\"name\":\"lastTick\",\"type\":\"int16\"},{\"internalType\":\"int16\",\"name\":\"lastLendingStateTick\",\"type\":\"int16\"},{\"internalType\":\"uint24\",\"name\":\"midTermIntervalConfig\",\"type\":\"uint24\"},{\"internalType\":\"uint24\",\"name\":\"longTermIntervalConfig\",\"type\":\"uint24\"},{\"internalType\":\"int56\",\"name\":\"lendingCumulativeSum\",\"type\":\"int56\"},{\"internalType\":\"int56[51]\",\"name\":\"midTermCumulativeSum\",\"type\":\"int56[51]\"},{\"internalType\":\"int56[9]\",\"name\":\"longTermCumulativeSum\",\"type\":\"int56[9]\"},{\"internalType\":\"uint32[51]\",\"name\":\"midTermTimeInterval\",\"type\":\"uint32[51]\"},{\"internalType\":\"uint32[9]\",\"name\":\"longTermTimeInterval\",\"type\":\"uint32[9]\"}],\"internalType\":\"struct GeometricTWAP.Observations\",\"name\":\"\",\"type\":\"tuple\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bool\",\"name\":\"isLongTermBufferInitialized\",\"type\":\"bool\"}],\"name\":\"getObservedTicks\",\"outputs\":[{\"internalType\":\"int16\",\"name\":\"longTermTick\",\"type\":\"int16\"},{\"internalType\":\"int16\",\"name\":\"midTermTick\",\"type\":\"int16\"},{\"internalType\":\"int16\",\"name\":\"lastTick\",\"type\":\"int16\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"int16\",\"name\":\"currentTick\",\"type\":\"int16\"}],\"name\":\"getTicks\",\"outputs\":[{\"internalType\":\"int16\",\"name\":\"minTick\",\"type\":\"int16\"},{\"internalType\":\"int16\",\"name\":\"maxTick\",\"type\":\"int16\"},{\"internalType\":\"int16\",\"name\":\"lastTick\",\"type\":\"int16\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"int16\",\"name\":\"a\",\"type\":\"int16\"},{\"internalType\":\"int16\",\"name\":\"b\",\"type\":\"int16\"}],\"name\":\"intMax\",\"outputs\":[{\"internalType\":\"int16\",\"name\":\"\",\"type\":\"int16\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"int16\",\"name\":\"a\",\"type\":\"int16\"},{\"internalType\":\"int16\",\"name\":\"b\",\"type\":\"int16\"}],\"name\":\"intMin\",\"outputs\":[{\"internalType\":\"int16\",\"name\":\"\",\"type\":\"int16\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"blockStep\",\"type\":\"uint256\"}],\"name\":\"mineBlock\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"obs\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"isMidTermBufferInitialized\",\"type\":\"bool\"},{\"internalType\":\"bool\",\"name\":\"isLongTermBufferInitialized\",\"type\":\"bool\"},{\"internalType\":\"uint8\",\"name\":\"midTermIndex\",\"type\":\"uint8\"},{\"internalType\":\"uint8\",\"name\":\"longTermIndex\",\"type\":\"uint8\"},{\"internalType\":\"int16\",\"name\":\"lastTick\",\"type\":\"int16\"},{\"internalType\":\"int16\",\"name\":\"lastLendingStateTick\",\"type\":\"int16\"},{\"internalType\":\"uint24\",\"name\":\"midTermIntervalConfig\",\"type\":\"uint24\"},{\"internalType\":\"uint24\",\"name\":\"longTermIntervalConfig\",\"type\":\"uint24\"},{\"internalType\":\"int56\",\"name\":\"lendingCumulativeSum\",\"type\":\"int56\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetArtifactSelectors\",\"outputs\":[{\"components\":[{\"internalType\":\"string\",\"name\":\"artifact\",\"type\":\"string\"},{\"internalType\":\"bytes4[]\",\"name\":\"selectors\",\"type\":\"bytes4[]\"}],\"internalType\":\"struct StdInvariant.FuzzArtifactSelector[]\",\"name\":\"targetedArtifactSelectors_\",\"type\":\"tuple[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetArtifacts\",\"outputs\":[{\"internalType\":\"string[]\",\"name\":\"targetedArtifacts_\",\"type\":\"string[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetContracts\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"targetedContracts_\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetInterfaces\",\"outputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"addr\",\"type\":\"address\"},{\"internalType\":\"string[]\",\"name\":\"artifacts\",\"type\":\"string[]\"}],\"internalType\":\"struct StdInvariant.FuzzInterface[]\",\"name\":\"targetedInterfaces_\",\"type\":\"tuple[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetSelectors\",\"outputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"addr\",\"type\":\"address\"},{\"internalType\":\"bytes4[]\",\"name\":\"selectors\",\"type\":\"bytes4[]\"}],\"internalType\":\"struct StdInvariant.FuzzSelector[]\",\"name\":\"targetedSelectors_\",\"type\":\"tuple[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetSenders\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"targetedSenders_\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"timestamp\",\"outputs\":[{\"internalType\":\"uint32\",\"name\":\"\",\"type\":\"uint32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"duration\",\"type\":\"uint256\"},{\"internalType\":\"uint24\",\"name\":\"interval\",\"type\":\"uint24\"}],\"name\":\"verifyIndexes\",\"outputs\":[],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"int16\",\"name\":\"longTermTick\",\"type\":\"int16\"},{\"internalType\":\"int16\",\"name\":\"midTermTick\",\"type\":\"int16\"},{\"internalType\":\"int16\",\"name\":\"_blockTick\",\"type\":\"int16\"},{\"internalType\":\"int16\",\"name\":\"_currentTick\",\"type\":\"int16\"}],\"name\":\"verifyTicks\",\"outputs\":[],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"int16\",\"name\":\"tick\",\"type\":\"int16\"}],\"name\":\"verifyTicks\",\"outputs\":[],\"stateMutability\":\"view\",\"type\":\"function\"}],\"devdoc\":{\"errors\":{\"SafeCastOverflowedIntDowncast(uint8,int256)\":[{\"details\":\"Value doesn't fit in an int of `bits` size.\"}]},\"events\":{\"UpdateLendingTick(int16)\":{\"details\":\"Emitted when `lendingStateTick` is updated\",\"params\":{\"lendingStateTick\":\"The updated value for lending state tick\"}}},\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"test/shared/GeometricTWAPTestFixture.sol\":\"GeometricTWAPTestFixture\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":1inch/=lib/1inch/\",\":@1inch/=lib/1inch/\",\":@mangrovedao/mangrove-core/=lib/mangrove-core/\",\":@mgv/forge-std/=lib/mangrove-core/lib/forge-std/src/\",\":@mgv/lib/=lib/mangrove-core/lib/\",\":@mgv/script/=lib/mangrove-core/script/\",\":@mgv/src/=lib/mangrove-core/src/\",\":@mgv/test/=lib/mangrove-core/test/\",\":@morpho-org/morpho-blue/=lib/morpho-blue/\",\":@openzeppelin/=lib/openzeppelin-contracts/\",\":ExcessivelySafeCall/=lib/ExcessivelySafeCall/src/\",\":core/=lib/mangrove-core/lib/core/\",\":ds-test/=lib/ExcessivelySafeCall/lib/ds-test/src/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":forge-std/=lib/openzeppelin-contracts/lib/forge-std/src/\",\":halmos-cheatcodes/=lib/morpho-blue/lib/halmos-cheatcodes/src/\",\":mangrove-core/=lib/mangrove-core/\",\":morpho-blue/=lib/morpho-blue/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":preprocessed/=lib/mangrove-core/lib/preprocessed/\"]},\"sources\":{\"contracts/AmmalgamPair.sol\":{\"keccak256\":\"0xe8f98844a55a216605e6c6dd6837977fafda458a6b5d0cfa1f3a18d25e9432e4\",\"urls\":[\"bzz-raw://65dda1a1de8dd64e31c666b13de3d0583b4b0da923c67065cadcddefe47562a2\",\"dweb:/ipfs/Qmaev9WFa4yyL8fXVoWkXwNsTTY8wY7jTBGDoKJbdwSCzS\"]},\"contracts/SaturationAndGeometricTWAPState.sol\":{\"keccak256\":\"0x5e293a35668bb216a99379ea2176894314cc0f1ac68644fcf4c07017da1a4419\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://00349bb86f1b657010919b4bc3f616ad56ef4883b99ab0eead36815dae93dc76\",\"dweb:/ipfs/QmbEd9GD2JxuDntX35YcfbSCcpRstDU9GDPUkBKGzsxvqE\"]},\"contracts/factories/AmmalgamFactory.sol\":{\"keccak256\":\"0xe0d9baf63d9538a7ecb8bd24ea61a8cdf6fc9c1e9eb028f343548adeb8b93e4e\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://bfca24db47fbbaeef8bc36996cbfed78eb48771ac71d2800f081fb58a8e8c92b\",\"dweb:/ipfs/QmUfYEwfmrjSmchqXi58SnpSina4qKQvD2Jbk5RqYsaoCa\"]},\"contracts/interfaces/IAmmalgamPair.sol\":{\"keccak256\":\"0xa17e45b2348d8920d9970c5d50b300fc0a1e8d03350cdd0d1a624494baa70337\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://8d252e89e5d49d1c15a0c0c0a495a325b9f8d608714b29279a7bacb1e4bf8795\",\"dweb:/ipfs/QmRkZ7a8JJQYEw6HQMJjjkuAK8b5Th1X1ET6BG1R8mx4qw\"]},\"contracts/interfaces/ISaturationAndGeometricTWAPState.sol\":{\"keccak256\":\"0xc9add2ad41f8edd9d360ced8d2cd7bd18dd500304794434fb2e309fa0f5af83c\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://8ecc810c544ac734ef26a2f6bebea3f3bd12d773965d297991e0e0e72892fa20\",\"dweb:/ipfs/QmarXc1Ut4FZzPRRZs2M2udbJjuZUJQHQ8fvmSr3bpHErR\"]},\"contracts/interfaces/callbacks/IAmmalgamCallee.sol\":{\"keccak256\":\"0x904b858859d460a61c9e644ca87009d8e32ba20482ef218801c89c7fb1ece339\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://1a7cedebbacc453e3e4e339fcc76fd3268247c13982de82b4930d59a44256c1d\",\"dweb:/ipfs/QmdjdvYabbwAYcV72xjiXyq278xQivFtiqD3eQ5P9Gk4f1\"]},\"contracts/interfaces/callbacks/ITransferValidator.sol\":{\"keccak256\":\"0x6d9028fc4ad1914e6b2091e6ba46a9f836f9e67ea435c4a8fef41363f2ceaf56\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://7ecaade4884d460168f6978edf35706f7b9e363de6002942b1d171a338dca6a4\",\"dweb:/ipfs/QmS5wgfDt5Pn68rpCytpzhiy57LcmivVFQ5XLGXUUP5Tt8\"]},\"contracts/interfaces/factories/IAmmalgamFactory.sol\":{\"keccak256\":\"0x1c80089901e8d7d7451775b5eaa92092eb2b65319cb92fa7884281bae49f52b8\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://bf1201399bb9d5046e0e788ff88394b2468771096a2a0d3500af542923e84628\",\"dweb:/ipfs/QmeUaPyQpbPbP5fyPUT2FfzeDgHcdyQAn1DaNg9uCuGoj9\"]},\"contracts/interfaces/factories/IFactoryCallback.sol\":{\"keccak256\":\"0x33250cf8351adb4846a3d133a9bc06568288e4c680bcf5b1085e3bca40a35e52\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://5663a39af4ed3040a58beaa5641425b9adca83c2683dd220e0c11e644fefe52b\",\"dweb:/ipfs/QmYB8Vf37WDzQfSpMDjv8hVicuaF1wMBzf7xjHRjGCy3wT\"]},\"contracts/interfaces/factories/INewTokensFactory.sol\":{\"keccak256\":\"0x3b2f1ee34106d2694a9ebbe600be692bed645f4247f4a24da3d5ec46025ab3e9\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://73143452a06db52deb593585fea6f2ef7c46e9ef6d649562dc39e79e4e5dca2b\",\"dweb:/ipfs/QmYQEy7BZWnfWKnuac8GB4QPhG5qJpaHQAfkTBoUDEuX1E\"]},\"contracts/interfaces/tokens/IAmmalgamERC20.sol\":{\"keccak256\":\"0x44a376269170b4270ec221ce3cb31a609b394e216cc4d2e27b818361b4369829\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://c48bc7586631f27ede73d3d0b4c1d7a29b1653e6c501c8b7fc9877c125f8f57e\",\"dweb:/ipfs/QmTSLtqnsxr7h7ct524rqYssHUo4qursmCZ7g5q3J1qQPK\"]},\"contracts/interfaces/tokens/ITokenController.sol\":{\"keccak256\":\"0x7778001aaf582fe10005240eb6023b2b6cee3f100b6c2222bf6b9ade93732624\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://91e5c4519207d6a450be1e0a8649157e86d20f8ef6a91ff6512a31cf5561a570\",\"dweb:/ipfs/QmUqZLW27JJZHFPf2fgLDYSWWj5gM158DdaxTTmDVukRAg\"]},\"contracts/libraries/Convert.sol\":{\"keccak256\":\"0x944776d31291de1a9cdc6a52154c23c22b43a01c3edebe7a4140e267edbba975\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://36c03749859077ba47a3acfd574f8c30f34f97def4ce81d7f4feac9a7b62794c\",\"dweb:/ipfs/QmdycZay5X2WrbS8qS7RycLpZbMQx7yKszWQzGU3rqidpH\"]},\"contracts/libraries/GeometricTWAP.sol\":{\"keccak256\":\"0x3860409daa0fdb5d96f0bfb8b49cbca058b9fe32c8e32457f85d4ee2c5cdcb1e\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://81fe70a80f4005a6529c7e93f1a92547ce0bf74c357c280a91e8778b378b18aa\",\"dweb:/ipfs/QmdRQ1DqsCu11zfbLAbrrzJ9Ups3oKgTGimYo3Zm3ALiCz\"]},\"contracts/libraries/Interest.sol\":{\"keccak256\":\"0xbc8bfa20d7295dd70e3c716fd3dbeb5b45d313e3c609d063d186042cbf000646\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://b015e8d4976d3b6d7eaca07dfcc487aeed3a7d8b4c41c8369a7476dcfb211194\",\"dweb:/ipfs/QmecH84UnZYxDZ2aL6rQtnrEExLEAfo7q4Y47yuBXdymeX\"]},\"contracts/libraries/Liquidation.sol\":{\"keccak256\":\"0x842bc44bc3cff80360ab82c5920070b12680edefe9267bdffc2d6c3c3a692d63\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://85ecd75568a0729aec06741d0575ed07dad8b7daebd7ba3114a93f6019250877\",\"dweb:/ipfs/QmQMvWdsPWsQ4t1yv6eyZy5TM7h1EZpSJdt5b8fDLcumCW\"]},\"contracts/libraries/QuadraticSwapFees.sol\":{\"keccak256\":\"0x00f6b7909be4fa1fc1ba426dd8ae659d1c5cb20c79665148898c973f55cfdccb\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://c64da0826a9b0ffc08319709f6db03339d22d24deda902a6540393251da0aecb\",\"dweb:/ipfs/QmSNwBbn2VAS8HPY4hNZusEc4DoKKZAZHtpPdjL9Gz3gs3\"]},\"contracts/libraries/Saturation.sol\":{\"keccak256\":\"0xf44bc610ece4bc7ebdb0730aa6ad69ea47647e19d4c1944c663d2d2eb4f10860\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://421fdf8d0b27132bc324a42ede9aaf23b476e5134e1073f84e824620a2a44f20\",\"dweb:/ipfs/QmbvSfMuMzDmrfPkCAEp7ydtRDWu5EUiXq4MyrGGjFErzE\"]},\"contracts/libraries/TickMath.sol\":{\"keccak256\":\"0x753813c7ed638d22edb71f48f8eb8b4283b3db2ba5b136b5c8909bd37ffa3f12\",\"license\":\"GPL-2.0-or-later\",\"urls\":[\"bzz-raw://04dd5085b72f6d73e1b17f58148e4d03639f654bdc4fdbc173b7c92ff102fc20\",\"dweb:/ipfs/QmSg4xTQPkngjNxs84428FZdSwH4AUQpwLXaASx7Qev6oG\"]},\"contracts/libraries/Uint16Set.sol\":{\"keccak256\":\"0x26a714430fe1618d78386e953153b4bd2bf024baee54453ec9a7a0cc60e1534f\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://8667dd78541d656a09678e5f9cce4d49adc805955604ccaaec414e9b241f5e06\",\"dweb:/ipfs/QmZVWU2CzyDQfGit32HjJxDphBJMKG3d6JRuxbC682Z1gy\"]},\"contracts/libraries/Validation.sol\":{\"keccak256\":\"0x294848b2af973dbcd8b83732a57b67f14fd15e4af0668de05a2928b8eca5a463\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://fab25c941e87f6924b31e3f20742ca6b5ec1b7e4251543f4a61567a04ef4d778\",\"dweb:/ipfs/Qmf4ChH8afdHc3SfXkFPpNGp3e1hscyvnujPAMza3yuXeA\"]},\"contracts/libraries/constants.sol\":{\"keccak256\":\"0x0dfb294985a8f48287ff13e8476718ddb5334b1d8bf6bfa59a5db1dbcf6ca7c4\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://4bedcfdb2850cfb22b5daa768ab8125b4ccab97c90068d1d0ad4495bf942b362\",\"dweb:/ipfs/Qmf9p88yQN2JYRBR5D7q9BLmwhDJWpFk47ZuayrKqCyHat\"]},\"contracts/tokens/TokenController.sol\":{\"keccak256\":\"0x8b76b9ebb9385f0c4b7c0b8210fb96b11a49a8c9a3a6e855752c32a5c12d54e6\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://de87bdae81940f397136f665d68907d4e4c32f35bf2cd0c9e9305a9fe190d159\",\"dweb:/ipfs/Qmce4hM6xofBYxzAXesHX4hkiHBexoGeQpCzpeCARctnCn\"]},\"lib/mangrove-core/lib/core/BitLib.sol\":{\"keccak256\":\"0x80f6885268986b9e976b424993aa875cf7aab8464403ed675a86ade9e9be5ee3\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5a31b4e1e0dc95de9a1dbb114c40c44814de5db3e2d857c93c2524a61454f6c8\",\"dweb:/ipfs/QmRkgE9ue5rGwE6XDnszF2e2meWqAC9nnKM97xKHjHphQr\"]},\"lib/morpho-blue/src/libraries/MathLib.sol\":{\"keccak256\":\"0xa7354cbbcecef7bc0c94b61061c4e5da75515056b8e2db65e826b00d7369744a\",\"license\":\"GPL-2.0-or-later\",\"urls\":[\"bzz-raw://d7419c59bb906fcfa49320b68f265c3200090e5c30b194766256aee70b012e08\",\"dweb:/ipfs/Qmbo4uaW6XYnudya4bb6RU6riWXFk5M3CWJge5XzTTaEfd\"]},\"lib/openzeppelin-contracts/contracts/access/AccessControl.sol\":{\"keccak256\":\"0xb64ecf1154f183412bcde47168f3af245e4120846346a0b3872c631e361156d2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://85331049a60659bc4489733ccd3cbeb177b65691122a8cb637bf9267ab25e23d\",\"dweb:/ipfs/QmbGpDcdwKTirzSCoZfE4rHG7jBSWsE4K2iSb6UCYXtLJv\"]},\"lib/openzeppelin-contracts/contracts/access/IAccessControl.sol\":{\"keccak256\":\"0x5643a5cadd1278581308b20becb48a50946c159fc31c29fc407ea9a61fc865d1\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://c7d79f305a239207a24fa2174897c8ea8ff1e81cb790d440fd54c89a0e85f63e\",\"dweb:/ipfs/QmT847eeAMnRN3DaG1zsKNMn7qipNAidqv1REnKexPkrfA\"]},\"lib/openzeppelin-contracts/contracts/access/Ownable.sol\":{\"keccak256\":\"0xff6d0bb2e285473e5311d9d3caacb525ae3538a80758c10649a4d61029b017bb\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://8ed324d3920bb545059d66ab97d43e43ee85fd3bd52e03e401f020afb0b120f6\",\"dweb:/ipfs/QmfEckWLmZkDDcoWrkEvMWhms66xwTLff9DDhegYpvHo1a\"]},\"lib/openzeppelin-contracts/contracts/governance/utils/IVotes.sol\":{\"keccak256\":\"0xb541b133f2d85eb37ae866cb21123be93bd3671b6840c47f951da52d3a704548\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://22443f43ece107eaf453aa9a41a59410cece5429c2779c794fbd2c8b5aa29d38\",\"dweb:/ipfs/Qmbum2jwLYuT9aZ2fr9NMLwWFsVavonrGm2VnbAL9hP2jn\"]},\"lib/openzeppelin-contracts/contracts/governance/utils/Votes.sol\":{\"keccak256\":\"0x3f91c79d6f55db9e4fc36e1cfe6a483a7b0f5be60fecbd979555071673746d47\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://9b1e3c64cbeb2757a2a1a45c69f7f3984a93b0eadd1016341b64f9d94f89d7c4\",\"dweb:/ipfs/QmP1Mj14U4vMTFa2rv2nodMbWSCov2ac9Md8W2aUcgYdKX\"]},\"lib/openzeppelin-contracts/contracts/interfaces/IERC1363.sol\":{\"keccak256\":\"0xc15298eb2b9ba5e18a8c9d12f93ad17a3e162a5c1d9b85f54c8adb5827b0d4da\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://1f3c3d8f81d2daf1231890a6a2f897be365d6a479b53dcd52ec2527b5d3faf41\",\"dweb:/ipfs/QmeNdkd6u4at9pd2GAyyqxzrVGGvxfLpGmAKnFoYM5ya2e\"]},\"lib/openzeppelin-contracts/contracts/interfaces/IERC165.sol\":{\"keccak256\":\"0x81b022028c39007cce9920c394b9cddd1cb9f3a1c0398f254b4a6492df92ad2b\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://e0b61b8a5c69b4df993c3d6f94c174ab293aa8698d149bce7be2d88f82929beb\",\"dweb:/ipfs/QmbtacmB1k8ginfrHvAJpjVeqnjYGfXYrkXmMPYEb83z4t\"]},\"lib/openzeppelin-contracts/contracts/interfaces/IERC20.sol\":{\"keccak256\":\"0xb5d81383d40f4006d1ce4bbad0064e7a930e17302cbe2a745e09cb403f042733\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3fc4a5681c2f00f41f49260a36ae6bbe1121dd93d470ea24d51d556eff2980be\",\"dweb:/ipfs/QmUBW6TwVWtGP96ka9TfuGivd27kH8CtkXD8RQAAecSFiR\"]},\"lib/openzeppelin-contracts/contracts/interfaces/IERC3156FlashBorrower.sol\":{\"keccak256\":\"0xad94c8d7246a50210f7bcb54e5b91fc9f1c6e137263ac972ca5dd0f7f6d4d49d\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6938e96fbb0cf3d961788b6c3522400e255d8057d1b9f7e08a50e0b48486b007\",\"dweb:/ipfs/QmNXG3MPzDXjHJ9iWDYCz4vi9RBTgVBnZjndnfBwMfhkyD\"]},\"lib/openzeppelin-contracts/contracts/interfaces/IERC3156FlashLender.sol\":{\"keccak256\":\"0xd92910b862581523ad4e9b99f0bf738f4e62700a5e305953c7fda7db2cfd0f73\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://799f3f0f30822ac806800bbe7fe63b9991490c4e1d9edb75f5993d9350320819\",\"dweb:/ipfs/QmT8T4SokW6YxpDJQiafpeYNRGtjC5gFHxRqKTRXRyP6zB\"]},\"lib/openzeppelin-contracts/contracts/interfaces/IERC5267.sol\":{\"keccak256\":\"0xf29e9088951d8a2074d872a733674618fe5c164df21b8b5cf4a6295f523ba7ad\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://562a1abc7ea505582827ce0c9a2f778360a1a8242742683af179930640020215\",\"dweb:/ipfs/QmPjx5f6KKaPfsDi1uV3ovQN9gHTAcNkMAFJZxE1Adw6VT\"]},\"lib/openzeppelin-contracts/contracts/interfaces/IERC5805.sol\":{\"keccak256\":\"0xc8960b7d3e504e98883de33856a917a473c05034cd61880df2a60b5c47c214fe\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://80542373fa695b68d65b4d7222e852df5bda035a82e86ee559336c93b2bf7051\",\"dweb:/ipfs/QmZgH14DPTnKfA5gMSTMiUa6ExuqFfAozmEtLXiWc1iDiw\"]},\"lib/openzeppelin-contracts/contracts/interfaces/IERC6372.sol\":{\"keccak256\":\"0xa602c8beaae2d9e2ab1ce585a54547a6d4da32d32e4d002d20ccba55b19258d8\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://ac6553b5b07788a0bb67cc53596837d795280233a9a5cb3a9b3e1fde56822f78\",\"dweb:/ipfs/QmVoHXoma4ZbPKVRJJRosvhipa4rtCMU9QQvWHWKiRUxvi\"]},\"lib/openzeppelin-contracts/contracts/interfaces/draft-IERC6093.sol\":{\"keccak256\":\"0x85cf779582f7860b00bba1d259be00e754bfbac3c2339e324d0113d682d9e9f9\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://2ddf369affd106e2a9e2b8a22a6ce3da8a6ccda14c6ba5b8c87f6b08169e6318\",\"dweb:/ipfs/QmNadAttd47ycHShxhk33JUJhrbzmyZQ7mHs7WEyG4Qkmp\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/ERC20.sol\":{\"keccak256\":\"0x5aad1745bddba11752c3962464a3b12e0af079310cc22d1f43f0388ae1aaf8db\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://577fad916bfdfe89aadf2685322fec7562cb0ed87722923085213cd9f85d7b79\",\"dweb:/ipfs/QmSM3J6PjrAUyEoNbdhq1ECZLXczKdCTzZTBUieKHsBYEL\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol\":{\"keccak256\":\"0xfe37358f223eddd65d61bb62b0b7bdb69d7101b5ec8d484292b8c1583a153b8a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://28dd43f30af3c12ae0fc08dd031b1250e906ef3c95f63f30fac6fd15aee2a662\",\"dweb:/ipfs/QmUkSyWsSRx36w1ti7U6qnGnQgJq16wpMhjeJrnyn9AXwG\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20Burnable.sol\":{\"keccak256\":\"0x2659248df25e34000ed214b3dc8da2160bc39874c992b477d9e2b1b3283dc073\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://c345af1b0e7ea28d1216d6a04ab28f5534a5229b9edf9ca3cd0e84950ae58d26\",\"dweb:/ipfs/QmY63jtSrYpLRe8Gj1ep2vMDCKxGNNG3hnNVKBVnrs2nmA\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20FlashMint.sol\":{\"keccak256\":\"0x4d43ed4b9ff9e4c671274976d59a58dbcc7b69bd7ac11b1710f5b7607cf15b74\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://0b47b42998f675cb6a51f2e74ef5906a6fa63ec6718f3fd56ee035d6f77143f9\",\"dweb:/ipfs/QmREnAXqPJBvAwfWfDzaFhNfSRWF4Jdy9ZrpHLw1KdQweY\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20Permit.sol\":{\"keccak256\":\"0x6485b101d7335f0fd25abc996c5e2fc965e72e5fbd0a7ad1a465bd3f012b5fd8\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://1f8d296121044e697bcd34c4cd394cebd46fc30b04ce94fccff37200872e6834\",\"dweb:/ipfs/QmTNdmLdoHgMzoCDZ8Txk9aYvwtuyeJYHf5mjLWgzGTZAu\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20Votes.sol\":{\"keccak256\":\"0x62dc9346044aabf22d78541bd495aa6ca05a7f5100aed26196ba35d40b59fcb5\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5221df4501c74cd4493fee1a0f0788e02c4dc78c3c601e9f557f557c5a53ea92\",\"dweb:/ipfs/QmZpzyYY9dKLrgvYhXSHT93jwqb1UGvtGNMQk5dpECY5pa\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol\":{\"keccak256\":\"0xbaffa0bcc92adf28a53cc3b68551fc3632cb8f849a0028cb8d5c06e4677715e9\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://32e6f8f6b2e883c85e6a602c0882d9962ce2f92406961244e86cd974df815912\",\"dweb:/ipfs/Qmahvx6fPpecicq1aUE1JihCxV5ep1bfuPukzrxa8Ub5PS\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Permit.sol\":{\"keccak256\":\"0x093f32ab700c2b05373387263915a75f5455cdb0f09a7630cc621e27b7b50d04\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://d163e6ef21df143969df5557305e8c643a135c7660a678d0c65dca91772114a0\",\"dweb:/ipfs/QmTZUgiwEro5oLRhbJ2iSWyCqu1JTDekoFHALVUn4eHqYK\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol\":{\"keccak256\":\"0x1183b415155c1a7bf56d45edad5b17caf0da70935ac420698cbe8afb6750cbb2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://21d9edaeb3e5e8f93eb0fdab41530654e8169b1990b3bbfcf5e4527c52aa03f5\",\"dweb:/ipfs/QmWrqpNW3x5k3pTjvrT8XU1hauHnXTjqaPL2tfzMuWYosj\"]},\"lib/openzeppelin-contracts/contracts/utils/Context.sol\":{\"keccak256\":\"0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12\",\"dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF\"]},\"lib/openzeppelin-contracts/contracts/utils/Nonces.sol\":{\"keccak256\":\"0x0082767004fca261c332e9ad100868327a863a88ef724e844857128845ab350f\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://132dce9686a54e025eb5ba5d2e48208f847a1ec3e60a3e527766d7bf53fb7f9e\",\"dweb:/ipfs/QmXn1a2nUZMpu2z6S88UoTfMVtY2YNh86iGrzJDYmMkKeZ\"]},\"lib/openzeppelin-contracts/contracts/utils/Panic.sol\":{\"keccak256\":\"0xf7fe324703a64fc51702311dc51562d5cb1497734f074e4f483bfb6717572d7a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://c6a5ff4f9fd8649b7ee20800b7fa387d3465bd77cf20c2d1068cd5c98e1ed57a\",\"dweb:/ipfs/QmVSaVJf9FXFhdYEYeCEfjMVHrxDh5qL4CGkxdMWpQCrqG\"]},\"lib/openzeppelin-contracts/contracts/utils/Pausable.sol\":{\"keccak256\":\"0xdb484371dfbb848cb6f5d70464e9ac9b2900e4164ead76bbce4fef0b44bcc68f\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f9d6f6f6600a2bec622f699081b58350873b5e63ce05464d17d674a290bb8a7c\",\"dweb:/ipfs/QmQKVzSQY1PM3Bid4QhgVVZyx6B4Jx7XgaQzLKHj38vJz8\"]},\"lib/openzeppelin-contracts/contracts/utils/ShortStrings.sol\":{\"keccak256\":\"0x1fcf8cceb1a67e6c8512267e780933c4a3f63ef44756e6c818fda79be51c8402\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://617d7d57f6f9cd449068b4d23daf485676d083aae648e038d05eb3a13291de35\",\"dweb:/ipfs/QmPADWPiGaSzZDFNpFEUx4ZPqhzPkYncBpHyTfAGcfsqzy\"]},\"lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol\":{\"keccak256\":\"0xcf74f855663ce2ae00ed8352666b7935f6cddea2932fdf2c3ecd30a9b1cd0e97\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://9f660b1f351b757dfe01438e59888f31f33ded3afcf5cb5b0d9bf9aa6f320a8b\",\"dweb:/ipfs/QmarDJ5hZEgBtCmmrVzEZWjub9769eD686jmzb2XpSU1cM\"]},\"lib/openzeppelin-contracts/contracts/utils/Strings.sol\":{\"keccak256\":\"0x1402d9ac66fbca0a2b282cd938f01f3cd5fb1e4c696ed28b37839401674aef52\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://d3e6c46b6d1ea36bd73e0ac443a53504089167b98baa24923d702a865a38d211\",\"dweb:/ipfs/QmdutUpr5KktmvgtqG2v96Bo8nVKLJ3PgPedxbsRD42CuQ\"]},\"lib/openzeppelin-contracts/contracts/utils/cryptography/ECDSA.sol\":{\"keccak256\":\"0x6c29257484c0595ca5af8844fafe99cc5eace7447c9f5bced71d6b3a19a6a2a5\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://cce7ac0bdb05f73c0918e362dea2e52426e00ddf0a1018f14accdcf78c6eb6e4\",\"dweb:/ipfs/QmbkNq5dDxww27FzFFiKgW3S7C5VoZpjdZGpSCtsb9hP32\"]},\"lib/openzeppelin-contracts/contracts/utils/cryptography/EIP712.sol\":{\"keccak256\":\"0xda8013da608bda3c9eaa9e59053d38d7888e64bb40aa557e5929cd702f8de87e\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3ea13234c6b00ae79dc1a98e7e7f2faf38d37e76a687ccd0c95ad84b03ea570f\",\"dweb:/ipfs/QmWtdefDm5jiEzAjmfPMZ5B1NKVxFoMiD5ZoD68hcNTHun\"]},\"lib/openzeppelin-contracts/contracts/utils/cryptography/MessageHashUtils.sol\":{\"keccak256\":\"0x26670fef37d4adf55570ba78815eec5f31cb017e708f61886add4fc4da665631\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b16d45febff462bafd8a5669f904796a835baf607df58a8461916d3bf4f08c59\",\"dweb:/ipfs/QmU2eJFpjmT4vxeJWJyLeQb8Xht1kdB8Y6MKLDPFA9WPux\"]},\"lib/openzeppelin-contracts/contracts/utils/introspection/ERC165.sol\":{\"keccak256\":\"0x41ddfafe0d00dc22e35119d41cb0ca93673960689d35710fd12875139e64bd9f\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://49d90142e15cdc4ca00de16e1882fa0a0daad8b46403628beb90c67a3efe4fc4\",\"dweb:/ipfs/QmNizYnFNcGixHxsknEccr2cQWyyQBqFF7h2bXLmefQz6M\"]},\"lib/openzeppelin-contracts/contracts/utils/introspection/IERC165.sol\":{\"keccak256\":\"0x4ee0e04cc52827588793a141d5efb9830f179a17e80867cc332b3a30ceb30fd9\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://17d8f47fce493b34099ed9005c5aee3012488f063cfe1c34ed8f9e6fc3d576e5\",\"dweb:/ipfs/QmZco2GbZZhEMvG3BovyoGMAFKvfi2LhfNGQLn283LPrXf\"]},\"lib/openzeppelin-contracts/contracts/utils/math/Math.sol\":{\"keccak256\":\"0x1225214420c83ebcca88f2ae2b50f053aaa7df7bd684c3e878d334627f2edfc6\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6c5fab4970634f9ab9a620983dc1c8a30153981a0b1a521666e269d0a11399d3\",\"dweb:/ipfs/QmVRnBC575MESGkEHndjujtR7qub2FzU9RWy9eKLp4hPZB\"]},\"lib/openzeppelin-contracts/contracts/utils/math/SafeCast.sol\":{\"keccak256\":\"0x195533c86d0ef72bcc06456a4f66a9b941f38eb403739b00f21fd7c1abd1ae54\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b1d578337048cad08c1c03041cca5978eff5428aa130c781b271ad9e5566e1f8\",\"dweb:/ipfs/QmPFKL2r9CBsMwmUqqdcFPfHZB2qcs9g1HDrPxzWSxomvy\"]},\"lib/openzeppelin-contracts/contracts/utils/math/SignedMath.sol\":{\"keccak256\":\"0xb1970fac7b64e6c09611e6691791e848d5e3fe410fa5899e7df2e0afd77a99e3\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://db5fbb3dddd8b7047465b62575d96231ba8a2774d37fb4737fbf23340fabbb03\",\"dweb:/ipfs/QmVUSvooZKEdEdap619tcJjTLcAuH6QBdZqAzWwnAXZAWJ\"]},\"lib/openzeppelin-contracts/contracts/utils/structs/Checkpoints.sol\":{\"keccak256\":\"0x743aa2d21f6c26885e0aa6a1c84f7f7bc58fbd6df6bab32bed23f1a41f50454a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://a651d38b4261840d3744e571edf2b59455352a8c7dac5d35b019afefa343ea3b\",\"dweb:/ipfs/QmSy3UkTCQDYTjKtGwtqPRrXaofcqtVZxaF6j1dV44wqvr\"]},\"lib/openzeppelin-contracts/contracts/utils/types/Time.sol\":{\"keccak256\":\"0x36776530f012618bc7526ceb28e77b85e582cb12d9b9466a71d4bd6bf952e4cc\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://9f867d046908497287d8a67643dd5d7e38c4027af4ab0a74ffbe1d6790c383c6\",\"dweb:/ipfs/QmQ7s9gMP1nkwThFmoDifnGgpUMsMe5q5ZrAxGDsNnRGza\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/Base.sol\":{\"keccak256\":\"0x4ff1a785311017d1eedb1b4737956fa383067ad34eb439abfec1d989754dde1c\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f553622969b9fdb930246704a4c10dfaee6b1a4468c142fa7eb9dc292a438224\",\"dweb:/ipfs/QmcxqHnqdQsMVtgsfH9VNLmZ3g7GhgNagfq7yvNCDcCHFK\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/StdAssertions.sol\":{\"keccak256\":\"0x4584f551c3a875c26423c1e50a77179bc2f9c9c1ee6d0f0c6be0f89ca5ee8270\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://ef118876a862b60ba8a6612b8b3f3a6c226e11a0071a2e9695f021586d1bfdbe\",\"dweb:/ipfs/QmXd2vD91PkvoFWasFkgbDi89PctmgsHVxGvGNyhjmRK2b\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/StdChains.sol\":{\"keccak256\":\"0xcd3e64ec9ffa19a2c0715bbdaf7ddf28887cc418e079bec4373fd6a3f9961a7b\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://e981a2ab738590928e9efa5f3d95a408c718eb12d73a113d7675f3ed55a026a1\",\"dweb:/ipfs/QmTgSEkWWsBRy32goRCaUkraSgpZHtgbZoKC3iEFNz5RDc\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/StdCheats.sol\":{\"keccak256\":\"0x0fa6ec03602648b62cce41aab2096e6b7e052f2846075d967b6958dd586db746\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://cd84e2ca9c1eaed6b76768cc12bb8c1af8289170ea8b7706f58d516460d79c41\",\"dweb:/ipfs/QmQ7BK7co6DE4eWUqMyv11s5eHYkS1tyx8tDSZGZVtf2aK\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/StdError.sol\":{\"keccak256\":\"0xbf477b11a42d7611696956546bcfaa29317d1166bf65e402344599c05943fc77\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://bc2e117d1135e030862b96a6526a43feb38d396cc79857f1fb696d4eff0e5fd6\",\"dweb:/ipfs/QmdSuQ5RrQudTLsNmWXGEeVJX8gR5U9XPm6m4dwwuQnJrj\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/StdInvariant.sol\":{\"keccak256\":\"0x4dab3021edfa9511bbdd80c48f060ef62eaf457f99eaf841f561fc2557d9a08d\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://07668628673174cf8f27f8f4e1f862bab775013ec247eb34f698c5005f229391\",\"dweb:/ipfs/QmPJsiiYwmCZXMsHmQv5tg8VF3CAhNdat7WnKLTWZZH2v5\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/StdJson.sol\":{\"keccak256\":\"0xbc0132abe1c2accc2867c0f03667afffdf92f3e95a581bb03c9557eaa38ea500\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://eb6fab37dc73c219cfbb7b4f4998bcf7677ca5397a867e850f40232192073974\",\"dweb:/ipfs/QmUHsbVdp9SKmgek7ZfPcLTKrpZFXpqaqt4sVejzxGEQL3\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/StdMath.sol\":{\"keccak256\":\"0xd90ad4fd8aeaeb8929964e686e769fdedd5eded3fc3815df194a0ab9f91a3fb2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://7919b70f636c7b805223992f28ad1ad0145d6c1385b5931a3589aface5fe6c92\",\"dweb:/ipfs/QmY7FRaULwoGgFteF8GawjQJRfasNgpWnU2aiMsFrYpuTC\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/StdStorage.sol\":{\"keccak256\":\"0xb91ab24383a5872b894fc93325eef1add6cbbf981628f18e860068bf88bb7dcc\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://2651e33b9ac8fc5803ed0a43078c1bf4fa90a0b0347aafd95776b02daccdcc57\",\"dweb:/ipfs/QmbdRyPuGJdZgnCuMnp7c1WsBo1Spf7j1KMmws1Z5rd4BQ\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/StdStyle.sol\":{\"keccak256\":\"0x43e2a8a9b9c2574dabe74f11adf6f782df218f463540e3b5b563609fe108597d\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://51363ca97404cf4128e1141428949768c31929e75e014b02c85e887fbbb4f1b8\",\"dweb:/ipfs/QmVhtbQc2fU4rRmbcfBtz34mAgG4BAZBsbna1Ca4SkoPsK\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/StdToml.sol\":{\"keccak256\":\"0x58a72c765ed3f7ff6b105509689658795b8a3739b8931772a497155878381861\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b4a3746f4fabaeb980bd77d9e091d3904ee38a6c0e191bfa8ba6874c6f8558a3\",\"dweb:/ipfs/QmUfFDMEn461FgGEXt5HicyGD54sc28sLaQ9JRWDMBKed8\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/StdUtils.sol\":{\"keccak256\":\"0x7274081e11c05164fd8eadde4de8305c033e58a43008dea58065f3170ccf0737\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://e9801614b6c9d3e472982e6cc68f5f1ad03682f84eafb686be65633c7132d138\",\"dweb:/ipfs/QmcQSUcrm2A7XuektnxJjvYGmZtBeA6LKPxCXRqdXBX776\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/Test.sol\":{\"keccak256\":\"0x3b4bb409a156dee9ce261458117fe9f81080ca844a8a26c07c857c46d155effe\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5792c69fe24bdc063a14e08fe68275007fdb1e5e7e343840a77938cb7e95a64e\",\"dweb:/ipfs/QmcAMhaurUwzhytJFYix4vRNeZeV8g27b8LnV3t7dvYtiK\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/Vm.sol\":{\"keccak256\":\"0x44bfadcf5a89b8058f80258f2259585c740f9cc45669a0579f4f2753ff2c6354\",\"license\":\"MIT OR Apache-2.0\",\"urls\":[\"bzz-raw://bbc366c8b3499d5030e3b2e45bac23770531f2f5243a0e80e3d5a66b6f9a312c\",\"dweb:/ipfs/QmNxDEB3BaVnKzNaWedtdMshhvCEddB1AsdJZcsQx6jdtC\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/console.sol\":{\"keccak256\":\"0x4bbf47eb762cef93729d6ef15e78789957147039b113e5d4df48e3d3fd16d0f5\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://af9e3a7c3d82fb5b10b57ca4d1a82f2acbef80c077f6f6ef0cc0187c7bfd9f57\",\"dweb:/ipfs/QmR9VzmnBDJpgiDP6CHT6truehukF9HpYvuP6kRiJbDwPP\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/console2.sol\":{\"keccak256\":\"0x3b8fe79f48f065a4e4d35362171304a33784c3a90febae5f2787805a438de12f\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://61de63af08803549299e68b6e6e88d40f3c5afac450e4ee0a228c66a61ba003d\",\"dweb:/ipfs/QmWVoQ5rrVxnczD4ZZoPbD4PC9Z3uExJtzjD4awTqd14MZ\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/interfaces/IMulticall3.sol\":{\"keccak256\":\"0x7aac1389150499a922d1f9ef5749c908cef127cb2075b92fa17e9cb611263d0a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://d95ebb7c7c463e08ebc12dab639945752fb2480acfc6e86da32f72732a7fd0c0\",\"dweb:/ipfs/QmNXK8P8oPWwajsQHvAHw3JPyQidPLCGQN3hWu1Lk6PBL2\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/safeconsole.sol\":{\"keccak256\":\"0xbef9786cb49d3eade757bad87568c49c8c8f35721f0193c95ffb055d9e466e11\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3bafd2b0b2d28068d329f95ea8a1fbce3719c257fcb863fc01abcbafd8d531ab\",\"dweb:/ipfs/QmUeaFjKWTVDBsHVfSob4mwt6A5hTnKDz22HaUXeZhypa3\"]},\"test/shared/GeometricTWAPTestFixture.sol\":{\"keccak256\":\"0x66a316a13c74698cbb99bd7f5681b6879eb300c7c2331095458be9d6b39bc386\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://cb294f187ac85de17d052e9cc9e91bfbf13c1d9d50fcaa8231fd0e3c9e4be280\",\"dweb:/ipfs/QmbZyjygUHe6Ss5sZrHjUm5jRBLSCLqCFRURjkAsGWaupu\"]},\"test/shared/StubErc20.sol\":{\"keccak256\":\"0xf3508dc98ae444d142d9993c52cebd856aba40c3e53d64bfeb63e71d190b12ee\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://0cc01d254b6d5569d1cb426250db9df1b01afde9dd7b52e1efa0691112fcd918\",\"dweb:/ipfs/QmPnL9wFpSKXprrEFS9kkC2WzK2kAgWSH1snom1wiorCxn\"]},\"test/shared/utilities.sol\":{\"keccak256\":\"0xc64b147bbe73bf59fdec4202c5b7c5dbcadd7550f4b2ea2390ea689e194d7cb8\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://ab03a14b75d4b9df7795eeefd7e6d4a1d7af7b58ce948741cdd5d056a2c30416\",\"dweb:/ipfs/QmShemddxGaLyTGtC3yLdMtdHf9Gj3H8rjf2umzbFmP6aG\"]},\"test/utils/DepletedAssetUtils.sol\":{\"keccak256\":\"0x2273187d5eb782fb341d44265bd6e8afcef18ab3cfabcb4a0b77a75f15298c42\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://04d0a060b217603f6c7e38efc49be20932f08c56e46b1f9cea54eb722595458e\",\"dweb:/ipfs/QmdJHgaFbbtGDHPpHEFucTvrj4p4LT1piMPjtbrWBMXzAR\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "uint24", "name": "midTermIntervalConfig", "type": "uint24"}, {"internalType": "uint24", "name": "longTermIntervalConfigFactor", "type": "uint24"}, {"internalType": "int16", "name": "firstTick", "type": "int16"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "type": "error", "name": "InvalidIntervalConfig"}, {"inputs": [{"internalType": "uint8", "name": "bits", "type": "uint8"}, {"internalType": "int256", "name": "value", "type": "int256"}], "type": "error", "name": "SafeCastOverflowedIntDowncast"}, {"inputs": [{"internalType": "int16", "name": "lendingStateTick", "type": "int16", "indexed": false}], "type": "event", "name": "UpdateLendingTick", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "", "type": "string", "indexed": false}], "type": "event", "name": "log", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "", "type": "address", "indexed": false}], "type": "event", "name": "log_address", "anonymous": false}, {"inputs": [{"internalType": "uint256[]", "name": "val", "type": "uint256[]", "indexed": false}], "type": "event", "name": "log_array", "anonymous": false}, {"inputs": [{"internalType": "int256[]", "name": "val", "type": "int256[]", "indexed": false}], "type": "event", "name": "log_array", "anonymous": false}, {"inputs": [{"internalType": "address[]", "name": "val", "type": "address[]", "indexed": false}], "type": "event", "name": "log_array", "anonymous": false}, {"inputs": [{"internalType": "bytes", "name": "", "type": "bytes", "indexed": false}], "type": "event", "name": "log_bytes", "anonymous": false}, {"inputs": [{"internalType": "bytes32", "name": "", "type": "bytes32", "indexed": false}], "type": "event", "name": "log_bytes32", "anonymous": false}, {"inputs": [{"internalType": "int256", "name": "", "type": "int256", "indexed": false}], "type": "event", "name": "log_int", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "address", "name": "val", "type": "address", "indexed": false}], "type": "event", "name": "log_named_address", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "uint256[]", "name": "val", "type": "uint256[]", "indexed": false}], "type": "event", "name": "log_named_array", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "int256[]", "name": "val", "type": "int256[]", "indexed": false}], "type": "event", "name": "log_named_array", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "address[]", "name": "val", "type": "address[]", "indexed": false}], "type": "event", "name": "log_named_array", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "bytes", "name": "val", "type": "bytes", "indexed": false}], "type": "event", "name": "log_named_bytes", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "bytes32", "name": "val", "type": "bytes32", "indexed": false}], "type": "event", "name": "log_named_bytes32", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "int256", "name": "val", "type": "int256", "indexed": false}, {"internalType": "uint256", "name": "decimals", "type": "uint256", "indexed": false}], "type": "event", "name": "log_named_decimal_int", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "uint256", "name": "val", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "decimals", "type": "uint256", "indexed": false}], "type": "event", "name": "log_named_decimal_uint", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "int256", "name": "val", "type": "int256", "indexed": false}], "type": "event", "name": "log_named_int", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "string", "name": "val", "type": "string", "indexed": false}], "type": "event", "name": "log_named_string", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "uint256", "name": "val", "type": "uint256", "indexed": false}], "type": "event", "name": "log_named_uint", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "", "type": "string", "indexed": false}], "type": "event", "name": "log_string", "anonymous": false}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256", "indexed": false}], "type": "event", "name": "log_uint", "anonymous": false}, {"inputs": [{"internalType": "bytes", "name": "", "type": "bytes", "indexed": false}], "type": "event", "name": "logs", "anonymous": false}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "IS_TEST", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "uint24", "name": "longTermIntervalConfig", "type": "uint24"}], "stateMutability": "nonpayable", "type": "function", "name": "changeInterval"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "excludeArtifacts", "outputs": [{"internalType": "string[]", "name": "excludedArtifacts_", "type": "string[]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "excludeContracts", "outputs": [{"internalType": "address[]", "name": "excludedContracts_", "type": "address[]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "excludeSelectors", "outputs": [{"internalType": "struct StdInvariant.FuzzSelector[]", "name": "excludedSelectors_", "type": "tuple[]", "components": [{"internalType": "address", "name": "addr", "type": "address"}, {"internalType": "bytes4[]", "name": "selectors", "type": "bytes4[]"}]}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "excludeSenders", "outputs": [{"internalType": "address[]", "name": "excludedSenders_", "type": "address[]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "failed", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "uint256", "name": "blocks", "type": "uint256"}, {"internalType": "int16", "name": "tick", "type": "int16"}], "stateMutability": "nonpayable", "type": "function", "name": "generateObservations"}, {"inputs": [{"internalType": "uint256", "name": "blocks", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "generatePastBlocks", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "uint32", "name": "timeElapsedSinceUpdate", "type": "uint32"}, {"internalType": "uint32", "name": "timeElapsedSinceLendingUpdate", "type": "uint32"}], "stateMutability": "nonpayable", "type": "function", "name": "getLendingStateTickAndCheckpoint", "outputs": [{"internalType": "int16", "name": "tick", "type": "int16"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "getObservationStruct", "outputs": [{"internalType": "struct GeometricTWAP.Observations", "name": "", "type": "tuple", "components": [{"internalType": "bool", "name": "isMidTermBufferInitialized", "type": "bool"}, {"internalType": "bool", "name": "isLongTermBufferInitialized", "type": "bool"}, {"internalType": "uint8", "name": "midTermIndex", "type": "uint8"}, {"internalType": "uint8", "name": "longTermIndex", "type": "uint8"}, {"internalType": "int16", "name": "lastTick", "type": "int16"}, {"internalType": "int16", "name": "lastLendingStateTick", "type": "int16"}, {"internalType": "uint24", "name": "midTermIntervalConfig", "type": "uint24"}, {"internalType": "uint24", "name": "longTermIntervalConfig", "type": "uint24"}, {"internalType": "int56", "name": "lendingCumulativeSum", "type": "int56"}, {"internalType": "int56[51]", "name": "midTermCumulativeSum", "type": "int56[51]"}, {"internalType": "int56[9]", "name": "longTermCumulativeSum", "type": "int56[9]"}, {"internalType": "uint32[51]", "name": "midTermTimeInterval", "type": "uint32[51]"}, {"internalType": "uint32[9]", "name": "longTermTimeInterval", "type": "uint32[9]"}]}]}, {"inputs": [{"internalType": "bool", "name": "isLongTermBufferInitialized", "type": "bool"}], "stateMutability": "view", "type": "function", "name": "getObservedTicks", "outputs": [{"internalType": "int16", "name": "longTermTick", "type": "int16"}, {"internalType": "int16", "name": "midTermTick", "type": "int16"}, {"internalType": "int16", "name": "lastTick", "type": "int16"}]}, {"inputs": [{"internalType": "int16", "name": "currentTick", "type": "int16"}], "stateMutability": "view", "type": "function", "name": "getTicks", "outputs": [{"internalType": "int16", "name": "minTick", "type": "int16"}, {"internalType": "int16", "name": "maxTick", "type": "int16"}, {"internalType": "int16", "name": "lastTick", "type": "int16"}]}, {"inputs": [{"internalType": "int16", "name": "a", "type": "int16"}, {"internalType": "int16", "name": "b", "type": "int16"}], "stateMutability": "pure", "type": "function", "name": "intMax", "outputs": [{"internalType": "int16", "name": "", "type": "int16"}]}, {"inputs": [{"internalType": "int16", "name": "a", "type": "int16"}, {"internalType": "int16", "name": "b", "type": "int16"}], "stateMutability": "pure", "type": "function", "name": "intMin", "outputs": [{"internalType": "int16", "name": "", "type": "int16"}]}, {"inputs": [{"internalType": "uint256", "name": "blockStep", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "mineBlock"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "obs", "outputs": [{"internalType": "bool", "name": "isMidTermBufferInitialized", "type": "bool"}, {"internalType": "bool", "name": "isLongTermBufferInitialized", "type": "bool"}, {"internalType": "uint8", "name": "midTermIndex", "type": "uint8"}, {"internalType": "uint8", "name": "longTermIndex", "type": "uint8"}, {"internalType": "int16", "name": "lastTick", "type": "int16"}, {"internalType": "int16", "name": "lastLendingStateTick", "type": "int16"}, {"internalType": "uint24", "name": "midTermIntervalConfig", "type": "uint24"}, {"internalType": "uint24", "name": "longTermIntervalConfig", "type": "uint24"}, {"internalType": "int56", "name": "lendingCumulativeSum", "type": "int56"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetArtifactSelectors", "outputs": [{"internalType": "struct StdInvariant.FuzzArtifactSelector[]", "name": "targetedArtifactSelectors_", "type": "tuple[]", "components": [{"internalType": "string", "name": "artifact", "type": "string"}, {"internalType": "bytes4[]", "name": "selectors", "type": "bytes4[]"}]}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetArtifacts", "outputs": [{"internalType": "string[]", "name": "targetedArtifacts_", "type": "string[]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetContracts", "outputs": [{"internalType": "address[]", "name": "targetedContracts_", "type": "address[]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetInterfaces", "outputs": [{"internalType": "struct StdInvariant.FuzzInterface[]", "name": "targetedInterfaces_", "type": "tuple[]", "components": [{"internalType": "address", "name": "addr", "type": "address"}, {"internalType": "string[]", "name": "artifacts", "type": "string[]"}]}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetSelectors", "outputs": [{"internalType": "struct StdInvariant.FuzzSelector[]", "name": "targetedSelectors_", "type": "tuple[]", "components": [{"internalType": "address", "name": "addr", "type": "address"}, {"internalType": "bytes4[]", "name": "selectors", "type": "bytes4[]"}]}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetSenders", "outputs": [{"internalType": "address[]", "name": "targetedSenders_", "type": "address[]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "timestamp", "outputs": [{"internalType": "uint32", "name": "", "type": "uint32"}]}, {"inputs": [{"internalType": "uint256", "name": "duration", "type": "uint256"}, {"internalType": "uint24", "name": "interval", "type": "uint24"}], "stateMutability": "view", "type": "function", "name": "verifyIndexes"}, {"inputs": [{"internalType": "int16", "name": "longTermTick", "type": "int16"}, {"internalType": "int16", "name": "midTermTick", "type": "int16"}, {"internalType": "int16", "name": "_blockTick", "type": "int16"}, {"internalType": "int16", "name": "_currentTick", "type": "int16"}], "stateMutability": "view", "type": "function", "name": "verifyTicks"}, {"inputs": [{"internalType": "int16", "name": "tick", "type": "int16"}], "stateMutability": "view", "type": "function", "name": "verifyTicks"}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["1inch/=lib/1inch/", "@1inch/=lib/1inch/", "@mangrovedao/mangrove-core/=lib/mangrove-core/", "@mgv/forge-std/=lib/mangrove-core/lib/forge-std/src/", "@mgv/lib/=lib/mangrove-core/lib/", "@mgv/script/=lib/mangrove-core/script/", "@mgv/src/=lib/mangrove-core/src/", "@mgv/test/=lib/mangrove-core/test/", "@morpho-org/morpho-blue/=lib/morpho-blue/", "@openzeppelin/=lib/openzeppelin-contracts/", "ExcessivelySafeCall/=lib/ExcessivelySafeCall/src/", "core/=lib/mangrove-core/lib/core/", "ds-test/=lib/ExcessivelySafeCall/lib/ds-test/src/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "forge-std/=lib/openzeppelin-contracts/lib/forge-std/src/", "halmos-cheatcodes/=lib/morpho-blue/lib/halmos-cheatcodes/src/", "mangrove-core/=lib/mangrove-core/", "morpho-blue/=lib/morpho-blue/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "preprocessed/=lib/mangrove-core/lib/preprocessed/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"test/shared/GeometricTWAPTestFixture.sol": "GeometricTWAPTestFixture"}, "evmVersion": "cancun", "libraries": {}}, "sources": {"contracts/AmmalgamPair.sol": {"keccak256": "0xe8f98844a55a216605e6c6dd6837977fafda458a6b5d0cfa1f3a18d25e9432e4", "urls": ["bzz-raw://65dda1a1de8dd64e31c666b13de3d0583b4b0da923c67065cadcddefe47562a2", "dweb:/ipfs/Qmaev9WFa4yyL8fXVoWkXwNsTTY8wY7jTBGDoKJbdwSCzS"], "license": null}, "contracts/SaturationAndGeometricTWAPState.sol": {"keccak256": "0x5e293a35668bb216a99379ea2176894314cc0f1ac68644fcf4c07017da1a4419", "urls": ["bzz-raw://00349bb86f1b657010919b4bc3f616ad56ef4883b99ab0eead36815dae93dc76", "dweb:/ipfs/QmbEd9GD2JxuDntX35YcfbSCcpRstDU9GDPUkBKGzsxvqE"], "license": "GPL-3.0-only"}, "contracts/factories/AmmalgamFactory.sol": {"keccak256": "0xe0d9baf63d9538a7ecb8bd24ea61a8cdf6fc9c1e9eb028f343548adeb8b93e4e", "urls": ["bzz-raw://bfca24db47fbbaeef8bc36996cbfed78eb48771ac71d2800f081fb58a8e8c92b", "dweb:/ipfs/QmUfYEwfmrjSmchqXi58SnpSina4qKQvD2Jbk5RqYsaoCa"], "license": "GPL-3.0-only"}, "contracts/interfaces/IAmmalgamPair.sol": {"keccak256": "0xa17e45b2348d8920d9970c5d50b300fc0a1e8d03350cdd0d1a624494baa70337", "urls": ["bzz-raw://8d252e89e5d49d1c15a0c0c0a495a325b9f8d608714b29279a7bacb1e4bf8795", "dweb:/ipfs/QmRkZ7a8JJQYEw6HQMJjjkuAK8b5Th1X1ET6BG1R8mx4qw"], "license": "GPL-3.0-only"}, "contracts/interfaces/ISaturationAndGeometricTWAPState.sol": {"keccak256": "0xc9add2ad41f8edd9d360ced8d2cd7bd18dd500304794434fb2e309fa0f5af83c", "urls": ["bzz-raw://8ecc810c544ac734ef26a2f6bebea3f3bd12d773965d297991e0e0e72892fa20", "dweb:/ipfs/QmarXc1Ut4FZzPRRZs2M2udbJjuZUJQHQ8fvmSr3bpHErR"], "license": "GPL-3.0-only"}, "contracts/interfaces/callbacks/IAmmalgamCallee.sol": {"keccak256": "0x904b858859d460a61c9e644ca87009d8e32ba20482ef218801c89c7fb1ece339", "urls": ["bzz-raw://1a7cedebbacc453e3e4e339fcc76fd3268247c13982de82b4930d59a44256c1d", "dweb:/ipfs/QmdjdvYabbwAYcV72xjiXyq278xQivFtiqD3eQ5P9Gk4f1"], "license": "GPL-3.0-only"}, "contracts/interfaces/callbacks/ITransferValidator.sol": {"keccak256": "0x6d9028fc4ad1914e6b2091e6ba46a9f836f9e67ea435c4a8fef41363f2ceaf56", "urls": ["bzz-raw://7ecaade4884d460168f6978edf35706f7b9e363de6002942b1d171a338dca6a4", "dweb:/ipfs/QmS5wgfDt5Pn68rpCytpzhiy57LcmivVFQ5XLGXUUP5Tt8"], "license": "GPL-3.0-only"}, "contracts/interfaces/factories/IAmmalgamFactory.sol": {"keccak256": "0x1c80089901e8d7d7451775b5eaa92092eb2b65319cb92fa7884281bae49f52b8", "urls": ["bzz-raw://bf1201399bb9d5046e0e788ff88394b2468771096a2a0d3500af542923e84628", "dweb:/ipfs/QmeUaPyQpbPbP5fyPUT2FfzeDgHcdyQAn1DaNg9uCuGoj9"], "license": "GPL-3.0-only"}, "contracts/interfaces/factories/IFactoryCallback.sol": {"keccak256": "0x33250cf8351adb4846a3d133a9bc06568288e4c680bcf5b1085e3bca40a35e52", "urls": ["bzz-raw://5663a39af4ed3040a58beaa5641425b9adca83c2683dd220e0c11e644fefe52b", "dweb:/ipfs/QmYB8Vf37WDzQfSpMDjv8hVicuaF1wMBzf7xjHRjGCy3wT"], "license": "GPL-3.0-only"}, "contracts/interfaces/factories/INewTokensFactory.sol": {"keccak256": "0x3b2f1ee34106d2694a9ebbe600be692bed645f4247f4a24da3d5ec46025ab3e9", "urls": ["bzz-raw://73143452a06db52deb593585fea6f2ef7c46e9ef6d649562dc39e79e4e5dca2b", "dweb:/ipfs/QmYQEy7BZWnfWKnuac8GB4QPhG5qJpaHQAfkTBoUDEuX1E"], "license": "GPL-3.0-only"}, "contracts/interfaces/tokens/IAmmalgamERC20.sol": {"keccak256": "0x44a376269170b4270ec221ce3cb31a609b394e216cc4d2e27b818361b4369829", "urls": ["bzz-raw://c48bc7586631f27ede73d3d0b4c1d7a29b1653e6c501c8b7fc9877c125f8f57e", "dweb:/ipfs/QmTSLtqnsxr7h7ct524rqYssHUo4qursmCZ7g5q3J1qQPK"], "license": "GPL-3.0-only"}, "contracts/interfaces/tokens/ITokenController.sol": {"keccak256": "0x7778001aaf582fe10005240eb6023b2b6cee3f100b6c2222bf6b9ade93732624", "urls": ["bzz-raw://91e5c4519207d6a450be1e0a8649157e86d20f8ef6a91ff6512a31cf5561a570", "dweb:/ipfs/QmUqZLW27JJZHFPf2fgLDYSWWj5gM158DdaxTTmDVukRAg"], "license": "GPL-3.0-only"}, "contracts/libraries/Convert.sol": {"keccak256": "0x944776d31291de1a9cdc6a52154c23c22b43a01c3edebe7a4140e267edbba975", "urls": ["bzz-raw://36c03749859077ba47a3acfd574f8c30f34f97def4ce81d7f4feac9a7b62794c", "dweb:/ipfs/QmdycZay5X2WrbS8qS7RycLpZbMQx7yKszWQzGU3rqidpH"], "license": "GPL-3.0-only"}, "contracts/libraries/GeometricTWAP.sol": {"keccak256": "0x3860409daa0fdb5d96f0bfb8b49cbca058b9fe32c8e32457f85d4ee2c5cdcb1e", "urls": ["bzz-raw://81fe70a80f4005a6529c7e93f1a92547ce0bf74c357c280a91e8778b378b18aa", "dweb:/ipfs/QmdRQ1DqsCu11zfbLAbrrzJ9Ups3oKgTGimYo3Zm3ALiCz"], "license": "GPL-3.0-only"}, "contracts/libraries/Interest.sol": {"keccak256": "0xbc8bfa20d7295dd70e3c716fd3dbeb5b45d313e3c609d063d186042cbf000646", "urls": ["bzz-raw://b015e8d4976d3b6d7eaca07dfcc487aeed3a7d8b4c41c8369a7476dcfb211194", "dweb:/ipfs/QmecH84UnZYxDZ2aL6rQtnrEExLEAfo7q4Y47yuBXdymeX"], "license": "GPL-3.0-only"}, "contracts/libraries/Liquidation.sol": {"keccak256": "0x842bc44bc3cff80360ab82c5920070b12680edefe9267bdffc2d6c3c3a692d63", "urls": ["bzz-raw://85ecd75568a0729aec06741d0575ed07dad8b7daebd7ba3114a93f6019250877", "dweb:/ipfs/QmQMvWdsPWsQ4t1yv6eyZy5TM7h1EZpSJdt5b8fDLcumCW"], "license": "GPL-3.0-only"}, "contracts/libraries/QuadraticSwapFees.sol": {"keccak256": "0x00f6b7909be4fa1fc1ba426dd8ae659d1c5cb20c79665148898c973f55cfdccb", "urls": ["bzz-raw://c64da0826a9b0ffc08319709f6db03339d22d24deda902a6540393251da0aecb", "dweb:/ipfs/QmSNwBbn2VAS8HPY4hNZusEc4DoKKZAZHtpPdjL9Gz3gs3"], "license": "GPL-3.0-only"}, "contracts/libraries/Saturation.sol": {"keccak256": "0xf44bc610ece4bc7ebdb0730aa6ad69ea47647e19d4c1944c663d2d2eb4f10860", "urls": ["bzz-raw://421fdf8d0b27132bc324a42ede9aaf23b476e5134e1073f84e824620a2a44f20", "dweb:/ipfs/QmbvSfMuMzDmrfPkCAEp7ydtRDWu5EUiXq4MyrGGjFErzE"], "license": "GPL-3.0-only"}, "contracts/libraries/TickMath.sol": {"keccak256": "0x753813c7ed638d22edb71f48f8eb8b4283b3db2ba5b136b5c8909bd37ffa3f12", "urls": ["bzz-raw://04dd5085b72f6d73e1b17f58148e4d03639f654bdc4fdbc173b7c92ff102fc20", "dweb:/ipfs/QmSg4xTQPkngjNxs84428FZdSwH4AUQpwLXaASx7Qev6oG"], "license": "GPL-2.0-or-later"}, "contracts/libraries/Uint16Set.sol": {"keccak256": "0x26a714430fe1618d78386e953153b4bd2bf024baee54453ec9a7a0cc60e1534f", "urls": ["bzz-raw://8667dd78541d656a09678e5f9cce4d49adc805955604ccaaec414e9b241f5e06", "dweb:/ipfs/QmZVWU2CzyDQfGit32HjJxDphBJMKG3d6JRuxbC682Z1gy"], "license": "GPL-3.0-only"}, "contracts/libraries/Validation.sol": {"keccak256": "0x294848b2af973dbcd8b83732a57b67f14fd15e4af0668de05a2928b8eca5a463", "urls": ["bzz-raw://fab25c941e87f6924b31e3f20742ca6b5ec1b7e4251543f4a61567a04ef4d778", "dweb:/ipfs/Qmf4ChH8afdHc3SfXkFPpNGp3e1hscyvnujPAMza3yuXeA"], "license": "GPL-3.0-only"}, "contracts/libraries/constants.sol": {"keccak256": "0x0dfb294985a8f48287ff13e8476718ddb5334b1d8bf6bfa59a5db1dbcf6ca7c4", "urls": ["bzz-raw://4bedcfdb2850cfb22b5daa768ab8125b4ccab97c90068d1d0ad4495bf942b362", "dweb:/ipfs/Qmf9p88yQN2JYRBR5D7q9BLmwhDJWpFk47ZuayrKqCyHat"], "license": "GPL-3.0-only"}, "contracts/tokens/TokenController.sol": {"keccak256": "0x8b76b9ebb9385f0c4b7c0b8210fb96b11a49a8c9a3a6e855752c32a5c12d54e6", "urls": ["bzz-raw://de87bdae81940f397136f665d68907d4e4c32f35bf2cd0c9e9305a9fe190d159", "dweb:/ipfs/Qmce4hM6xofBYxzAXesHX4hkiHBexoGeQpCzpeCARctnCn"], "license": "GPL-3.0-only"}, "lib/mangrove-core/lib/core/BitLib.sol": {"keccak256": "0x80f6885268986b9e976b424993aa875cf7aab8464403ed675a86ade9e9be5ee3", "urls": ["bzz-raw://5a31b4e1e0dc95de9a1dbb114c40c44814de5db3e2d857c93c2524a61454f6c8", "dweb:/ipfs/QmRkgE9ue5rGwE6XDnszF2e2meWqAC9nnKM97xKHjHphQr"], "license": "MIT"}, "lib/morpho-blue/src/libraries/MathLib.sol": {"keccak256": "0xa7354cbbcecef7bc0c94b61061c4e5da75515056b8e2db65e826b00d7369744a", "urls": ["bzz-raw://d7419c59bb906fcfa49320b68f265c3200090e5c30b194766256aee70b012e08", "dweb:/ipfs/Qmbo4uaW6XYnudya4bb6RU6riWXFk5M3CWJge5XzTTaEfd"], "license": "GPL-2.0-or-later"}, "lib/openzeppelin-contracts/contracts/access/AccessControl.sol": {"keccak256": "0xb64ecf1154f183412bcde47168f3af245e4120846346a0b3872c631e361156d2", "urls": ["bzz-raw://85331049a60659bc4489733ccd3cbeb177b65691122a8cb637bf9267ab25e23d", "dweb:/ipfs/QmbGpDcdwKTirzSCoZfE4rHG7jBSWsE4K2iSb6UCYXtLJv"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/access/IAccessControl.sol": {"keccak256": "0x5643a5cadd1278581308b20becb48a50946c159fc31c29fc407ea9a61fc865d1", "urls": ["bzz-raw://c7d79f305a239207a24fa2174897c8ea8ff1e81cb790d440fd54c89a0e85f63e", "dweb:/ipfs/QmT847eeAMnRN3DaG1zsKNMn7qipNAidqv1REnKexPkrfA"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/access/Ownable.sol": {"keccak256": "0xff6d0bb2e285473e5311d9d3caacb525ae3538a80758c10649a4d61029b017bb", "urls": ["bzz-raw://8ed324d3920bb545059d66ab97d43e43ee85fd3bd52e03e401f020afb0b120f6", "dweb:/ipfs/QmfEckWLmZkDDcoWrkEvMWhms66xwTLff9DDhegYpvHo1a"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/governance/utils/IVotes.sol": {"keccak256": "0xb541b133f2d85eb37ae866cb21123be93bd3671b6840c47f951da52d3a704548", "urls": ["bzz-raw://22443f43ece107eaf453aa9a41a59410cece5429c2779c794fbd2c8b5aa29d38", "dweb:/ipfs/Qmbum2jwLYuT9aZ2fr9NMLwWFsVavonrGm2VnbAL9hP2jn"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/governance/utils/Votes.sol": {"keccak256": "0x3f91c79d6f55db9e4fc36e1cfe6a483a7b0f5be60fecbd979555071673746d47", "urls": ["bzz-raw://9b1e3c64cbeb2757a2a1a45c69f7f3984a93b0eadd1016341b64f9d94f89d7c4", "dweb:/ipfs/QmP1Mj14U4vMTFa2rv2nodMbWSCov2ac9Md8W2aUcgYdKX"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/IERC1363.sol": {"keccak256": "0xc15298eb2b9ba5e18a8c9d12f93ad17a3e162a5c1d9b85f54c8adb5827b0d4da", "urls": ["bzz-raw://1f3c3d8f81d2daf1231890a6a2f897be365d6a479b53dcd52ec2527b5d3faf41", "dweb:/ipfs/QmeNdkd6u4at9pd2GAyyqxzrVGGvxfLpGmAKnFoYM5ya2e"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/IERC165.sol": {"keccak256": "0x81b022028c39007cce9920c394b9cddd1cb9f3a1c0398f254b4a6492df92ad2b", "urls": ["bzz-raw://e0b61b8a5c69b4df993c3d6f94c174ab293aa8698d149bce7be2d88f82929beb", "dweb:/ipfs/QmbtacmB1k8ginfrHvAJpjVeqnjYGfXYrkXmMPYEb83z4t"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/IERC20.sol": {"keccak256": "0xb5d81383d40f4006d1ce4bbad0064e7a930e17302cbe2a745e09cb403f042733", "urls": ["bzz-raw://3fc4a5681c2f00f41f49260a36ae6bbe1121dd93d470ea24d51d556eff2980be", "dweb:/ipfs/QmUBW6TwVWtGP96ka9TfuGivd27kH8CtkXD8RQAAecSFiR"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/IERC3156FlashBorrower.sol": {"keccak256": "0xad94c8d7246a50210f7bcb54e5b91fc9f1c6e137263ac972ca5dd0f7f6d4d49d", "urls": ["bzz-raw://6938e96fbb0cf3d961788b6c3522400e255d8057d1b9f7e08a50e0b48486b007", "dweb:/ipfs/QmNXG3MPzDXjHJ9iWDYCz4vi9RBTgVBnZjndnfBwMfhkyD"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/IERC3156FlashLender.sol": {"keccak256": "0xd92910b862581523ad4e9b99f0bf738f4e62700a5e305953c7fda7db2cfd0f73", "urls": ["bzz-raw://799f3f0f30822ac806800bbe7fe63b9991490c4e1d9edb75f5993d9350320819", "dweb:/ipfs/QmT8T4SokW6YxpDJQiafpeYNRGtjC5gFHxRqKTRXRyP6zB"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/IERC5267.sol": {"keccak256": "0xf29e9088951d8a2074d872a733674618fe5c164df21b8b5cf4a6295f523ba7ad", "urls": ["bzz-raw://562a1abc7ea505582827ce0c9a2f778360a1a8242742683af179930640020215", "dweb:/ipfs/QmPjx5f6KKaPfsDi1uV3ovQN9gHTAcNkMAFJZxE1Adw6VT"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/IERC5805.sol": {"keccak256": "0xc8960b7d3e504e98883de33856a917a473c05034cd61880df2a60b5c47c214fe", "urls": ["bzz-raw://80542373fa695b68d65b4d7222e852df5bda035a82e86ee559336c93b2bf7051", "dweb:/ipfs/QmZgH14DPTnKfA5gMSTMiUa6ExuqFfAozmEtLXiWc1iDiw"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/IERC6372.sol": {"keccak256": "0xa602c8beaae2d9e2ab1ce585a54547a6d4da32d32e4d002d20ccba55b19258d8", "urls": ["bzz-raw://ac6553b5b07788a0bb67cc53596837d795280233a9a5cb3a9b3e1fde56822f78", "dweb:/ipfs/QmVoHXoma4ZbPKVRJJRosvhipa4rtCMU9QQvWHWKiRUxvi"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/draft-IERC6093.sol": {"keccak256": "0x85cf779582f7860b00bba1d259be00e754bfbac3c2339e324d0113d682d9e9f9", "urls": ["bzz-raw://2ddf369affd106e2a9e2b8a22a6ce3da8a6ccda14c6ba5b8c87f6b08169e6318", "dweb:/ipfs/QmNadAttd47ycHShxhk33JUJhrbzmyZQ7mHs7WEyG4Qkmp"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/ERC20.sol": {"keccak256": "0x5aad1745bddba11752c3962464a3b12e0af079310cc22d1f43f0388ae1aaf8db", "urls": ["bzz-raw://577fad916bfdfe89aadf2685322fec7562cb0ed87722923085213cd9f85d7b79", "dweb:/ipfs/QmSM3J6PjrAUyEoNbdhq1ECZLXczKdCTzZTBUieKHsBYEL"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol": {"keccak256": "0xfe37358f223eddd65d61bb62b0b7bdb69d7101b5ec8d484292b8c1583a153b8a", "urls": ["bzz-raw://28dd43f30af3c12ae0fc08dd031b1250e906ef3c95f63f30fac6fd15aee2a662", "dweb:/ipfs/QmUkSyWsSRx36w1ti7U6qnGnQgJq16wpMhjeJrnyn9AXwG"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20Burnable.sol": {"keccak256": "0x2659248df25e34000ed214b3dc8da2160bc39874c992b477d9e2b1b3283dc073", "urls": ["bzz-raw://c345af1b0e7ea28d1216d6a04ab28f5534a5229b9edf9ca3cd0e84950ae58d26", "dweb:/ipfs/QmY63jtSrYpLRe8Gj1ep2vMDCKxGNNG3hnNVKBVnrs2nmA"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20FlashMint.sol": {"keccak256": "0x4d43ed4b9ff9e4c671274976d59a58dbcc7b69bd7ac11b1710f5b7607cf15b74", "urls": ["bzz-raw://0b47b42998f675cb6a51f2e74ef5906a6fa63ec6718f3fd56ee035d6f77143f9", "dweb:/ipfs/QmREnAXqPJBvAwfWfDzaFhNfSRWF4Jdy9ZrpHLw1KdQweY"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20Permit.sol": {"keccak256": "0x6485b101d7335f0fd25abc996c5e2fc965e72e5fbd0a7ad1a465bd3f012b5fd8", "urls": ["bzz-raw://1f8d296121044e697bcd34c4cd394cebd46fc30b04ce94fccff37200872e6834", "dweb:/ipfs/QmTNdmLdoHgMzoCDZ8Txk9aYvwtuyeJYHf5mjLWgzGTZAu"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20Votes.sol": {"keccak256": "0x62dc9346044aabf22d78541bd495aa6ca05a7f5100aed26196ba35d40b59fcb5", "urls": ["bzz-raw://5221df4501c74cd4493fee1a0f0788e02c4dc78c3c601e9f557f557c5a53ea92", "dweb:/ipfs/QmZpzyYY9dKLrgvYhXSHT93jwqb1UGvtGNMQk5dpECY5pa"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol": {"keccak256": "0xbaffa0bcc92adf28a53cc3b68551fc3632cb8f849a0028cb8d5c06e4677715e9", "urls": ["bzz-raw://32e6f8f6b2e883c85e6a602c0882d9962ce2f92406961244e86cd974df815912", "dweb:/ipfs/Qmahvx6fPpecicq1aUE1JihCxV5ep1bfuPukzrxa8Ub5PS"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Permit.sol": {"keccak256": "0x093f32ab700c2b05373387263915a75f5455cdb0f09a7630cc621e27b7b50d04", "urls": ["bzz-raw://d163e6ef21df143969df5557305e8c643a135c7660a678d0c65dca91772114a0", "dweb:/ipfs/QmTZUgiwEro5oLRhbJ2iSWyCqu1JTDekoFHALVUn4eHqYK"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol": {"keccak256": "0x1183b415155c1a7bf56d45edad5b17caf0da70935ac420698cbe8afb6750cbb2", "urls": ["bzz-raw://21d9edaeb3e5e8f93eb0fdab41530654e8169b1990b3bbfcf5e4527c52aa03f5", "dweb:/ipfs/QmWrqpNW3x5k3pTjvrT8XU1hauHnXTjqaPL2tfzMuWYosj"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Context.sol": {"keccak256": "0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2", "urls": ["bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12", "dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Nonces.sol": {"keccak256": "0x0082767004fca261c332e9ad100868327a863a88ef724e844857128845ab350f", "urls": ["bzz-raw://132dce9686a54e025eb5ba5d2e48208f847a1ec3e60a3e527766d7bf53fb7f9e", "dweb:/ipfs/QmXn1a2nUZMpu2z6S88UoTfMVtY2YNh86iGrzJDYmMkKeZ"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Panic.sol": {"keccak256": "0xf7fe324703a64fc51702311dc51562d5cb1497734f074e4f483bfb6717572d7a", "urls": ["bzz-raw://c6a5ff4f9fd8649b7ee20800b7fa387d3465bd77cf20c2d1068cd5c98e1ed57a", "dweb:/ipfs/QmVSaVJf9FXFhdYEYeCEfjMVHrxDh5qL4CGkxdMWpQCrqG"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Pausable.sol": {"keccak256": "0xdb484371dfbb848cb6f5d70464e9ac9b2900e4164ead76bbce4fef0b44bcc68f", "urls": ["bzz-raw://f9d6f6f6600a2bec622f699081b58350873b5e63ce05464d17d674a290bb8a7c", "dweb:/ipfs/QmQKVzSQY1PM3Bid4QhgVVZyx6B4Jx7XgaQzLKHj38vJz8"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/ShortStrings.sol": {"keccak256": "0x1fcf8cceb1a67e6c8512267e780933c4a3f63ef44756e6c818fda79be51c8402", "urls": ["bzz-raw://617d7d57f6f9cd449068b4d23daf485676d083aae648e038d05eb3a13291de35", "dweb:/ipfs/QmPADWPiGaSzZDFNpFEUx4ZPqhzPkYncBpHyTfAGcfsqzy"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol": {"keccak256": "0xcf74f855663ce2ae00ed8352666b7935f6cddea2932fdf2c3ecd30a9b1cd0e97", "urls": ["bzz-raw://9f660b1f351b757dfe01438e59888f31f33ded3afcf5cb5b0d9bf9aa6f320a8b", "dweb:/ipfs/QmarDJ5hZEgBtCmmrVzEZWjub9769eD686jmzb2XpSU1cM"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Strings.sol": {"keccak256": "0x1402d9ac66fbca0a2b282cd938f01f3cd5fb1e4c696ed28b37839401674aef52", "urls": ["bzz-raw://d3e6c46b6d1ea36bd73e0ac443a53504089167b98baa24923d702a865a38d211", "dweb:/ipfs/QmdutUpr5KktmvgtqG2v96Bo8nVKLJ3PgPedxbsRD42CuQ"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/cryptography/ECDSA.sol": {"keccak256": "0x6c29257484c0595ca5af8844fafe99cc5eace7447c9f5bced71d6b3a19a6a2a5", "urls": ["bzz-raw://cce7ac0bdb05f73c0918e362dea2e52426e00ddf0a1018f14accdcf78c6eb6e4", "dweb:/ipfs/QmbkNq5dDxww27FzFFiKgW3S7C5VoZpjdZGpSCtsb9hP32"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/cryptography/EIP712.sol": {"keccak256": "0xda8013da608bda3c9eaa9e59053d38d7888e64bb40aa557e5929cd702f8de87e", "urls": ["bzz-raw://3ea13234c6b00ae79dc1a98e7e7f2faf38d37e76a687ccd0c95ad84b03ea570f", "dweb:/ipfs/QmWtdefDm5jiEzAjmfPMZ5B1NKVxFoMiD5ZoD68hcNTHun"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/cryptography/MessageHashUtils.sol": {"keccak256": "0x26670fef37d4adf55570ba78815eec5f31cb017e708f61886add4fc4da665631", "urls": ["bzz-raw://b16d45febff462bafd8a5669f904796a835baf607df58a8461916d3bf4f08c59", "dweb:/ipfs/QmU2eJFpjmT4vxeJWJyLeQb8Xht1kdB8Y6MKLDPFA9WPux"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/introspection/ERC165.sol": {"keccak256": "0x41ddfafe0d00dc22e35119d41cb0ca93673960689d35710fd12875139e64bd9f", "urls": ["bzz-raw://49d90142e15cdc4ca00de16e1882fa0a0daad8b46403628beb90c67a3efe4fc4", "dweb:/ipfs/QmNizYnFNcGixHxsknEccr2cQWyyQBqFF7h2bXLmefQz6M"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/introspection/IERC165.sol": {"keccak256": "0x4ee0e04cc52827588793a141d5efb9830f179a17e80867cc332b3a30ceb30fd9", "urls": ["bzz-raw://17d8f47fce493b34099ed9005c5aee3012488f063cfe1c34ed8f9e6fc3d576e5", "dweb:/ipfs/QmZco2GbZZhEMvG3BovyoGMAFKvfi2LhfNGQLn283LPrXf"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/math/Math.sol": {"keccak256": "0x1225214420c83ebcca88f2ae2b50f053aaa7df7bd684c3e878d334627f2edfc6", "urls": ["bzz-raw://6c5fab4970634f9ab9a620983dc1c8a30153981a0b1a521666e269d0a11399d3", "dweb:/ipfs/QmVRnBC575MESGkEHndjujtR7qub2FzU9RWy9eKLp4hPZB"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/math/SafeCast.sol": {"keccak256": "0x195533c86d0ef72bcc06456a4f66a9b941f38eb403739b00f21fd7c1abd1ae54", "urls": ["bzz-raw://b1d578337048cad08c1c03041cca5978eff5428aa130c781b271ad9e5566e1f8", "dweb:/ipfs/QmPFKL2r9CBsMwmUqqdcFPfHZB2qcs9g1HDrPxzWSxomvy"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/math/SignedMath.sol": {"keccak256": "0xb1970fac7b64e6c09611e6691791e848d5e3fe410fa5899e7df2e0afd77a99e3", "urls": ["bzz-raw://db5fbb3dddd8b7047465b62575d96231ba8a2774d37fb4737fbf23340fabbb03", "dweb:/ipfs/QmVUSvooZKEdEdap619tcJjTLcAuH6QBdZqAzWwnAXZAWJ"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/structs/Checkpoints.sol": {"keccak256": "0x743aa2d21f6c26885e0aa6a1c84f7f7bc58fbd6df6bab32bed23f1a41f50454a", "urls": ["bzz-raw://a651d38b4261840d3744e571edf2b59455352a8c7dac5d35b019afefa343ea3b", "dweb:/ipfs/QmSy3UkTCQDYTjKtGwtqPRrXaofcqtVZxaF6j1dV44wqvr"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/types/Time.sol": {"keccak256": "0x36776530f012618bc7526ceb28e77b85e582cb12d9b9466a71d4bd6bf952e4cc", "urls": ["bzz-raw://9f867d046908497287d8a67643dd5d7e38c4027af4ab0a74ffbe1d6790c383c6", "dweb:/ipfs/QmQ7s9gMP1nkwThFmoDifnGgpUMsMe5q5ZrAxGDsNnRGza"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/Base.sol": {"keccak256": "0x4ff1a785311017d1eedb1b4737956fa383067ad34eb439abfec1d989754dde1c", "urls": ["bzz-raw://f553622969b9fdb930246704a4c10dfaee6b1a4468c142fa7eb9dc292a438224", "dweb:/ipfs/QmcxqHnqdQsMVtgsfH9VNLmZ3g7GhgNagfq7yvNCDcCHFK"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/StdAssertions.sol": {"keccak256": "0x4584f551c3a875c26423c1e50a77179bc2f9c9c1ee6d0f0c6be0f89ca5ee8270", "urls": ["bzz-raw://ef118876a862b60ba8a6612b8b3f3a6c226e11a0071a2e9695f021586d1bfdbe", "dweb:/ipfs/QmXd2vD91PkvoFWasFkgbDi89PctmgsHVxGvGNyhjmRK2b"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/StdChains.sol": {"keccak256": "0xcd3e64ec9ffa19a2c0715bbdaf7ddf28887cc418e079bec4373fd6a3f9961a7b", "urls": ["bzz-raw://e981a2ab738590928e9efa5f3d95a408c718eb12d73a113d7675f3ed55a026a1", "dweb:/ipfs/QmTgSEkWWsBRy32goRCaUkraSgpZHtgbZoKC3iEFNz5RDc"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/StdCheats.sol": {"keccak256": "0x0fa6ec03602648b62cce41aab2096e6b7e052f2846075d967b6958dd586db746", "urls": ["bzz-raw://cd84e2ca9c1eaed6b76768cc12bb8c1af8289170ea8b7706f58d516460d79c41", "dweb:/ipfs/QmQ7BK7co6DE4eWUqMyv11s5eHYkS1tyx8tDSZGZVtf2aK"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/StdError.sol": {"keccak256": "0xbf477b11a42d7611696956546bcfaa29317d1166bf65e402344599c05943fc77", "urls": ["bzz-raw://bc2e117d1135e030862b96a6526a43feb38d396cc79857f1fb696d4eff0e5fd6", "dweb:/ipfs/QmdSuQ5RrQudTLsNmWXGEeVJX8gR5U9XPm6m4dwwuQnJrj"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/StdInvariant.sol": {"keccak256": "0x4dab3021edfa9511bbdd80c48f060ef62eaf457f99eaf841f561fc2557d9a08d", "urls": ["bzz-raw://07668628673174cf8f27f8f4e1f862bab775013ec247eb34f698c5005f229391", "dweb:/ipfs/QmPJsiiYwmCZXMsHmQv5tg8VF3CAhNdat7WnKLTWZZH2v5"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/StdJson.sol": {"keccak256": "0xbc0132abe1c2accc2867c0f03667afffdf92f3e95a581bb03c9557eaa38ea500", "urls": ["bzz-raw://eb6fab37dc73c219cfbb7b4f4998bcf7677ca5397a867e850f40232192073974", "dweb:/ipfs/QmUHsbVdp9SKmgek7ZfPcLTKrpZFXpqaqt4sVejzxGEQL3"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/StdMath.sol": {"keccak256": "0xd90ad4fd8aeaeb8929964e686e769fdedd5eded3fc3815df194a0ab9f91a3fb2", "urls": ["bzz-raw://7919b70f636c7b805223992f28ad1ad0145d6c1385b5931a3589aface5fe6c92", "dweb:/ipfs/QmY7FRaULwoGgFteF8GawjQJRfasNgpWnU2aiMsFrYpuTC"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/StdStorage.sol": {"keccak256": "0xb91ab24383a5872b894fc93325eef1add6cbbf981628f18e860068bf88bb7dcc", "urls": ["bzz-raw://2651e33b9ac8fc5803ed0a43078c1bf4fa90a0b0347aafd95776b02daccdcc57", "dweb:/ipfs/QmbdRyPuGJdZgnCuMnp7c1WsBo1Spf7j1KMmws1Z5rd4BQ"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/StdStyle.sol": {"keccak256": "0x43e2a8a9b9c2574dabe74f11adf6f782df218f463540e3b5b563609fe108597d", "urls": ["bzz-raw://51363ca97404cf4128e1141428949768c31929e75e014b02c85e887fbbb4f1b8", "dweb:/ipfs/QmVhtbQc2fU4rRmbcfBtz34mAgG4BAZBsbna1Ca4SkoPsK"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/StdToml.sol": {"keccak256": "0x58a72c765ed3f7ff6b105509689658795b8a3739b8931772a497155878381861", "urls": ["bzz-raw://b4a3746f4fabaeb980bd77d9e091d3904ee38a6c0e191bfa8ba6874c6f8558a3", "dweb:/ipfs/QmUfFDMEn461FgGEXt5HicyGD54sc28sLaQ9JRWDMBKed8"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/StdUtils.sol": {"keccak256": "0x7274081e11c05164fd8eadde4de8305c033e58a43008dea58065f3170ccf0737", "urls": ["bzz-raw://e9801614b6c9d3e472982e6cc68f5f1ad03682f84eafb686be65633c7132d138", "dweb:/ipfs/QmcQSUcrm2A7XuektnxJjvYGmZtBeA6LKPxCXRqdXBX776"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/Test.sol": {"keccak256": "0x3b4bb409a156dee9ce261458117fe9f81080ca844a8a26c07c857c46d155effe", "urls": ["bzz-raw://5792c69fe24bdc063a14e08fe68275007fdb1e5e7e343840a77938cb7e95a64e", "dweb:/ipfs/QmcAMhaurUwzhytJFYix4vRNeZeV8g27b8LnV3t7dvYtiK"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/Vm.sol": {"keccak256": "0x44bfadcf5a89b8058f80258f2259585c740f9cc45669a0579f4f2753ff2c6354", "urls": ["bzz-raw://bbc366c8b3499d5030e3b2e45bac23770531f2f5243a0e80e3d5a66b6f9a312c", "dweb:/ipfs/QmNxDEB3BaVnKzNaWedtdMshhvCEddB1AsdJZcsQx6jdtC"], "license": "MIT OR Apache-2.0"}, "lib/openzeppelin-contracts/lib/forge-std/src/console.sol": {"keccak256": "0x4bbf47eb762cef93729d6ef15e78789957147039b113e5d4df48e3d3fd16d0f5", "urls": ["bzz-raw://af9e3a7c3d82fb5b10b57ca4d1a82f2acbef80c077f6f6ef0cc0187c7bfd9f57", "dweb:/ipfs/QmR9VzmnBDJpgiDP6CHT6truehukF9HpYvuP6kRiJbDwPP"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/console2.sol": {"keccak256": "0x3b8fe79f48f065a4e4d35362171304a33784c3a90febae5f2787805a438de12f", "urls": ["bzz-raw://61de63af08803549299e68b6e6e88d40f3c5afac450e4ee0a228c66a61ba003d", "dweb:/ipfs/QmWVoQ5rrVxnczD4ZZoPbD4PC9Z3uExJtzjD4awTqd14MZ"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/interfaces/IMulticall3.sol": {"keccak256": "0x7aac1389150499a922d1f9ef5749c908cef127cb2075b92fa17e9cb611263d0a", "urls": ["bzz-raw://d95ebb7c7c463e08ebc12dab639945752fb2480acfc6e86da32f72732a7fd0c0", "dweb:/ipfs/QmNXK8P8oPWwajsQHvAHw3JPyQidPLCGQN3hWu1Lk6PBL2"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/safeconsole.sol": {"keccak256": "0xbef9786cb49d3eade757bad87568c49c8c8f35721f0193c95ffb055d9e466e11", "urls": ["bzz-raw://3bafd2b0b2d28068d329f95ea8a1fbce3719c257fcb863fc01abcbafd8d531ab", "dweb:/ipfs/QmUeaFjKWTVDBsHVfSob4mwt6A5hTnKDz22HaUXeZhypa3"], "license": "MIT"}, "test/shared/GeometricTWAPTestFixture.sol": {"keccak256": "0x66a316a13c74698cbb99bd7f5681b6879eb300c7c2331095458be9d6b39bc386", "urls": ["bzz-raw://cb294f187ac85de17d052e9cc9e91bfbf13c1d9d50fcaa8231fd0e3c9e4be280", "dweb:/ipfs/QmbZyjygUHe6Ss5sZrHjUm5jRBLSCLqCFRURjkAsGWaupu"], "license": "GPL-3.0-only"}, "test/shared/StubErc20.sol": {"keccak256": "0xf3508dc98ae444d142d9993c52cebd856aba40c3e53d64bfeb63e71d190b12ee", "urls": ["bzz-raw://0cc01d254b6d5569d1cb426250db9df1b01afde9dd7b52e1efa0691112fcd918", "dweb:/ipfs/QmPnL9wFpSKXprrEFS9kkC2WzK2kAgWSH1snom1wiorCxn"], "license": "MIT"}, "test/shared/utilities.sol": {"keccak256": "0xc64b147bbe73bf59fdec4202c5b7c5dbcadd7550f4b2ea2390ea689e194d7cb8", "urls": ["bzz-raw://ab03a14b75d4b9df7795eeefd7e6d4a1d7af7b58ce948741cdd5d056a2c30416", "dweb:/ipfs/QmShemddxGaLyTGtC3yLdMtdHf9Gj3H8rjf2umzbFmP6aG"], "license": "GPL-3.0-only"}, "test/utils/DepletedAssetUtils.sol": {"keccak256": "0x2273187d5eb782fb341d44265bd6e8afcef18ab3cfabcb4a0b77a75f15298c42", "urls": ["bzz-raw://04d0a060b217603f6c7e38efc49be20932f08c56e46b1f9cea54eb722595458e", "dweb:/ipfs/QmdJHgaFbbtGDHPpHEFucTvrj4p4LT1piMPjtbrWBMXzAR"], "license": "GPL-3.0-only"}}, "version": 1}, "id": 178}