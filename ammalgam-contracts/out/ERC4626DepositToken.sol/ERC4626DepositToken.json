{"abi": [{"type": "constructor", "inputs": [{"name": "config", "type": "tuple", "internalType": "struct ERC20BaseConfig", "components": [{"name": "pair", "type": "address", "internalType": "address"}, {"name": "pluginRegistry", "type": "address", "internalType": "address"}, {"name": "name", "type": "string", "internalType": "string"}, {"name": "symbol", "type": "string", "internalType": "string"}, {"name": "tokenType", "type": "uint256", "internalType": "uint256"}]}, {"name": "_asset", "type": "address", "internalType": "address"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "DOMAIN_SEPARATOR", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "HOOK_CALL_GAS_LIMIT", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "MAX_HOOKS_PER_ACCOUNT", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "addHook", "inputs": [{"name": "hook", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "allowance", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}, {"name": "spender", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "approve", "inputs": [{"name": "spender", "type": "address", "internalType": "address"}, {"name": "value", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "asset", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "balanceOf", "inputs": [{"name": "account", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "convertToAssets", "inputs": [{"name": "shares", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "convertToShares", "inputs": [{"name": "assets", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "decimals", "inputs": [], "outputs": [{"name": "", "type": "uint8", "internalType": "uint8"}], "stateMutability": "view"}, {"type": "function", "name": "deposit", "inputs": [{"name": "assets", "type": "uint256", "internalType": "uint256"}, {"name": "receiver", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "eip712Domain", "inputs": [], "outputs": [{"name": "fields", "type": "bytes1", "internalType": "bytes1"}, {"name": "name", "type": "string", "internalType": "string"}, {"name": "version", "type": "string", "internalType": "string"}, {"name": "chainId", "type": "uint256", "internalType": "uint256"}, {"name": "verifyingContract", "type": "address", "internalType": "address"}, {"name": "salt", "type": "bytes32", "internalType": "bytes32"}, {"name": "extensions", "type": "uint256[]", "internalType": "uint256[]"}], "stateMutability": "view"}, {"type": "function", "name": "hasH<PERSON>", "inputs": [{"name": "account", "type": "address", "internalType": "address"}, {"name": "hook", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "hookAt", "inputs": [{"name": "account", "type": "address", "internalType": "address"}, {"name": "index", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "hookBalanceOf", "inputs": [{"name": "hook", "type": "address", "internalType": "address"}, {"name": "account", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "hooks", "inputs": [{"name": "account", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "hooksCount", "inputs": [{"name": "account", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "maxDeposit", "inputs": [{"name": "", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "maxMint", "inputs": [{"name": "", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "max<PERSON><PERSON><PERSON>", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "max<PERSON><PERSON><PERSON><PERSON>", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "mint", "inputs": [{"name": "shares", "type": "uint256", "internalType": "uint256"}, {"name": "receiver", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "name", "inputs": [], "outputs": [{"name": "", "type": "string", "internalType": "string"}], "stateMutability": "view"}, {"type": "function", "name": "nonces", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "owner", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "ownerBurn", "inputs": [{"name": "sender", "type": "address", "internalType": "address"}, {"name": "to", "type": "address", "internalType": "address"}, {"name": "assets", "type": "uint256", "internalType": "uint256"}, {"name": "shares", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "ownerMint", "inputs": [{"name": "sender", "type": "address", "internalType": "address"}, {"name": "to", "type": "address", "internalType": "address"}, {"name": "assets", "type": "uint256", "internalType": "uint256"}, {"name": "shares", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "ownerTransfer", "inputs": [{"name": "from", "type": "address", "internalType": "address"}, {"name": "to", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "pair", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract ITransferValidator"}], "stateMutability": "view"}, {"type": "function", "name": "permit", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}, {"name": "spender", "type": "address", "internalType": "address"}, {"name": "value", "type": "uint256", "internalType": "uint256"}, {"name": "deadline", "type": "uint256", "internalType": "uint256"}, {"name": "v", "type": "uint8", "internalType": "uint8"}, {"name": "r", "type": "bytes32", "internalType": "bytes32"}, {"name": "s", "type": "bytes32", "internalType": "bytes32"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "previewDeposit", "inputs": [{"name": "assets", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "previewMint", "inputs": [{"name": "shares", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "previewRedeem", "inputs": [{"name": "shares", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "previewWithdraw", "inputs": [{"name": "assets", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "redeem", "inputs": [{"name": "shares", "type": "uint256", "internalType": "uint256"}, {"name": "receiver", "type": "address", "internalType": "address"}, {"name": "owner", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "removeAllHooks", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "removeH<PERSON>", "inputs": [{"name": "hook", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "renounceOwnership", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "symbol", "inputs": [], "outputs": [{"name": "", "type": "string", "internalType": "string"}], "stateMutability": "view"}, {"type": "function", "name": "tokenType", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "totalAssets", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "totalSupply", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "transfer", "inputs": [{"name": "to", "type": "address", "internalType": "address"}, {"name": "value", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "transferFrom", "inputs": [{"name": "from", "type": "address", "internalType": "address"}, {"name": "to", "type": "address", "internalType": "address"}, {"name": "value", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "transferOwnership", "inputs": [{"name": "new<PERSON>wner", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "withdraw", "inputs": [{"name": "assets", "type": "uint256", "internalType": "uint256"}, {"name": "receiver", "type": "address", "internalType": "address"}, {"name": "owner", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "event", "name": "Approval", "inputs": [{"name": "owner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "spender", "type": "address", "indexed": true, "internalType": "address"}, {"name": "value", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "Borrow", "inputs": [{"name": "sender", "type": "address", "indexed": true, "internalType": "address"}, {"name": "to", "type": "address", "indexed": true, "internalType": "address"}, {"name": "assets", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "shares", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "BorrowLiquidity", "inputs": [{"name": "sender", "type": "address", "indexed": true, "internalType": "address"}, {"name": "to", "type": "address", "indexed": true, "internalType": "address"}, {"name": "assets", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "shares", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "Burn", "inputs": [{"name": "sender", "type": "address", "indexed": true, "internalType": "address"}, {"name": "to", "type": "address", "indexed": true, "internalType": "address"}, {"name": "assets", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "shares", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "<PERSON><PERSON><PERSON><PERSON>", "inputs": [{"name": "sender", "type": "address", "indexed": true, "internalType": "address"}, {"name": "owner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "assets", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "shares", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "EIP712DomainChanged", "inputs": [], "anonymous": false}, {"type": "event", "name": "HookAdded", "inputs": [{"name": "account", "type": "address", "indexed": false, "internalType": "address"}, {"name": "hook", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "HookRemoved", "inputs": [{"name": "account", "type": "address", "indexed": false, "internalType": "address"}, {"name": "hook", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "Mint", "inputs": [{"name": "sender", "type": "address", "indexed": true, "internalType": "address"}, {"name": "to", "type": "address", "indexed": true, "internalType": "address"}, {"name": "assets", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "shares", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "OwnershipTransferred", "inputs": [{"name": "previousOwner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "new<PERSON>wner", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "<PERSON>ay", "inputs": [{"name": "sender", "type": "address", "indexed": true, "internalType": "address"}, {"name": "onBehalfOf", "type": "address", "indexed": true, "internalType": "address"}, {"name": "assets", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "shares", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "RepayLiquidity", "inputs": [{"name": "sender", "type": "address", "indexed": true, "internalType": "address"}, {"name": "onBehalfOf", "type": "address", "indexed": true, "internalType": "address"}, {"name": "assets", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "shares", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "Transfer", "inputs": [{"name": "from", "type": "address", "indexed": true, "internalType": "address"}, {"name": "to", "type": "address", "indexed": true, "internalType": "address"}, {"name": "value", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "Withdraw", "inputs": [{"name": "sender", "type": "address", "indexed": true, "internalType": "address"}, {"name": "receiver", "type": "address", "indexed": true, "internalType": "address"}, {"name": "owner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "assets", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "shares", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "error", "name": "ECDSAInvalidSignature", "inputs": []}, {"type": "error", "name": "ECDSAInvalidSignatureLength", "inputs": [{"name": "length", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "ECDSAInvalidSignatureS", "inputs": [{"name": "s", "type": "bytes32", "internalType": "bytes32"}]}, {"type": "error", "name": "ERC20InsufficientAllowance", "inputs": [{"name": "spender", "type": "address", "internalType": "address"}, {"name": "allowance", "type": "uint256", "internalType": "uint256"}, {"name": "needed", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "ERC20InsufficientBalance", "inputs": [{"name": "sender", "type": "address", "internalType": "address"}, {"name": "balance", "type": "uint256", "internalType": "uint256"}, {"name": "needed", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "ERC20InvalidApprover", "inputs": [{"name": "approver", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ERC20InvalidReceiver", "inputs": [{"name": "receiver", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ERC20InvalidSender", "inputs": [{"name": "sender", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ERC20InvalidSpender", "inputs": [{"name": "spender", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ERC2612ExpiredSignature", "inputs": [{"name": "deadline", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "ERC2612InvalidSigner", "inputs": [{"name": "signer", "type": "address", "internalType": "address"}, {"name": "owner", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ERC4626ExceededMaxDeposit", "inputs": [{"name": "receiver", "type": "address", "internalType": "address"}, {"name": "assets", "type": "uint256", "internalType": "uint256"}, {"name": "max", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "ERC4626ExceededMaxMint", "inputs": [{"name": "receiver", "type": "address", "internalType": "address"}, {"name": "shares", "type": "uint256", "internalType": "uint256"}, {"name": "max", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "ERC4626ExceededMaxRedeem", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}, {"name": "shares", "type": "uint256", "internalType": "uint256"}, {"name": "max", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "ERC4626ExceededMaxWithdraw", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}, {"name": "assets", "type": "uint256", "internalType": "uint256"}, {"name": "max", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "HookAlreadyAdded", "inputs": []}, {"type": "error", "name": "HookNotFound", "inputs": []}, {"type": "error", "name": "HooksLimitReachedForAccount", "inputs": []}, {"type": "error", "name": "IndexOutOfBounds", "inputs": []}, {"type": "error", "name": "InvalidAccountNonce", "inputs": [{"name": "account", "type": "address", "internalType": "address"}, {"name": "currentNonce", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "InvalidHookAddress", "inputs": []}, {"type": "error", "name": "InvalidShortString", "inputs": []}, {"type": "error", "name": "InvalidTokenInHook", "inputs": []}, {"type": "error", "name": "OwnableInvalidOwner", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "OwnableUnauthorizedAccount", "inputs": [{"name": "account", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ReentrantCall", "inputs": []}, {"type": "error", "name": "SafeERC20FailedOperation", "inputs": [{"name": "token", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "StringTooLong", "inputs": [{"name": "str", "type": "string", "internalType": "string"}]}, {"type": "error", "name": "ZeroHooksLimit", "inputs": []}], "bytecode": {"object": "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********116101bd578063********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", "sourceMap": "718:3896:36:-:0;;;775:103;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;868:6;2019::31;:11;;;1616:4:71;3428:431:85;;;;;;;;;;;;;-1:-1:-1;;;3428:431:85;;;2048:6:31;:11;;;1956:2;1960:7;849:6:36;1909::31;:11;;;1922:6;:13;;;1656:5:67;1648;:13;;;;;;:::i;:::-;-1:-1:-1;1671:7:67;:17;1681:7;1671;:17;:::i;:::-;;1582:113;;4321:12:73;4335:19;4358:28;4379:6;4358:20;;;:28;;:::i;:::-;4320:66;;;;4418:7;:28;;4444:2;4418:28;;;4428:13;4418:28;4396:50;;;;-1:-1:-1;;;;;;;4456:15:73;;;1561:1:44;1546:16;;;1542:45;;1571:16;;-1:-1:-1;;;1571:16:44;;;;;;;;;;;1542:45;1597:35;;;;;1642:39;;349:1:47;1691:6:44;717:27:47;-1:-1:-1;;;;;1273:26:53;;1269:95;;1322:31;;-1:-1:-1;;;1322:31:53;;1350:1;1322:31;;;5138:51:190;5111:18;;1322:31:53;;;;;;;;1269:95;1373:32;1392:12;1373:18;:32::i;:::-;-1:-1:-1;3501:45:85;:4;3532:13;3501:30;:45::i;:::-;3493:53;;3567:51;:7;3601:16;3567:33;:51::i;:::-;3556:62;;3642:22;;;;;;;;;;3628:36;;3691:25;;;;;;3674:42;;3744:13;3727:30;;3792:23;4326:11;;4339:14;;4304:80;;;2079:95;4304:80;;;6000:25:190;6041:18;;;6034:34;;;;6084:18;;;6077:34;4355:13:85;6127:18:190;;;6120:34;4378:4:85;6170:19:190;;;6163:61;4268:7:85;;5972:19:190;;4304:80:85;;;;;;;;;;;;4294:91;;;;;;4287:98;;4213:179;;3792:23;3767:48;;-1:-1:-1;;3847:4:85;3825:27;;-1:-1:-1;2101:11:31;;-1:-1:-1;;;;;2075:38:31;;::::4;;::::0;2135:16:::4;::::0;::::4;::::0;2123:28:::4;::::0;2194:21:::4;::::0;;::::4;::::0;2161:55:::4;;::::0;-1:-1:-1;718:3896:36;;-1:-1:-1;718:3896:36;4621:550:73;4815:43;;;;;;;;;;;;;;;;-1:-1:-1;;;;;4815:43:73;-1:-1:-1;;;4815:43:73;;;4775:93;;4688:7;;;;;;;;-1:-1:-1;;;;;4775:26:73;;;:93;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4728:140;;;;4882:7;:39;;;;;4919:2;4893:15;:22;:28;;4882:39;4878:260;;;4937:24;4975:15;4964:38;;;;;;;;;;;;:::i;:::-;4937:65;-1:-1:-1;5040:15:73;5020:35;;5016:112;;5083:4;;5095:16;;-1:-1:-1;4621:550:73;-1:-1:-1;;;;4621:550:73:o;5016:112::-;4923:215;4878:260;-1:-1:-1;5155:5:73;;;;-1:-1:-1;4621:550:73;-1:-1:-1;;;4621:550:73:o;2912:187:53:-;3004:6;;;-1:-1:-1;;;;;3020:17:53;;;-1:-1:-1;;;;;;3020:17:53;;;;;;;3052:40;;3004:6;;;3020:17;3004:6;;3052:40;;2985:16;;3052:40;2975:124;2912:187;:::o;2887:340:81:-;2983:11;3032:2;3016:5;3010:19;:24;3006:215;;;3057:20;3071:5;3057:13;:20::i;:::-;3050:27;;;;3006:215;3134:5;3108:46;3149:5;3134;3108:46;:::i;:::-;-1:-1:-1;1390:66:81;;-1:-1:-1;3006:215:81;2887:340;;;;:::o;1708:286::-;1773:11;1796:17;1822:3;1796:30;;1854:2;1840:4;:11;:16;1836:72;;;1893:3;1879:18;;-1:-1:-1;;;1879:18:81;;;;;;;;:::i;1836:72::-;1974:11;;1957:13;1974:4;1957:13;:::i;:::-;1949:36;;1708:286;-1:-1:-1;;;1708:286:81:o;14:127:190:-;75:10;70:3;66:20;63:1;56:31;106:4;103:1;96:15;130:4;127:1;120:15;146:248;213:2;207:9;255:4;243:17;;-1:-1:-1;;;;;275:34:190;;311:22;;;272:62;269:88;;;337:18;;:::i;:::-;373:2;366:22;146:248;:::o;399:177::-;478:13;;-1:-1:-1;;;;;520:31:190;;510:42;;500:70;;566:1;563;556:12;500:70;399:177;;;:::o;581:743::-;635:5;688:3;681:4;673:6;669:17;665:27;655:55;;706:1;703;696:12;655:55;733:13;;-1:-1:-1;;;;;758:30:190;;755:56;;;791:18;;:::i;:::-;860:2;854:9;952:2;914:17;;-1:-1:-1;;910:31:190;;;943:2;906:40;902:54;890:67;;-1:-1:-1;;;;;972:34:190;;1008:22;;;969:62;966:88;;;1034:18;;:::i;:::-;1070:2;1063:22;1094;;;1135:19;;;1156:4;1131:30;1128:39;-1:-1:-1;1125:59:190;;;1180:1;1177;1170:12;1125:59;1237:6;1230:4;1222:6;1218:17;1211:4;1203:6;1199:17;1193:51;1292:1;1264:19;;;1285:4;1260:30;1253:41;;;;1268:6;581:743;-1:-1:-1;;;581:743:190:o;1329:1149::-;1442:6;1450;1503:2;1491:9;1482:7;1478:23;1474:32;1471:52;;;1519:1;1516;1509:12;1471:52;1546:16;;-1:-1:-1;;;;;1574:30:190;;1571:50;;;1617:1;1614;1607:12;1571:50;1640:22;;1696:4;1678:16;;;1674:27;1671:47;;;1714:1;1711;1704:12;1671:47;1740:17;;:::i;:::-;1780:33;1810:2;1780:33;:::i;:::-;1773:5;1766:48;1846:42;1884:2;1880;1876:11;1846:42;:::i;:::-;1841:2;1830:14;;1823:66;1928:2;1920:11;;1914:18;-1:-1:-1;;;;;1944:32:190;;1941:52;;;1989:1;1986;1979:12;1941:52;2025:56;2073:7;2062:8;2058:2;2054:17;2025:56;:::i;:::-;2020:2;2009:14;;2002:80;-1:-1:-1;2121:2:190;2113:11;;2107:18;-1:-1:-1;;;;;2137:32:190;;2134:52;;;2182:1;2179;2172:12;2134:52;2218:56;2266:7;2255:8;2251:2;2247:17;2218:56;:::i;:::-;2213:2;2202:14;;2195:80;-1:-1:-1;2334:3:190;2326:12;;;2320:19;2355:15;;;2348:32;;;;2206:5;-1:-1:-1;2423:49:190;2468:2;2453:18;;2423:49;:::i;:::-;2413:59;;1329:1149;;;;;:::o;2483:380::-;2562:1;2558:12;;;;2605;;;2626:61;;2680:4;2672:6;2668:17;2658:27;;2626:61;2733:2;2725:6;2722:14;2702:18;2699:38;2696:161;;2779:10;2774:3;2770:20;2767:1;2760:31;2814:4;2811:1;2804:15;2842:4;2839:1;2832:15;2696:161;;2483:380;;;:::o;2994:518::-;3096:2;3091:3;3088:11;3085:421;;;3132:5;3129:1;3122:16;3176:4;3173:1;3163:18;3246:2;3234:10;3230:19;3227:1;3223:27;3217:4;3213:38;3282:4;3270:10;3267:20;3264:47;;;-1:-1:-1;3305:4:190;3264:47;3360:2;3355:3;3351:12;3348:1;3344:20;3338:4;3334:31;3324:41;;3415:81;3433:2;3426:5;3423:13;3415:81;;;3492:1;3478:16;;3459:1;3448:13;3415:81;;;3419:3;;3085:421;2994:518;;;:::o;3688:1299::-;3808:10;;-1:-1:-1;;;;;3830:30:190;;3827:56;;;3863:18;;:::i;:::-;3892:97;3982:6;3942:38;3974:4;3968:11;3942:38;:::i;:::-;3936:4;3892:97;:::i;:::-;4038:4;4069:2;4058:14;;4086:1;4081:649;;;;4774:1;4791:6;4788:89;;;-1:-1:-1;4843:19:190;;;4837:26;4788:89;-1:-1:-1;;3645:1:190;3641:11;;;3637:24;3633:29;3623:40;3669:1;3665:11;;;3620:57;4890:81;;4051:930;;4081:649;2941:1;2934:14;;;2978:4;2965:18;;-1:-1:-1;;4117:20:190;;;4235:222;4249:7;4246:1;4243:14;4235:222;;;4331:19;;;4325:26;4310:42;;4438:4;4423:20;;;;4391:1;4379:14;;;;4265:12;4235:222;;;4239:3;4485:6;4476:7;4473:19;4470:201;;;4546:19;;;4540:26;-1:-1:-1;;4629:1:190;4625:14;;;4641:3;4621:24;4617:37;4613:42;4598:58;4583:74;;4470:201;-1:-1:-1;;;;4717:1:190;4701:14;;;4697:22;4684:36;;-1:-1:-1;3688:1299:190:o;5200:301::-;5329:3;5367:6;5361:13;5413:6;5406:4;5398:6;5394:17;5389:3;5383:37;5475:1;5439:16;;5464:13;;;-1:-1:-1;5439:16:190;5200:301;-1:-1:-1;5200:301:190:o;5506:230::-;5576:6;5629:2;5617:9;5608:7;5604:23;5600:32;5597:52;;;5645:1;5642;5635:12;5597:52;-1:-1:-1;5690:16:190;;5506:230;-1:-1:-1;5506:230:190:o;6235:418::-;6384:2;6373:9;6366:21;6347:4;6416:6;6410:13;6459:6;6454:2;6443:9;6439:18;6432:34;6518:6;6513:2;6505:6;6501:15;6496:2;6485:9;6481:18;6475:50;6574:1;6569:2;6560:6;6549:9;6545:22;6541:31;6534:42;6644:2;6637;6633:7;6628:2;6620:6;6616:15;6612:29;6601:9;6597:45;6593:54;6585:62;;;6235:418;;;;:::o;6658:297::-;6776:12;;6823:4;6812:16;;;6806:23;;6776:12;6841:16;;6838:111;;;-1:-1:-1;;6915:4:190;6911:17;;;;6908:1;6904:25;6900:38;6889:50;;6658:297;-1:-1:-1;6658:297:190:o;:::-;718:3896:36;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "0x608060405234801561000f575f5ffd5b506004361061028b575f3560e01c80637b4e04e811610161578063b460af94116100ca578063d505accf11610084578063d505accf146105ef578063d905777e14610602578063dd62ed3e14610615578063e5adf3ab1461064d578063ef8b30f7146105a9578063f2fde38b14610660575f5ffd5b8063b460af9414610583578063ba08765214610596578063c63d75b6146103da578063c6e6f592146105a9578063c982681b146105bc578063ce96cb77146105dc575f5ffd5b806395d89b411161011b57806395d89b4114610508578063a1291f7f14610510578063a8aa1b3114610523578063a9059cbb1461054a578063ac2c3e1d1461055d578063b3d7f6b914610570575f5ffd5b80637b4e04e81461047c5780637ecebe001461048f57806382b36169146104a257806384b0196e146104c95780638da5cb5b146104e457806394bf804d146104f5575f5ffd5b806334aa983211610203578063********116101bd578063********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", "sourceMap": "718:3896:36:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2896:234:44;;;;;;:::i;:::-;;:::i;:::-;;;689:25:190;;;677:2;662:18;2896:234:44;;;;;;;;4024:142:36;;;:::i;1760:89:67:-;;;:::i;:::-;;;;;;;:::i;6234:148:73:-;;;;;;:::i;:::-;;:::i;3902:186:67:-;;;;;;:::i;:::-;;:::i;:::-;;;2012:14:190;;2005:22;1987:41;;1975:2;1960:18;3902:186:67;1847:187:190;7395:147:73;;;;;;:::i;:::-;;:::i;2803:97:67:-;2881:12;;2803:97;;4680:244;;;;;;:::i;:::-;;:::i;2163:134:44:-;;;;;;:::i;:::-;;:::i;:::-;;;-1:-1:-1;;;;;2716:32:190;;;2698:51;;2686:2;2671:18;2163:134:44;2552:203:190;1650:34:31;;;;;3901:117:36;;;:::i;:::-;;;2932:4:190;2920:17;;;2902:36;;2890:2;2875:18;3901:117:36;2760:184:190;3516:85:44;;;:::i;:::-;;2614:112:71;;;:::i;5762:94:73:-;5842:6;5762:94;;6417:108;;;;;;:::i;:::-;-1:-1:-1;;;6501:17:73;6417:108;1258:197:36;;;;;;:::i;:::-;;:::i;948:46:44:-;;;;;1800:218:36;;;;;;:::i;:::-;;:::i;7758:392:73:-;;;;;;:::i;:::-;;:::i;1775:136:44:-;;;;;;:::i;:::-;;:::i;3732:163:36:-;;;;;;:::i;:::-;;:::i;2293:101:53:-;;;:::i;3746:176:31:-;;;;;;:::i;:::-;;:::i;2292:162::-;;;;;;:::i;:::-;;:::i;1046:44:44:-;;;;;5228:557:85;;;:::i;:::-;;;;;;;;;;;;;:::i;1638:85:53:-;1710:6;;-1:-1:-1;;;;;1710:6:53;1638:85;;8185:380:73;;;;;;:::i;:::-;;:::i;1962:93:67:-;;;:::i;2300:143:36:-;;;;;;:::i;:::-;;:::i;1549:40:31:-;;;;;3244:178:67;;;;;;:::i;:::-;;:::i;1978:122:44:-;;;;;;:::i;:::-;;:::i;7217:143:73:-;;;;;;:::i;:::-;;:::i;8600:413::-;;;;;;:::i;:::-;;:::i;9048:405::-;;;;;;:::i;:::-;;:::i;6051:148::-;;;;;;:::i;:::-;;:::i;2359:129:44:-;;;;;;:::i;:::-;;:::i;:::-;;;;;;;:::i;6700:153:73:-;;;;;;:::i;:::-;;:::i;1668:672:71:-;;;;;;:::i;:::-;;:::i;6888:112:73:-;;;;;;:::i;:::-;;:::i;3455:140:67:-;;;;;;:::i;:::-;-1:-1:-1;;;;;3561:18:67;;;3535:7;3561:18;;;:11;:18;;;;;;;;:27;;;;;;;;;;;;;3455:140;3350:95:44;;;;;;:::i;:::-;;:::i;2543:215:53:-;;;;;;:::i;:::-;;:::i;2896:234:44:-;3003:7;2974:6;2390:12:47;:4;1591:12;392:1;1591:24;;1511:111;2390:12;2386:59;;;2411:34;;-1:-1:-1;;;2411:34:47;;;;;;;;;;;2386:59;3026:22:44::1;3034:7;3043:4;3026:7;:22::i;:::-;3022:84;;;-1:-1:-1::0;;;;;3024:18:67;;2998:7;3024:18;;;;;;;;;;;3064:31:44::1;;;;3022:84;3122:1;3115:8;;2455:1:47;2896:234:44::0;;;;;:::o;4024:142:36:-;4077:7;4128:4;-1:-1:-1;;;;;4103:43:36;;:45;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;4149:9;4103:56;;;;;;;:::i;:::-;;;;;-1:-1:-1;;;;;4096:63:36;;;4024:142;:::o;1760:89:67:-;1805:13;1837:5;1830:12;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1760:89;:::o;6234:148:73:-;6304:7;6330:45;6347:6;6355:19;6330:16;:45::i;:::-;6323:52;6234:148;-1:-1:-1;;6234:148:73:o;3902:186:67:-;3975:4;735:10:77;4029:31:67;735:10:77;4045:7:67;4054:5;4029:8;:31::i;:::-;-1:-1:-1;4077:4:67;;3902:186;-1:-1:-1;;;3902:186:67:o;7395:147:73:-;7465:7;7491:44;7508:6;7516:18;7491:16;:44::i;4680:244:67:-;4767:4;735:10:77;4823:37:67;4839:4;735:10:77;4854:5:67;4823:15;:37::i;:::-;4870:26;4880:4;4886:2;4890:5;4870:9;:26::i;:::-;-1:-1:-1;4913:4:67;;4680:244;-1:-1:-1;;;;4680:244:67:o;2163:134:44:-;-1:-1:-1;;;;;2265:15:44;;2239:7;2265:15;;;:6;:15;;;;;:25;;2284:5;2265:18;:25::i;:::-;2258:32;2163:134;-1:-1:-1;;;2163:134:44:o;3901:117:36:-;3971:5;3995:16;:14;:16::i;:::-;3988:23;;3901:117;:::o;3516:85:44:-;3567:27;3583:10;3567:15;:27::i;:::-;3516:85::o;2614:112:71:-;2673:7;2699:20;:18;:20::i;1258:197:36:-;1531:13:53;:11;:13::i;:::-;1402:2:36::1;-1:-1:-1::0;;;;;1386:35:36::1;1394:6;-1:-1:-1::0;;;;;1386:35:36::1;;1406:6;1414;1386:35;;;;;;9878:25:190::0;;;9934:2;9919:18;;9912:34;9866:2;9851:18;;9704:248;1386:35:36::1;;;;;;;;1431:17;1437:2;1441:6;1431:5;:17::i;:::-;1258:197:::0;;;;:::o;1800:218::-;1531:13:53;:11;:13::i;:::-;1928:48:36::1;::::0;;9878:25:190;;;9934:2;9919:18;;9912:34;;;-1:-1:-1;;;;;1928:48:36;;::::1;::::0;;;::::1;::::0;1937:10:::1;::::0;1928:48:::1;::::0;9851:18:190;1928:48:36::1;;;;;;;1986:25;1992:10;2004:6;1986:5;:25::i;7758:392:73:-:0;7833:7;-1:-1:-1;;7902:110:73;;7947:54;;;;;;;;7902:110;8022:14;8039:22;8054:6;8039:14;:22::i;:::-;8022:39;-1:-1:-1;8071:48:73;735:10:77;8094:8:73;8104:6;8112;8071:8;:48::i;:::-;8137:6;7758:392;-1:-1:-1;;;;7758:392:73:o;1775:136:44:-;-1:-1:-1;;;;;1874:15:44;;1851:4;1874:15;;;:6;:15;;;;;:30;;1899:4;1874:24;:30::i;3732:163:36:-;3838:7;3864:24;3880:7;3864:15;:24::i;2293:101:53:-;1531:13;:11;:13::i;:::-;2357:30:::1;2384:1;2357:18;:30::i;3746:176:31:-:0;3821:36;;-1:-1:-1;;;3821:36:31;;-1:-1:-1;;;;;2716:32:190;;;3821:36:31;;;2698:51:190;3821:14:31;:30;;;;2671:18:190;;3821:36:31;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;3817:99;;;3873:32;3888:10;3900:4;3873:14;:32::i;:::-;3746:176;:::o;2292:162::-;2402:7;2428:19;2441:5;2428:12;:19::i;5228:557:85:-;5326:13;5353:18;5385:21;5420:15;5449:25;5488:12;5514:27;5617:13;:11;:13::i;:::-;5644:16;:14;:16::i;:::-;5752;;;5736:1;5752:16;;;;;;;;;-1:-1:-1;;;5566:212:85;;;-1:-1:-1;5566:212:85;;-1:-1:-1;5674:13:85;;-1:-1:-1;5709:4:85;;-1:-1:-1;5736:1:85;-1:-1:-1;5752:16:85;-1:-1:-1;5566:212:85;-1:-1:-1;5228:557:85:o;8185:380:73:-;8257:7;-1:-1:-1;;8440:14:73;8457:19;8469:6;8457:11;:19::i;:::-;8440:36;-1:-1:-1;8486:48:73;735:10:77;8509:8:73;8519:6;8527;8486:8;:48::i;1962:93:67:-;2009:13;2041:7;2034:14;;;;;:::i;2300:143:36:-;1531:13:53;:11;:13::i;:::-;2409:27:36::1;2419:4;2425:2;2429:6;2409:9;:27::i;:::-;2300:143:::0;;;:::o;3244:178:67:-;3313:4;735:10:77;3367:27:67;735:10:77;3384:2:67;3388:5;3367:9;:27::i;1978:122:44:-;-1:-1:-1;;;;;2069:15:44;;2043:7;2069:15;;;:6;:15;;;;;:24;;:22;:24::i;7217:143:73:-;7283:7;7309:44;7326:6;7334:18;7309:16;:44::i;8600:413::-;8691:7;8710:17;8730:18;8742:5;8730:11;:18::i;:::-;8710:38;;8771:9;8762:6;:18;8758:108;;;8830:5;8837:6;8845:9;8803:52;;-1:-1:-1;;;8803:52:73;;;;;;;;;;:::i;8758:108::-;8876:14;8893:23;8909:6;8893:15;:23::i;:::-;8876:40;-1:-1:-1;8926:56:73;735:10:77;8950:8:73;8960:5;8967:6;8975;8926:9;:56::i;:::-;9000:6;8600:413;-1:-1:-1;;;;;8600:413:73:o;9048:405::-;9137:7;9156:17;9176:16;9186:5;9176:9;:16::i;:::-;9156:36;;9215:9;9206:6;:18;9202:106;;;9272:5;9279:6;9287:9;9247:50;;-1:-1:-1;;;9247:50:73;;;;;;;;;;:::i;9202:106::-;9318:14;9335:21;9349:6;9335:13;:21::i;:::-;9318:38;-1:-1:-1;9366:56:73;735:10:77;9390:8:73;9400:5;9407:6;9415;9366:9;:56::i;6051:148::-;6121:7;6147:45;6164:6;6172:19;6147:16;:45::i;2359:129:44:-;-1:-1:-1;;;;;2454:15:44;;;;;;:6;:15;;;;;2419:16;;2454:27;;:25;:27::i;6700:153:73:-;6765:7;6791:55;6808:16;6818:5;6808:9;:16::i;:::-;6826:19;6791:16;:55::i;1668:672:71:-;1889:8;1871:15;:26;1867:97;;;1920:33;;-1:-1:-1;;;1920:33:71;;;;;689:25:190;;;662:18;;1920:33:71;543:177:190;1867:97:71;1974:18;1024:95;2033:5;2040:7;2049:5;2056:16;2066:5;-1:-1:-1;;;;;1121:14:78;819:7;1121:14;;;:7;:14;;;;;:16;;;;;;;;;759:395;2056:16:71;2005:78;;;;;;10876:25:190;;;;-1:-1:-1;;;;;10937:32:190;;;10917:18;;;10910:60;11006:32;;;;10986:18;;;10979:60;11055:18;;;11048:34;11098:19;;;11091:35;11142:19;;;11135:35;;;10848:19;;2005:78:71;;;;;;;;;;;;1995:89;;;;;;1974:110;;2095:12;2110:28;2127:10;2110:16;:28::i;:::-;2095:43;;2149:14;2166:28;2180:4;2186:1;2189;2192;2166:13;:28::i;:::-;2149:45;;2218:5;-1:-1:-1;;;;;2208:15:71;:6;-1:-1:-1;;;;;2208:15:71;;2204:88;;2246:35;;-1:-1:-1;;;2246:35:71;;-1:-1:-1;;;;;11373:32:190;;;2246:35:71;;;11355:51:190;11442:32;;11422:18;;;11415:60;11328:18;;2246:35:71;11181:300:190;2204:88:71;2302:31;2311:5;2318:7;2327:5;2302:8;:31::i;:::-;1857:483;;;1668:672;;;;;;;:::o;6888:112:73:-;6951:7;6977:16;6987:5;6977:9;:16::i;3350:95:44:-;3409:29;3421:10;3433:4;3409:11;:29::i;2543:215:53:-;1531:13;:11;:13::i;:::-;-1:-1:-1;;;;;2627:22:53;::::1;2623:91;;2672:31;::::0;-1:-1:-1;;;2672:31:53;;2700:1:::1;2672:31;::::0;::::1;2698:51:190::0;2671:18;;2672:31:53::1;2552:203:190::0;2623:91:53::1;2723:28;2742:8;2723:18;:28::i;4395:217:36:-:0;4493:7;4519:86;4536:6;4544:13;:11;:13::i;:::-;2881:12:67;;4586:18:36;4574:8;:30;;;;;;;;:::i;:::-;;4519:16;:86::i;8630:128:67:-;8714:37;8723:5;8730:7;8739:5;8746:4;8714:8;:37::i;4172:217:36:-;4270:7;4296:86;4313:6;4321:13;:11;:13::i;:::-;2881:12:67;;4363:18:36;4351:8;:30;;;;;;;;:::i;:::-;;4296:16;:86::i;10319:476:67:-;-1:-1:-1;;;;;3561:18:67;;;10418:24;3561:18;;;:11;:18;;;;;;;;:27;;;;;;;;;;-1:-1:-1;;10484:36:67;;10480:309;;;10559:5;10540:16;:24;10536:130;;;10618:7;10627:16;10645:5;10591:60;;-1:-1:-1;;;10591:60:67;;;;;;;;;;:::i;10536:130::-;10707:57;10716:5;10723:7;10751:5;10732:16;:24;10758:5;10707:8;:57::i;5297:300::-;-1:-1:-1;;;;;5380:18:67;;5376:86;;5421:30;;-1:-1:-1;;;5421:30:67;;5448:1;5421:30;;;2698:51:190;2671:18;;5421:30:67;2552:203:190;5376:86:67;-1:-1:-1;;;;;5475:16:67;;5471:86;;5514:32;;-1:-1:-1;;;5514:32:67;;5543:1;5514:32;;;2698:51:190;2671:18;;5514:32:67;2552:203:190;5471:86:67;5566:24;5574:4;5580:2;5584:5;5566:7;:24::i;1203:116:41:-;1269:7;1295:17;:1;1306:5;1295:10;:17::i;2848:128:31:-;2929:5;2953:16;:14;:16::i;4532:499:44:-;-1:-1:-1;;;;;4630:15:44;;4601:26;4630:15;;;:6;:15;;;;;:23;;:21;:23::i;:::-;4601:52;;4663:15;4681:18;4691:7;4681:9;:18::i;:::-;4663:36;-1:-1:-1;4738:9:44;4733:282;4757:9;:16;4753:1;:20;4733:282;;;4798:12;4813:9;4823:1;4813:12;;;;;;;;:::i;:::-;;;;;;;4798:27;;4848:26;4860:7;4869:4;4848:26;;;;;;-1:-1:-1;;;;;11373:32:190;;;11355:51;;11442:32;;11437:2;11422:18;;11415:60;11343:2;11328:18;;11181:300;4848:26:44;;;;;;;;4896:11;;4892:109;;4931:51;4947:4;4953:7;4970:1;4974:7;4931:15;:51::i;:::-;-1:-1:-1;4775:3:44;;4733:282;;3945:262:85;3998:7;4029:4;-1:-1:-1;;;;;4038:11:85;4021:28;;:63;;;;;4070:14;4053:13;:31;4021:63;4017:184;;;-1:-1:-1;4107:22:85;;3945:262::o;4017:184::-;4167:23;4304:80;;;2079:95;4304:80;;;12439:25:190;4326:11:85;12480:18:190;;;12473:34;;;;4339:14:85;12523:18:190;;;12516:34;4355:13:85;12566:18:190;;;12559:34;4378:4:85;12609:19:190;;;12602:61;4268:7:85;;12411:19:190;;4304:80:85;;;;;;;;;;;;4294:91;;;;;;4287:98;;4213:179;;1796:162:53;1710:6;;-1:-1:-1;;;;;1710:6:53;735:10:77;1855:23:53;1851:101;;1901:40;;-1:-1:-1;;;1901:40:53;;735:10:77;1901:40:53;;;2698:51:190;2671:18;;1901:40:53;2552:203:190;7362:208:67;-1:-1:-1;;;;;7432:21:67;;7428:91;;7476:32;;-1:-1:-1;;;7476:32:67;;7505:1;7476:32;;;2698:51:190;2671:18;;7476:32:67;2552:203:190;7428:91:67;7528:35;7544:1;7548:7;7557:5;7528:7;:35::i;:::-;7362:208;;:::o;7888:206::-;-1:-1:-1;;;;;7958:21:67;;7954:89;;8002:30;;-1:-1:-1;;;8002:30:67;;8029:1;8002:30;;;2698:51:190;2671:18;;8002:30:67;2552:203:190;7954:89:67;8052:35;8060:7;8077:1;8081:5;8052:7;:35::i;2656:357:36:-;2876:74;5842:6:73;2920::36;2936:4;2943:6;2876:26;:74::i;:::-;2960:46;;-1:-1:-1;;;2960:46:36;;-1:-1:-1;;;;;2716:32:190;;;2960:46:36;;;2698:51:190;2982:4:36;2960:36;;;;2671:18:190;;2960:46:36;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2656:357;;;;:::o;1950:175:41:-;-1:-1:-1;;;;;2053:14:41;;2021:4;2053:14;;;-1:-1:-1;;;2053:8:41;;:14;;;;;;2084:10;;;;;:34;;-1:-1:-1;;;2098:20:41;;;2077:41;-1:-1:-1;;;1950:175:41:o;2670:172:31:-;2785:7;2811:24;2827:7;2811:15;:24::i;2912:187:53:-;3004:6;;;-1:-1:-1;;;;;3020:17:53;;;-1:-1:-1;;;;;;3020:17:53;;;;;;;3052:40;;3004:6;;;3020:17;3004:6;;3052:40;;2985:16;;3052:40;2975:124;2912:187;:::o;3607:581:44:-;-1:-1:-1;;;;;3687:18:44;;3683:51;;3714:20;;-1:-1:-1;;;3714:20:44;;;;;;;;;;;3683:51;3791:4;-1:-1:-1;;;;;3748:49:44;3754:4;-1:-1:-1;;;;;3748:17:44;;:19;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;-1:-1:-1;;;;;3748:49:44;;3744:82;;3806:20;;-1:-1:-1;;;3806:20:44;;;;;;;;;;;3744:82;-1:-1:-1;;;;;3841:15:44;;;;;;:6;:15;;;;;:25;;3861:4;3841:19;:25::i;:::-;3836:57;;3875:18;;-1:-1:-1;;;3875:18:44;;;;;;;;;;;3836:57;-1:-1:-1;;;;;3907:15:44;;;;;;:6;:15;;;;;3934:21;;3907:24;;:22;:24::i;:::-;:48;3903:90;;;3964:29;;-1:-1:-1;;;3964:29:44;;;;;;;;;;;3903:90;4009:24;;;-1:-1:-1;;;;;11373:32:190;;;11355:51;;11442:32;;11437:2;11422:18;;11415:60;4009:24:44;;11328:18:190;4009:24:44;;;;;;;4043:15;4061:18;4071:7;4061:9;:18::i;:::-;4043:36;-1:-1:-1;4093:11:44;;4089:93;;4120:51;4136:4;4150:1;4154:7;4163;4120:15;:51::i;2379:143:71:-;-1:-1:-1;;;;;624:14:78;;2470:7:71;624:14:78;;;:7;:14;;;;;;2496:19:71;538:107:78;6105:126:85;6151:13;6183:41;:5;6210:13;6183:26;:41::i;6557:135::-;6606:13;6638:47;:8;6668:16;6638:29;:47::i;821:104:41:-;1770:12:40;;876:7:41;;1331:3:40;1769:47;;;902:16:41;1685:138:40;3140:392:36;3388:80;3430:4;3438:6;3454:4;3461:6;3388:26;:80::i;:::-;3478:47;;-1:-1:-1;;;3478:47:36;;-1:-1:-1;;;;;2716:32:190;;;3478:47:36;;;2698:51:190;3500:4:36;3478:37;;;;2671:18:190;;3478:47:36;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3140:392;;;;;:::o;3007:919:40:-;3062:23;3207:9;3201:16;-1:-1:-1;;;3265:14:40;3261:33;3245:14;3241:54;-1:-1:-1;;;;;3323:14:40;3319:34;3308:45;;3413:4;3407:11;3397:21;;3473:3;3470:1;3466:11;3460:4;3456:22;3448:6;3444:35;3438:4;3431:49;3508:3;3500:6;3493:19;3529:3;3526:384;;;3640:4;3632:6;3628:17;3674:3;3669;3662:16;3710:1;3695:201;3720:3;3717:1;3714:10;3695:201;;;3787:17;;;3781:24;-1:-1:-1;;;;;3777:44:40;3862:4;3858:12;;3849:22;;3842:36;3738:1;3731:9;3695:201;;;3699:14;;3526:384;;;3007:919;;;:::o;5017:176:85:-;5094:7;5120:66;5153:20;:18;:20::i;:::-;5175:10;4049:4:86;4043:11;-1:-1:-1;;;4067:23:86;;4119:4;4110:14;;4103:39;;;;4171:4;4162:14;;4155:34;4227:4;4212:20;;;3874:374;8225:260:84;8310:7;8330:17;8349:18;8369:16;8389:25;8400:4;8406:1;8409;8412;8389:10;:25::i;:::-;8329:85;;;;;;8424:28;8436:5;8443:8;8424:11;:28::i;:::-;-1:-1:-1;8469:9:84;;8225:260;-1:-1:-1;;;;;;8225:260:84:o;4194:332:44:-;-1:-1:-1;;;;;4278:15:44;;;;;;:6;:15;;;;;:28;;4301:4;4278:22;:28::i;:::-;4273:56;;4315:14;;-1:-1:-1;;;4315:14:44;;;;;;;;;;;4273:56;4345:26;;;-1:-1:-1;;;;;11373:32:190;;;11355:51;;11442:32;;11437:2;11422:18;;11415:60;4345:26:44;;11328:18:190;4345:26:44;;;;;;;4381:15;4399:18;4409:7;4399:9;:18::i;:::-;4381:36;-1:-1:-1;4431:11:44;;4427:93;;4458:51;4474:4;4480:7;4497:1;4501:7;4458:15;:51::i;1174:361:20:-;1328:15;1359:11;1374:1;1359:16;1355:105;;-1:-1:-1;1398:6:20;1391:13;;1355:105;1476:52;1483:6;1491:11;1504;1517:10;1476:6;:52::i;9605:432:67:-;-1:-1:-1;;;;;9717:19:67;;9713:89;;9759:32;;-1:-1:-1;;;9759:32:67;;9788:1;9759:32;;;2698:51:190;2671:18;;9759:32:67;2552:203:190;9713:89:67;-1:-1:-1;;;;;9815:21:67;;9811:90;;9859:31;;-1:-1:-1;;;9859:31:67;;9887:1;9859:31;;;2698:51:190;2671:18;;9859:31:67;2552:203:190;9811:90:67;-1:-1:-1;;;;;9910:18:67;;;;;;;:11;:18;;;;;;;;:27;;;;;;;;;:35;;;9955:76;;;;10005:7;-1:-1:-1;;;;;9989:31:67;9998:5;-1:-1:-1;;;;;9989:31:67;;10014:5;9989:31;;;;689:25:190;;677:2;662:18;;543:177;9989:31:67;;;;;;;;9605:432;;;;:::o;1541:361:20:-;1695:15;1726:11;1741:1;1726:16;1722:105;;-1:-1:-1;1765:6:20;1758:13;;1722:105;1843:52;1850:6;1858:11;1871;1884:10;1843:6;:52::i;3575:151:36:-;3688:31;3702:4;3708:2;3712:6;3688:13;:31::i;2122:202:40:-;1770:12;;2187:7;;2226:1;;1331:3;1769:47;;;2210:17;2206:48;;2236:18;;-1:-1:-1;;;2236:18:40;;;;;;;;;;;2206:48;-1:-1:-1;;;;;2287:4:40;2297:1;-1:-1:-1;;;2287:12:40;;;;;;:::i;:::-;;;:28;;2122:202;-1:-1:-1;;;2122:202:40:o;5576:151:73:-;5657:5;5681:39;5657:5;5681:19;:39;:::i;4144:370:41:-;4192:22;4234:13;:1;:11;:13::i;:::-;4271:12;;4226:21;;-1:-1:-1;4297:7:41;;4293:215;;-1:-1:-1;;;;;9056:32:40;;4382:9:41;4377:107;4401:3;4397:1;:7;4377:107;;;-1:-1:-1;;4433:1:41;-1:-1:-1;;;4433:8:41;:18;4442:5;4448:1;4442:8;;;;;;;;:::i;:::-;;;;;;;;;;;;-1:-1:-1;;;;;4433:18:41;;;;;;;;;;;-1:-1:-1;4433:18:41;:32;4406:3;;4377:107;;4293:215;4216:298;4144:370;;;:::o;5335:785:44:-;5638:4;5632:11;-1:-1:-1;;;5656:21:44;;;5706:4;5697:14;;5690:28;;;5747:4;5738:14;;5731:26;;;5786:4;5777:14;;5770:30;;;5452:29;5510:19;;5829:5;5895:1;5892;5886:4;5881:3;5878:1;5872:4;5862:8;5857:40;5847:257;;5950:8;5945:2;5940;5931:7;5927:16;5923:25;5920:39;5917:173;;;6005:16;6002:1;5997:3;5982:40;6055:16;6050:3;6043:29;1662:232:76;1767:47;1785:5;1792:4;1798:2;1802:5;1809:4;1767:17;:47::i;:::-;1762:126;;1837:40;;-1:-1:-1;;;1837:40:76;;-1:-1:-1;;;;;2716:32:190;;1837:40:76;;;2698:51:190;2671:18;;1837:40:76;2552:203:190;2656:170:44;2769:7;2716:6;2390:12:47;:4;1591:12;392:1;1591:24;;1511:111;2390:12;2386:59;;;2411:34;;-1:-1:-1;;;2411:34:47;;;;;;;;;;;2386:59;-1:-1:-1;;;;;;;3024:18:67;2998:7;3024:18;;;;;;;;;;;;2656:170:44:o;3001:266:41:-;-1:-1:-1;;;;;3094:14:41;;3062:4;3094:14;;;-1:-1:-1;;;3094:8:41;;:14;;;;;;3122:10;;;;;:34;;;-1:-1:-1;;3136:5:41;:20;;3122:34;3118:77;;;3179:5;3172:12;;;;;3118:77;3221:18;:1;3234:4;3221:12;:18::i;:::-;-1:-1:-1;;;;;3204:14:41;;;;;;-1:-1:-1;;;3204:8:41;;:14;;;;;:35;-1:-1:-1;3256:4:41;;-1:-1:-1;3001:266:41;;;;:::o;3368:267:81:-;3462:13;1390:66;3491:46;;3487:142;;3560:15;3569:5;3560:8;:15::i;:::-;3553:22;;;;3487:142;3613:5;3606:12;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;6541:1551:84;6667:17;;;7621:66;7608:79;;7604:164;;;-1:-1:-1;7719:1:84;;-1:-1:-1;7723:30:84;;-1:-1:-1;7755:1:84;7703:54;;7604:164;7879:24;;;7862:14;7879:24;;;;;;;;;12901:25:190;;;12974:4;12962:17;;12942:18;;;12935:45;;;;12996:18;;;12989:34;;;13039:18;;;13032:34;;;7879:24:84;;12873:19:190;;7879:24:84;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;7879:24:84;;-1:-1:-1;;7879:24:84;;;-1:-1:-1;;;;;;;7917:20:84;;7913:113;;-1:-1:-1;7969:1:84;;-1:-1:-1;7973:29:84;;-1:-1:-1;7969:1:84;;-1:-1:-1;7953:62:84;;7913:113;8044:6;-1:-1:-1;8052:20:84;;-1:-1:-1;8052:20:84;;-1:-1:-1;6541:1551:84;;;;;;;;;:::o;8618:532::-;8713:20;8704:5;:29;;;;;;;;:::i;:::-;;8700:444;;8618:532;;:::o;8700:444::-;8809:29;8800:5;:38;;;;;;;;:::i;:::-;;8796:348;;8861:23;;-1:-1:-1;;;8861:23:84;;;;;;;;;;;8796:348;8914:35;8905:5;:44;;;;;;;;:::i;:::-;;8901:243;;8972:46;;-1:-1:-1;;;8972:46:84;;;;;689:25:190;;;662:18;;8972:46:84;543:177:190;8901:243:84;9048:30;9039:5;:39;;;;;;;;:::i;:::-;;9035:109;;9101:32;;-1:-1:-1;;;9101:32:84;;;;;689:25:190;;;662:18;;9101:32:84;543:177:190;3518:482:41;-1:-1:-1;;;;;3614:14:41;;3582:4;3614:14;;;-1:-1:-1;;;3614:8:41;;:14;;;;;;;-1:-1:-1;;3638:28:41;;;3680:10;;;:34;;;-1:-1:-1;;3694:5:41;:20;3680:34;3676:77;;;3737:5;3730:12;;;;;3676:77;3763:16;3782;:1;:14;:16::i;:::-;3763:35;;3824:4;-1:-1:-1;;;;;3812:16:41;:8;-1:-1:-1;;;;;3812:16:41;;3808:165;;3872:32;:1;-1:-1:-1;;3884:9:41;;3895:8;3872:11;:32::i;:::-;-1:-1:-1;;;;;3922:18:41;;;;;;-1:-1:-1;;;3922:8:41;;:18;;;;;:26;;;-1:-1:-1;3989:4:41;;3518:482;-1:-1:-1;;;;3518:482:41:o;1908:204:20:-;1997:14;2032:5;2036:1;2032;:5;:::i;:::-;2023:14;;2056:10;:49;;2095:10;2104:1;2095:6;:10;:::i;:::-;2056:49;;;2069:23;2082:6;2090:1;2069:12;:23::i;2982:758:31:-;3096:31;3110:4;3116:2;3120:6;3096:13;:31::i;:::-;-1:-1:-1;;;;;3141:20:31;;;;;;:42;;-1:-1:-1;;;;;;3165:18:31;;;;3141:42;:80;;;;-1:-1:-1;3188:33:31;;;;3187:34;3141:80;3137:597;;;3294:16;3324:14;3352:13;443:1:19;3438:9:31;:28;3434:227;;;3497:4;3486:15;;3528:2;3519:11;;3434:227;;;-1:-1:-1;3580:2:31;;-1:-1:-1;3609:4:31;;-1:-1:-1;3642:4:31;3434:227;3674:49;;-1:-1:-1;;;3674:49:31;;-1:-1:-1;;;;;13818:32:190;;;3674:49:31;;;13800:51:190;13887:32;;;13867:18;;;13860:60;13963:14;;13956:22;13936:18;;;13929:50;3674:4:31;:21;;;;13773:18:190;;3674:49:31;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;10151:1384:76;10446:4;10440:11;-1:-1:-1;;;10309:12:76;10464:22;;;-1:-1:-1;;;;;10512:26:76;;;10506:4;10499:40;10565:24;;10559:4;10552:38;10610:4;10603:19;;;10309:12;10680:4;10309:12;10671:4;10309:12;;10658:5;10651;10646:39;10635:50;;10897:1;10890:4;10884:11;10881:18;10872:7;10868:32;10858:603;;11029:6;11019:7;11012:15;11008:28;11005:162;;;11082:16;11079:1;11074:3;11059:40;11132:16;11127:3;11120:29;11005:162;11443:1;11435:5;11423:18;11420:25;11401:16;11394:24;11390:56;11381:7;11377:70;11366:81;;10858:603;11481:4;11474:17;-1:-1:-1;11517:1:76;11511:4;11504:15;10151:1384;;-1:-1:-1;;;;;10151:1384:76:o;5389:631:40:-;5457:11;5590:9;5584:16;-1:-1:-1;;;5648:14:40;5644:33;5628:14;5624:54;5699:3;5720:1;5715:82;;;;-1:-1:-1;;;5858:14:40;5854:32;5843:9;5836:51;-1:-1:-1;;;5935:7:40;5932:26;5926:3;5915:9;5911:19;5904:55;5692:281;;5715:82;-1:-1:-1;;;5761:7:40;5758:24;5747:9;5740:43;5692:281;-1:-1:-1;6002:1:40;5993:11;;5389:631;-1:-1:-1;;;;5389:631:40:o;2078:378:81:-;2137:13;2162:11;2176:16;2187:4;2176:10;:16::i;:::-;2300:14;;;2311:2;2300:14;;;;;;;;;2162:30;;-1:-1:-1;2280:17:81;;2300:14;;;;;;;;;-1:-1:-1;;;2363:16:81;;;-1:-1:-1;2408:4:81;2399:14;;2392:28;;;;-1:-1:-1;2363:16:81;2078:378::o;6997:781:40:-;7225:16;;7049:11;;-1:-1:-1;;;7085:26:40;7265:54;7269:14;7265:54;;;;;7356:82;;;;7456:1;7451:129;;;;-1:-1:-1;;;;;7660:1:40;7655:3;7651:11;7640:9;7636:27;7630:34;7626:54;7619:61;;-1:-1:-1;;;7719:14:40;7715:32;7704:9;7697:51;7333:429;;7356:82;7391:3;7388:1;7381:14;7422:1;7419;7412:12;7451:129;-1:-1:-1;;;;;7534:32:40;;;-1:-1:-1;;;;;;;7483:34:40;;6997:781::o;8043:762::-;8278:16;;-1:-1:-1;;;8139:25:40;8318:54;8322:14;8318:54;;;;-1:-1:-1;;;;;8396:34:40;;8454:14;;;8444:101;;8498:3;8495:1;8488:14;8529:1;8526;8519:12;8444:101;8566:5;8584:95;;;;-1:-1:-1;;;8751:7:40;8748:26;8740:5;8729:9;8725:21;8718:57;8559:230;;8584:95;-1:-1:-1;8630:24:40;;;;8627:37;;;8609:56;;;-1:-1:-1;;;8043:762:40:o;6215:704:89:-;6277:7;6300:1;6305;6300:6;6296:150;;6400:35;1035:4:79;6400:11:89;:35::i;:::-;6896:1;6891;6887;:5;6886:11;;;;;:::i;:::-;;6900:1;6886:15;6876:5;;;6860:42;;6215:704;-1:-1:-1;;;6215:704:89:o;6126:1626:44:-;6207:6;2073:12:47;:4;:10;:12::i;:::-;6242:31:44::1;6256:4;6262:2;6266:6;6242:13;:31::i;:::-;6321:1;6312:6;:10;:24;;;;;6334:2;-1:-1:-1::0;;;;;6326:10:44::1;:4;-1:-1:-1::0;;;;;6326:10:44::1;;;6312:24;6308:1428;;;-1:-1:-1::0;;;;;6385:12:44;::::1;6356:26;6385:12:::0;;;:6:::1;:12;::::0;;;;:24:::1;::::0;:22:::1;:24::i;:::-;-1:-1:-1::0;;;;;6454:10:44;::::1;6427:24;6454:10:::0;;;:6:::1;:10;::::0;;;;6356:53;;-1:-1:-1;6427:24:44;6454:22:::1;::::0;:20:::1;:22::i;:::-;6520:16:::0;;6578:14;;6427:49;;-1:-1:-1;6520:16:44;6494:23:::1;6611:762;6635:15;6631:1;:19;6611:762;;;6679:12;6694:9;6704:1;6694:12;;;;;;;;:::i;:::-;;;;;;;6679:27;;6729:9;6769:1;6765:5;;6760:371;6776:13;6772:1;:17;6760:371;;;6834:7;6842:1;6834:10;;;;;;;;:::i;:::-;;;;;;;-1:-1:-1::0;;;;;6826:18:44::1;:4;-1:-1:-1::0;;;;;6826:18:44::1;::::0;6822:287:::1;;6955:39;6971:4;6977;6983:2;6987:6;6955:15;:39::i;:::-;7045:1;7024:7;7032:1;7024:10;;;;;;;;:::i;:::-;;;;;;:23;-1:-1:-1::0;;;;;7024:23:44::1;;;-1:-1:-1::0;;;;;7024:23:44::1;;;::::0;::::1;7077:5;;6822:287;6791:3;;6760:371;;;7162:13;7157:1;:18:::0;7153:202:::1;;7285:47;7301:4;7307;7321:1;7325:6;7285:15;:47::i;:::-;-1:-1:-1::0;;6652:3:44::1;;6611:762;;;-1:-1:-1::0;7396:9:44::1;7391:331;7415:13;7411:1;:17;7391:331;;;7457:12;7472:7;7480:1;7472:10;;;;;;;;:::i;:::-;;;;;;;7457:25;;7524:1;-1:-1:-1::0;;;;;7508:18:44::1;:4;-1:-1:-1::0;;;;;7508:18:44::1;;7504:200;;7636:45;7652:4;7666:1;7670:2;7674:6;7636:15;:45::i;:::-;-1:-1:-1::0;7430:3:44::1;;7391:331;;;;6338:1398;;;;6308:1428;349:1:47::0;1238:27;;2106:11;1186:86;2528:245:81;2589:7;2661:4;2625:40;;2688:2;2679:11;;2675:69;;;2713:20;;-1:-1:-1;;;2713:20:81;;;;;;;;;;;1776:194:79;1881:10;1875:4;1868:24;1918:4;1912;1905:18;1949:4;1943;1936:18;917:145:47;974:12;;-1:-1:-1;;974:24:47;970:52;;1007:15;;-1:-1:-1;;;1007:15:47;;;;;;;;;;;970:52;392:1;1032:23;;917:145::o;5912:1107:67:-;-1:-1:-1;;;;;6001:18:67;;5997:540;;6153:5;6137:12;;:21;;;;;;;:::i;:::-;;;;-1:-1:-1;5997:540:67;;-1:-1:-1;5997:540:67;;-1:-1:-1;;;;;6211:15:67;;6189:19;6211:15;;;;;;;;;;;6244:19;;;6240:115;;;6315:4;6321:11;6334:5;6290:50;;-1:-1:-1;;;6290:50:67;;;;;;;;;;:::i;6240:115::-;-1:-1:-1;;;;;6475:15:67;;:9;:15;;;;;;;;;;6493:19;;;;6475:37;;5997:540;-1:-1:-1;;;;;6551:16:67;;6547:425;;6714:12;:21;;;;;;;6547:425;;;-1:-1:-1;;;;;6925:13:67;;:9;:13;;;;;;;;;;:22;;;;;;6547:425;7002:2;-1:-1:-1;;;;;6987:25:67;6996:4;-1:-1:-1;;;;;6987:25:67;;7006:5;6987:25;;;;689::190;;677:2;662:18;;543:177;6987:25:67;;;;;;;;5912:1107;;;:::o;14:131:190:-;-1:-1:-1;;;;;89:31:190;;79:42;;69:70;;135:1;132;125:12;150:388;218:6;226;279:2;267:9;258:7;254:23;250:32;247:52;;;295:1;292;285:12;247:52;334:9;321:23;353:31;378:5;353:31;:::i;:::-;403:5;-1:-1:-1;460:2:190;445:18;;432:32;473:33;432:32;473:33;:::i;:::-;525:7;515:17;;;150:388;;;;;:::o;725:289::-;767:3;805:5;799:12;832:6;827:3;820:19;888:6;881:4;874:5;870:16;863:4;858:3;854:14;848:47;940:1;933:4;924:6;919:3;915:16;911:27;904:38;1003:4;996:2;992:7;987:2;979:6;975:15;971:29;966:3;962:39;958:50;951:57;;;725:289;;;;:::o;1019:220::-;1168:2;1157:9;1150:21;1131:4;1188:45;1229:2;1218:9;1214:18;1206:6;1188:45;:::i;1244:226::-;1303:6;1356:2;1344:9;1335:7;1331:23;1327:32;1324:52;;;1372:1;1369;1362:12;1324:52;-1:-1:-1;1417:23:190;;1244:226;-1:-1:-1;1244:226:190:o;1475:367::-;1543:6;1551;1604:2;1592:9;1583:7;1579:23;1575:32;1572:52;;;1620:1;1617;1610:12;1572:52;1659:9;1646:23;1678:31;1703:5;1678:31;:::i;:::-;1728:5;1806:2;1791:18;;;;1778:32;;-1:-1:-1;;;1475:367:190:o;2039:508::-;2116:6;2124;2132;2185:2;2173:9;2164:7;2160:23;2156:32;2153:52;;;2201:1;2198;2191:12;2153:52;2240:9;2227:23;2259:31;2284:5;2259:31;:::i;:::-;2309:5;-1:-1:-1;2366:2:190;2351:18;;2338:32;2379:33;2338:32;2379:33;:::i;:::-;2039:508;;2431:7;;-1:-1:-1;;;2511:2:190;2496:18;;;;2483:32;;2039:508::o;3131:247::-;3190:6;3243:2;3231:9;3222:7;3218:23;3214:32;3211:52;;;3259:1;3256;3249:12;3211:52;3298:9;3285:23;3317:31;3342:5;3317:31;:::i;3383:629::-;3469:6;3477;3485;3493;3546:3;3534:9;3525:7;3521:23;3517:33;3514:53;;;3563:1;3560;3553:12;3514:53;3602:9;3589:23;3621:31;3646:5;3621:31;:::i;:::-;3671:5;-1:-1:-1;3728:2:190;3713:18;;3700:32;3741:33;3700:32;3741:33;:::i;:::-;3383:629;;3793:7;;-1:-1:-1;;;;3873:2:190;3858:18;;3845:32;;3976:2;3961:18;3948:32;;3383:629::o;4017:367::-;4085:6;4093;4146:2;4134:9;4125:7;4121:23;4117:32;4114:52;;;4162:1;4159;4152:12;4114:52;4207:23;;;-1:-1:-1;4306:2:190;4291:18;;4278:32;4319:33;4278:32;4319:33;:::i;4389:1238::-;4795:3;4790;4786:13;4778:6;4774:26;4763:9;4756:45;4837:3;4832:2;4821:9;4817:18;4810:31;4737:4;4864:46;4905:3;4894:9;4890:19;4882:6;4864:46;:::i;:::-;4958:9;4950:6;4946:22;4941:2;4930:9;4926:18;4919:50;4992:33;5018:6;5010;4992:33;:::i;:::-;5056:2;5041:18;;5034:34;;;-1:-1:-1;;;;;5105:32:190;;5099:3;5084:19;;5077:61;5125:3;5154:19;;5147:35;;;5219:22;;;5213:3;5198:19;;5191:51;5291:13;;5313:22;;;5363:2;5389:15;;;;-1:-1:-1;5351:15:190;;;;-1:-1:-1;5432:169:190;5446:6;5443:1;5440:13;5432:169;;;5507:13;;5495:26;;5550:2;5576:15;;;;5541:12;;;;5468:1;5461:9;5432:169;;;-1:-1:-1;5618:3:190;;4389:1238;-1:-1:-1;;;;;;;;;;;4389:1238:190:o;5867:508::-;5944:6;5952;5960;6013:2;6001:9;5992:7;5988:23;5984:32;5981:52;;;6029:1;6026;6019:12;5981:52;6074:23;;;-1:-1:-1;6173:2:190;6158:18;;6145:32;6186:33;6145:32;6186:33;:::i;:::-;6238:7;-1:-1:-1;6297:2:190;6282:18;;6269:32;6310:33;6269:32;6310:33;:::i;:::-;6362:7;6352:17;;;5867:508;;;;;:::o;6380:637::-;6570:2;6582:21;;;6652:13;;6555:18;;;6674:22;;;6522:4;;6753:15;;;6727:2;6712:18;;;6522:4;6796:195;6810:6;6807:1;6804:13;6796:195;;;6875:13;;-1:-1:-1;;;;;6871:39:190;6859:52;;6940:2;6966:15;;;;6931:12;;;;6907:1;6825:9;6796:195;;;-1:-1:-1;7008:3:190;;6380:637;-1:-1:-1;;;;;6380:637:190:o;7022:1037::-;7133:6;7141;7149;7157;7165;7173;7181;7234:3;7222:9;7213:7;7209:23;7205:33;7202:53;;;7251:1;7248;7241:12;7202:53;7290:9;7277:23;7309:31;7334:5;7309:31;:::i;:::-;7359:5;-1:-1:-1;7416:2:190;7401:18;;7388:32;7429:33;7388:32;7429:33;:::i;:::-;7481:7;-1:-1:-1;7561:2:190;7546:18;;7533:32;;-1:-1:-1;7664:2:190;7649:18;;7636:32;;-1:-1:-1;7746:3:190;7731:19;;7718:33;7795:4;7782:18;;7770:31;;7760:59;;7815:1;7812;7805:12;7760:59;7022:1037;;;;-1:-1:-1;7022:1037:190;;;;7838:7;7918:3;7903:19;;7890:33;;-1:-1:-1;8022:3:190;8007:19;;;7994:33;;7022:1037;-1:-1:-1;;7022:1037:190:o;8064:127::-;8125:10;8120:3;8116:20;8113:1;8106:31;8156:4;8153:1;8146:15;8180:4;8177:1;8170:15;8196:192;8275:13;;-1:-1:-1;;;;;8317:46:190;;8307:57;;8297:85;;8378:1;8375;8368:12;8297:85;8196:192;;;:::o;8393:789::-;8486:6;8539:3;8527:9;8518:7;8514:23;8510:33;8507:53;;;8556:1;8553;8546:12;8507:53;8605:7;8598:4;8587:9;8583:20;8579:34;8569:62;;8627:1;8624;8617:12;8569:62;8660:2;8654:9;8702:3;8694:6;8690:16;8772:6;8760:10;8757:22;8736:18;8724:10;8721:34;8718:62;8715:88;;;8783:18;;:::i;:::-;8819:2;8812:22;8854:6;8898:3;8883:19;;8914;;;8911:39;;;8946:1;8943;8936:12;8911:39;8970:9;8988:163;9004:6;8999:3;8996:15;8988:163;;;9072:34;9102:3;9072:34;:::i;:::-;9060:47;;9136:4;9127:14;;;;9021;8988:163;;;-1:-1:-1;9170:6:190;;8393:789;-1:-1:-1;;;;;8393:789:190:o;9187:127::-;9248:10;9243:3;9239:20;9236:1;9229:31;9279:4;9276:1;9269:15;9303:4;9300:1;9293:15;9319:380;9398:1;9394:12;;;;9441;;;9462:61;;9516:4;9508:6;9504:17;9494:27;;9462:61;9569:2;9561:6;9558:14;9538:18;9535:38;9532:161;;9615:10;9610:3;9606:20;9603:1;9596:31;9650:4;9647:1;9640:15;9678:4;9675:1;9668:15;9957:345;-1:-1:-1;;;;;10177:32:190;;;;10159:51;;10241:2;10226:18;;10219:34;;;;10284:2;10269:18;;10262:34;10147:2;10132:18;;9957:345::o;10307:277::-;10374:6;10427:2;10415:9;10406:7;10402:23;10398:32;10395:52;;;10443:1;10440;10433:12;10395:52;10475:9;10469:16;10528:5;10521:13;10514:21;10507:5;10504:32;10494:60;;10550:1;10547;10540:12;11486:127;11547:10;11542:3;11538:20;11535:1;11528:31;11578:4;11575:1;11568:15;11602:4;11599:1;11592:15;11618:272;11709:6;11762:2;11750:9;11741:7;11737:23;11733:32;11730:52;;;11778:1;11775;11768:12;11730:52;11810:9;11804:16;11829:31;11854:5;11829:31;:::i;11895:127::-;11956:10;11951:3;11947:20;11944:1;11937:31;11987:4;11984:1;11977:15;12011:4;12008:1;12001:15;12027:148;12115:4;12094:12;;;12108;;;12090:31;;12133:13;;12130:39;;;12149:18;;:::i;13077:168::-;13150:9;;;13181;;13198:15;;;13192:22;;13178:37;13168:71;;13219:18;;:::i;13250:127::-;13311:10;13306:3;13302:20;13299:1;13292:31;13342:4;13339:1;13332:15;13366:4;13363:1;13356:15;13382:217;13422:1;13448;13438:132;;13492:10;13487:3;13483:20;13480:1;13473:31;13527:4;13524:1;13517:15;13555:4;13552:1;13545:15;13438:132;-1:-1:-1;13584:9:190;;13382:217::o;13990:125::-;14055:9;;;14076:10;;;14073:36;;;14089:18;;:::i", "linkReferences": {}, "immutableReferences": {"16149": [{"start": 1320, "length": 32}, {"start": 1755, "length": 32}, {"start": 4524, "length": 32}, {"start": 4592, "length": 32}, {"start": 5410, "length": 32}, {"start": 5478, "length": 32}, {"start": 7717, "length": 32}], "16152": [{"start": 2597, "length": 32}], "16154": [{"start": 870, "length": 32}, {"start": 1883, "length": 32}, {"start": 7612, "length": 32}], "20383": [{"start": 1030, "length": 32}, {"start": 5097, "length": 32}], "20386": [{"start": 1191, "length": 32}, {"start": 6489, "length": 32}], "24377": [{"start": 950, "length": 32}, {"start": 4490, "length": 32}], "24379": [{"start": 6296, "length": 32}], "28107": [{"start": 4131, "length": 32}], "28109": [{"start": 4089, "length": 32}], "28111": [{"start": 4047, "length": 32}], "28113": [{"start": 4212, "length": 32}], "28115": [{"start": 4252, "length": 32}], "28118": [{"start": 5302, "length": 32}], "28121": [{"start": 5347, "length": 32}]}}, "methodIdentifiers": {"DOMAIN_SEPARATOR()": "3644e515", "HOOK_CALL_GAS_LIMIT()": "82b36169", "MAX_HOOKS_PER_ACCOUNT()": "********", "addHook(address)": "7b4e04e8", "allowance(address,address)": "dd62ed3e", "approve(address,uint256)": "095ea7b3", "asset()": "38d52e0f", "balanceOf(address)": "70a08231", "convertToAssets(uint256)": "07a2d13a", "convertToShares(uint256)": "c6e6f592", "decimals()": "313ce567", "deposit(uint256,address)": "6e553f65", "eip712Domain()": "84b0196e", "hasHook(address,address)": "6ea3d988", "hookAt(address,uint256)": "2d6516b6", "hookBalanceOf(address,address)": "00d137dd", "hooks(address)": "c982681b", "hooksCount(address)": "ac2c3e1d", "maxDeposit(address)": "402d267d", "maxMint(address)": "c63d75b6", "maxRedeem(address)": "d905777e", "maxWithdraw(address)": "ce96cb77", "mint(uint256,address)": "94bf804d", "name()": "06fdde03", "nonces(address)": "7ecebe00", "owner()": "8da5cb5b", "ownerBurn(address,address,uint256,uint256)": "62a0d2cb", "ownerMint(address,address,uint256,uint256)": "4c412cd2", "ownerTransfer(address,address,uint256)": "a1291f7f", "pair()": "a8aa1b31", "permit(address,address,uint256,uint256,uint8,bytes32,bytes32)": "d505accf", "previewDeposit(uint256)": "ef8b30f7", "previewMint(uint256)": "b3d7f6b9", "previewRedeem(uint256)": "4cdad506", "previewWithdraw(uint256)": "0a28a477", "redeem(uint256,address,address)": "ba087652", "removeAllHooks()": "34aa9832", "removeHook(address)": "e5adf3ab", "renounceOwnership()": "715018a6", "symbol()": "95d89b41", "tokenType()": "30fa738c", "totalAssets()": "01e1d114", "totalSupply()": "18160ddd", "transfer(address,uint256)": "a9059cbb", "transferFrom(address,address,uint256)": "23b872dd", "transferOwnership(address)": "f2fde38b", "withdraw(uint256,address,address)": "b460af94"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"pair\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"pluginRegistry\",\"type\":\"address\"},{\"internalType\":\"string\",\"name\":\"name\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"symbol\",\"type\":\"string\"},{\"internalType\":\"uint256\",\"name\":\"tokenType\",\"type\":\"uint256\"}],\"internalType\":\"struct ERC20BaseConfig\",\"name\":\"config\",\"type\":\"tuple\"},{\"internalType\":\"address\",\"name\":\"_asset\",\"type\":\"address\"}],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"inputs\":[],\"name\":\"ECDSAInvalidSignature\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"length\",\"type\":\"uint256\"}],\"name\":\"ECDSAInvalidSignatureLength\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"s\",\"type\":\"bytes32\"}],\"name\":\"ECDSAInvalidSignatureS\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"allowance\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"needed\",\"type\":\"uint256\"}],\"name\":\"ERC20InsufficientAllowance\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"balance\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"needed\",\"type\":\"uint256\"}],\"name\":\"ERC20InsufficientBalance\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"approver\",\"type\":\"address\"}],\"name\":\"ERC20InvalidApprover\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"receiver\",\"type\":\"address\"}],\"name\":\"ERC20InvalidReceiver\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"}],\"name\":\"ERC20InvalidSender\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"}],\"name\":\"ERC20InvalidSpender\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"deadline\",\"type\":\"uint256\"}],\"name\":\"ERC2612ExpiredSignature\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"signer\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"}],\"name\":\"ERC2612InvalidSigner\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"receiver\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"assets\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"max\",\"type\":\"uint256\"}],\"name\":\"ERC4626ExceededMaxDeposit\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"receiver\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"shares\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"max\",\"type\":\"uint256\"}],\"name\":\"ERC4626ExceededMaxMint\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"shares\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"max\",\"type\":\"uint256\"}],\"name\":\"ERC4626ExceededMaxRedeem\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"assets\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"max\",\"type\":\"uint256\"}],\"name\":\"ERC4626ExceededMaxWithdraw\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"HookAlreadyAdded\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"HookNotFound\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"HooksLimitReachedForAccount\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"IndexOutOfBounds\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"currentNonce\",\"type\":\"uint256\"}],\"name\":\"InvalidAccountNonce\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidHookAddress\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidShortString\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidTokenInHook\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"}],\"name\":\"OwnableInvalidOwner\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"OwnableUnauthorizedAccount\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"ReentrantCall\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"}],\"name\":\"SafeERC20FailedOperation\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"str\",\"type\":\"string\"}],\"name\":\"StringTooLong\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"ZeroHooksLimit\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"Approval\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"assets\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"shares\",\"type\":\"uint256\"}],\"name\":\"Borrow\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"assets\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"shares\",\"type\":\"uint256\"}],\"name\":\"BorrowLiquidity\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"assets\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"shares\",\"type\":\"uint256\"}],\"name\":\"Burn\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"assets\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"shares\",\"type\":\"uint256\"}],\"name\":\"Deposit\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[],\"name\":\"EIP712DomainChanged\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"hook\",\"type\":\"address\"}],\"name\":\"HookAdded\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"hook\",\"type\":\"address\"}],\"name\":\"HookRemoved\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"assets\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"shares\",\"type\":\"uint256\"}],\"name\":\"Mint\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"previousOwner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"OwnershipTransferred\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"onBehalfOf\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"assets\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"shares\",\"type\":\"uint256\"}],\"name\":\"Repay\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"onBehalfOf\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"assets\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"shares\",\"type\":\"uint256\"}],\"name\":\"RepayLiquidity\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"Transfer\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"receiver\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"assets\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"shares\",\"type\":\"uint256\"}],\"name\":\"Withdraw\",\"type\":\"event\"},{\"inputs\":[],\"name\":\"DOMAIN_SEPARATOR\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"HOOK_CALL_GAS_LIMIT\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"MAX_HOOKS_PER_ACCOUNT\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"hook\",\"type\":\"address\"}],\"name\":\"addHook\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"}],\"name\":\"allowance\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"approve\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"asset\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"balanceOf\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"shares\",\"type\":\"uint256\"}],\"name\":\"convertToAssets\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"assets\",\"type\":\"uint256\"}],\"name\":\"convertToShares\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"decimals\",\"outputs\":[{\"internalType\":\"uint8\",\"name\":\"\",\"type\":\"uint8\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"assets\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"receiver\",\"type\":\"address\"}],\"name\":\"deposit\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"eip712Domain\",\"outputs\":[{\"internalType\":\"bytes1\",\"name\":\"fields\",\"type\":\"bytes1\"},{\"internalType\":\"string\",\"name\":\"name\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"version\",\"type\":\"string\"},{\"internalType\":\"uint256\",\"name\":\"chainId\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"verifyingContract\",\"type\":\"address\"},{\"internalType\":\"bytes32\",\"name\":\"salt\",\"type\":\"bytes32\"},{\"internalType\":\"uint256[]\",\"name\":\"extensions\",\"type\":\"uint256[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"hook\",\"type\":\"address\"}],\"name\":\"hasHook\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"index\",\"type\":\"uint256\"}],\"name\":\"hookAt\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"hook\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"hookBalanceOf\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"hooks\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"hooksCount\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"name\":\"maxDeposit\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"name\":\"maxMint\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"}],\"name\":\"maxRedeem\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"}],\"name\":\"maxWithdraw\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"shares\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"receiver\",\"type\":\"address\"}],\"name\":\"mint\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"name\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"}],\"name\":\"nonces\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"owner\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"assets\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"shares\",\"type\":\"uint256\"}],\"name\":\"ownerBurn\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"assets\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"shares\",\"type\":\"uint256\"}],\"name\":\"ownerMint\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"ownerTransfer\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"pair\",\"outputs\":[{\"internalType\":\"contract ITransferValidator\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"deadline\",\"type\":\"uint256\"},{\"internalType\":\"uint8\",\"name\":\"v\",\"type\":\"uint8\"},{\"internalType\":\"bytes32\",\"name\":\"r\",\"type\":\"bytes32\"},{\"internalType\":\"bytes32\",\"name\":\"s\",\"type\":\"bytes32\"}],\"name\":\"permit\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"assets\",\"type\":\"uint256\"}],\"name\":\"previewDeposit\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"shares\",\"type\":\"uint256\"}],\"name\":\"previewMint\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"shares\",\"type\":\"uint256\"}],\"name\":\"previewRedeem\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"assets\",\"type\":\"uint256\"}],\"name\":\"previewWithdraw\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"shares\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"receiver\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"}],\"name\":\"redeem\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"removeAllHooks\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"hook\",\"type\":\"address\"}],\"name\":\"removeHook\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"renounceOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"symbol\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"tokenType\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"totalAssets\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"totalSupply\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"transfer\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"transferFrom\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"transferOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"assets\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"receiver\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"}],\"name\":\"withdraw\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"errors\":{\"ECDSAInvalidSignature()\":[{\"details\":\"The signature derives the `address(0)`.\"}],\"ECDSAInvalidSignatureLength(uint256)\":[{\"details\":\"The signature has an invalid length.\"}],\"ECDSAInvalidSignatureS(bytes32)\":[{\"details\":\"The signature has an S value that is in the upper half order.\"}],\"ERC20InsufficientAllowance(address,uint256,uint256)\":[{\"details\":\"Indicates a failure with the `spender`\\u2019s `allowance`. Used in transfers.\",\"params\":{\"allowance\":\"Amount of tokens a `spender` is allowed to operate with.\",\"needed\":\"Minimum amount required to perform a transfer.\",\"spender\":\"Address that may be allowed to operate on tokens without being their owner.\"}}],\"ERC20InsufficientBalance(address,uint256,uint256)\":[{\"details\":\"Indicates an error related to the current `balance` of a `sender`. Used in transfers.\",\"params\":{\"balance\":\"Current balance for the interacting account.\",\"needed\":\"Minimum amount required to perform a transfer.\",\"sender\":\"Address whose tokens are being transferred.\"}}],\"ERC20InvalidApprover(address)\":[{\"details\":\"Indicates a failure with the `approver` of a token to be approved. Used in approvals.\",\"params\":{\"approver\":\"Address initiating an approval operation.\"}}],\"ERC20InvalidReceiver(address)\":[{\"details\":\"Indicates a failure with the token `receiver`. Used in transfers.\",\"params\":{\"receiver\":\"Address to which tokens are being transferred.\"}}],\"ERC20InvalidSender(address)\":[{\"details\":\"Indicates a failure with the token `sender`. Used in transfers.\",\"params\":{\"sender\":\"Address whose tokens are being transferred.\"}}],\"ERC20InvalidSpender(address)\":[{\"details\":\"Indicates a failure with the `spender` to be approved. Used in approvals.\",\"params\":{\"spender\":\"Address that may be allowed to operate on tokens without being their owner.\"}}],\"ERC2612ExpiredSignature(uint256)\":[{\"details\":\"Permit deadline has expired.\"}],\"ERC2612InvalidSigner(address,address)\":[{\"details\":\"Mismatched signature.\"}],\"ERC4626ExceededMaxDeposit(address,uint256,uint256)\":[{\"details\":\"Attempted to deposit more assets than the max amount for `receiver`.\"}],\"ERC4626ExceededMaxMint(address,uint256,uint256)\":[{\"details\":\"Attempted to mint more shares than the max amount for `receiver`.\"}],\"ERC4626ExceededMaxRedeem(address,uint256,uint256)\":[{\"details\":\"Attempted to redeem more shares than the max amount for `receiver`.\"}],\"ERC4626ExceededMaxWithdraw(address,uint256,uint256)\":[{\"details\":\"Attempted to withdraw more assets than the max amount for `receiver`.\"}],\"IndexOutOfBounds()\":[{\"details\":\"Error thrown when attempting to access an index outside the bounds of the array.\"}],\"InvalidAccountNonce(address,uint256)\":[{\"details\":\"The nonce used for an `account` is not the expected current nonce.\"}],\"OwnableInvalidOwner(address)\":[{\"details\":\"The owner is not a valid owner account. (eg. `address(0)`)\"}],\"OwnableUnauthorizedAccount(address)\":[{\"details\":\"The caller account is not authorized to perform an operation.\"}],\"ReentrantCall()\":[{\"details\":\"Emit when reentrancy detected\"}],\"SafeERC20FailedOperation(address)\":[{\"details\":\"An operation with an ERC-20 token failed.\"}]},\"events\":{\"Approval(address,address,uint256)\":{\"details\":\"Emitted when the allowance of a `spender` for an `owner` is set by a call to {approve}. `value` is the new allowance.\"},\"Borrow(address,address,uint256,uint256)\":{\"details\":\"Emitted on a borrow of tokens\",\"params\":{\"assets\":\"The amount of the borrowed token assets\",\"sender\":\"The address initiating the borrowing action\",\"shares\":\"The amount of token shares being borrowed\",\"to\":\"The address receiving the borrowed tokens\"}},\"BorrowLiquidity(address,address,uint256,uint256)\":{\"details\":\"Emitted on a liquidity borrow\",\"params\":{\"assets\":\"The amount of the borrowed liquidity token\",\"sender\":\"The address initiating the borrowing action\",\"shares\":\"The amount of token shares being borrowed\",\"to\":\"Address where the borrowed liquidity is sent\"}},\"Burn(address,address,uint256,uint256)\":{\"details\":\"Emitted when tokens are burned\",\"params\":{\"assets\":\"The amount of token assets being burned\",\"sender\":\"Supplies Ammalgam Liquidity token into the pair contract and receives the minted assets in exchange\",\"shares\":\"The amount of token shares being burned\",\"to\":\"Address where burned tokens are sent\"}},\"EIP712DomainChanged()\":{\"details\":\"MAY be emitted to signal that the domain could have changed.\"},\"Mint(address,address,uint256,uint256)\":{\"details\":\"Emitted when tokens are minted\",\"params\":{\"assets\":\"The amount of token assets being minted\",\"shares\":\"The amount of token shares being minted\",\"to\":\"Address where minted tokens are sent\"}},\"Repay(address,address,uint256,uint256)\":{\"details\":\"Emitted on a repayment of tokens\",\"params\":{\"assets\":\"The address of the repaid token\",\"onBehalfOf\":\"The address of the account on whose behalf tokens are repaid\",\"sender\":\"The address initiating the repayment action\",\"shares\":\"The amount of tokens being repaid\"}},\"RepayLiquidity(address,address,uint256,uint256)\":{\"details\":\"Emitted on a liquidity repayment\",\"params\":{\"assets\":\"The amount of liquidity assets being repaid\",\"onBehalfOf\":\"Address for whom the repayment is made\",\"sender\":\"Supplies borrowed liquidity into the pair contract and the corresponding Ammalgam Debt tokens will be destroyed\",\"shares\":\"The amount of liquidity shares being repaid\"}},\"Transfer(address,address,uint256)\":{\"details\":\"Emitted when `value` tokens are moved from one account (`from`) to another (`to`). Note that `value` may be zero.\"}},\"kind\":\"dev\",\"methods\":{\"DOMAIN_SEPARATOR()\":{\"details\":\"Returns the domain separator used in the encoding of the signature for {permit}, as defined by {EIP712}.\"},\"allowance(address,address)\":{\"details\":\"Returns the remaining number of tokens that `spender` will be allowed to spend on behalf of `owner` through {transferFrom}. This is zero by default. This value changes when {approve} or {transferFrom} are called.\"},\"approve(address,uint256)\":{\"details\":\"See {IERC20-approve}. NOTE: If `value` is the maximum `uint256`, the allowance is not updated on `transferFrom`. This is semantically equivalent to an infinite approval. Requirements: - `spender` cannot be the zero address.\"},\"asset()\":{\"details\":\"Returns the address of the underlying token used for the Vault for accounting, depositing, and withdrawing. - MUST be an ERC-20 token contract. - MUST NOT revert.\"},\"convertToAssets(uint256)\":{\"details\":\"Returns the amount of assets that the Vault would exchange for the amount of shares provided, in an ideal scenario where all the conditions are met. - MUST NOT be inclusive of any fees that are charged against assets in the Vault. - MUST NOT show any variations depending on the caller. - MUST NOT reflect slippage or other on-chain conditions, when performing the actual exchange. - MUST NOT revert. NOTE: This calculation MAY NOT reflect the \\u201cper-user\\u201d price-per-share, and instead should reflect the \\u201caverage-user\\u2019s\\u201d price-per-share, meaning what the average user should expect to see when exchanging to and from.\"},\"convertToShares(uint256)\":{\"details\":\"Returns the amount of shares that the Vault would exchange for the amount of assets provided, in an ideal scenario where all the conditions are met. - MUST NOT be inclusive of any fees that are charged against assets in the Vault. - MUST NOT show any variations depending on the caller. - MUST NOT reflect slippage or other on-chain conditions, when performing the actual exchange. - MUST NOT revert. NOTE: This calculation MAY NOT reflect the \\u201cper-user\\u201d price-per-share, and instead should reflect the \\u201caverage-user\\u2019s\\u201d price-per-share, meaning what the average user should expect to see when exchanging to and from.\"},\"deposit(uint256,address)\":{\"details\":\"Mints shares Vault shares to receiver by depositing exactly amount of underlying tokens. - MUST emit the Deposit event. - MAY support an additional flow in which the underlying tokens are owned by the Vault contract before the   deposit execution, and are accounted for during deposit. - MUST revert if all of assets cannot be deposited (due to deposit limit being reached, slippage, the user not   approving enough underlying tokens to the Vault contract, etc). NOTE: most implementations will require pre-approval of the Vault with the Vault\\u2019s underlying asset token.\"},\"eip712Domain()\":{\"details\":\"returns the fields and values that describe the domain separator used by this contract for EIP-712 signature.\"},\"maxDeposit(address)\":{\"details\":\"Returns the maximum amount of the underlying asset that can be deposited into the Vault for the receiver, through a deposit call. - MUST return a limited value if receiver is subject to some deposit limit. - MUST return 2 ** 256 - 1 if there is no limit on the maximum amount of assets that may be deposited. - MUST NOT revert.\"},\"maxMint(address)\":{\"details\":\"Returns the maximum amount of the Vault shares that can be minted for the receiver, through a mint call. - MUST return a limited value if receiver is subject to some mint limit. - MUST return 2 ** 256 - 1 if there is no limit on the maximum amount of shares that may be minted. - MUST NOT revert.\"},\"maxRedeem(address)\":{\"details\":\"Returns the maximum amount of Vault shares that can be redeemed from the owner balance in the Vault, through a redeem call. - MUST return a limited value if owner is subject to some withdrawal limit or timelock. - MUST return balanceOf(owner) if owner is not subject to any withdrawal limit or timelock. - MUST NOT revert.\"},\"maxWithdraw(address)\":{\"details\":\"Returns the maximum amount of the underlying asset that can be withdrawn from the owner balance in the Vault, through a withdraw call. - MUST return a limited value if owner is subject to some withdrawal limit or timelock. - MUST NOT revert.\"},\"mint(uint256,address)\":{\"details\":\"Mints exactly shares Vault shares to receiver by depositing amount of underlying tokens. - MUST emit the Deposit event. - MAY support an additional flow in which the underlying tokens are owned by the Vault contract before the mint   execution, and are accounted for during mint. - MUST revert if all of shares cannot be minted (due to deposit limit being reached, slippage, the user not   approving enough underlying tokens to the Vault contract, etc). NOTE: most implementations will require pre-approval of the Vault with the Vault\\u2019s underlying asset token.\"},\"name()\":{\"details\":\"Returns the name of the token.\"},\"owner()\":{\"details\":\"Returns the address of the current owner.\"},\"ownerBurn(address,address,uint256,uint256)\":{\"details\":\"override {AmmalgamERC20Base-ownerBurn}.\",\"params\":{\"assets\":\"The amount of underlying assets that will be received.\",\"sender\":\"The owner of the Ammalgam Deposit token.\",\"shares\":\"The amount of shares that will be burned.\",\"to\":\"The address that will receive the underlying assets.\"}},\"ownerMint(address,address,uint256,uint256)\":{\"details\":\"override {AmmalgamERC20Base-ownerMint}.\",\"params\":{\"assets\":\"The amount of underlying assets that were sent to the pair contract.\",\"sender\":\"The address that sent the underlying assets to the pair contract.\",\"shares\":\"The amount of shares that will be minted.\",\"to\":\"The address that will receive the minted shares.\"}},\"ownerTransfer(address,address,uint256)\":{\"params\":{\"amount\":\"The amount of tokens to be transferred.\",\"from\":\"The account to deduct the tokens from.\",\"to\":\"The account to deliver the tokens to.\"}},\"permit(address,address,uint256,uint256,uint8,bytes32,bytes32)\":{\"details\":\"Sets `value` as the allowance of `spender` over ``owner``'s tokens, given ``owner``'s signed approval. IMPORTANT: The same issues {IERC20-approve} has related to transaction ordering also apply here. Emits an {Approval} event. Requirements: - `spender` cannot be the zero address. - `deadline` must be a timestamp in the future. - `v`, `r` and `s` must be a valid `secp256k1` signature from `owner` over the EIP712-formatted function arguments. - the signature must use ``owner``'s current nonce (see {nonces}). For more information on the signature format, see the https://eips.ethereum.org/EIPS/eip-2612#specification[relevant EIP section]. CAUTION: See Security Considerations above.\"},\"previewDeposit(uint256)\":{\"details\":\"Allows an on-chain or off-chain user to simulate the effects of their deposit at the current block, given current on-chain conditions. - MUST return as close to and no more than the exact amount of Vault shares that would be minted in a deposit   call in the same transaction. I.e. deposit should return the same or more shares as previewDeposit if called   in the same transaction. - MUST NOT account for deposit limits like those returned from maxDeposit and should always act as though the   deposit would be accepted, regardless if the user has enough tokens approved, etc. - MUST be inclusive of deposit fees. Integrators should be aware of the existence of deposit fees. - MUST NOT revert. NOTE: any unfavorable discrepancy between convertToShares and previewDeposit SHOULD be considered slippage in share price or some other type of condition, meaning the depositor will lose assets by depositing.\"},\"previewMint(uint256)\":{\"details\":\"Allows an on-chain or off-chain user to simulate the effects of their mint at the current block, given current on-chain conditions. - MUST return as close to and no fewer than the exact amount of assets that would be deposited in a mint call   in the same transaction. I.e. mint should return the same or fewer assets as previewMint if called in the   same transaction. - MUST NOT account for mint limits like those returned from maxMint and should always act as though the mint   would be accepted, regardless if the user has enough tokens approved, etc. - MUST be inclusive of deposit fees. Integrators should be aware of the existence of deposit fees. - MUST NOT revert. NOTE: any unfavorable discrepancy between convertToAssets and previewMint SHOULD be considered slippage in share price or some other type of condition, meaning the depositor will lose assets by minting.\"},\"previewRedeem(uint256)\":{\"details\":\"Allows an on-chain or off-chain user to simulate the effects of their redemption at the current block, given current on-chain conditions. - MUST return as close to and no more than the exact amount of assets that would be withdrawn in a redeem call   in the same transaction. I.e. redeem should return the same or more assets as previewRedeem if called in the   same transaction. - MUST NOT account for redemption limits like those returned from maxRedeem and should always act as though the   redemption would be accepted, regardless if the user has enough shares, etc. - MUST be inclusive of withdrawal fees. Integrators should be aware of the existence of withdrawal fees. - MUST NOT revert. NOTE: any unfavorable discrepancy between convertToAssets and previewRedeem SHOULD be considered slippage in share price or some other type of condition, meaning the depositor will lose assets by redeeming.\"},\"previewWithdraw(uint256)\":{\"details\":\"Allows an on-chain or off-chain user to simulate the effects of their withdrawal at the current block, given current on-chain conditions. - MUST return as close to and no fewer than the exact amount of Vault shares that would be burned in a withdraw   call in the same transaction. I.e. withdraw should return the same or fewer shares as previewWithdraw if   called   in the same transaction. - MUST NOT account for withdrawal limits like those returned from maxWithdraw and should always act as though   the withdrawal would be accepted, regardless if the user has enough shares, etc. - MUST be inclusive of withdrawal fees. Integrators should be aware of the existence of withdrawal fees. - MUST NOT revert. NOTE: any unfavorable discrepancy between convertToShares and previewWithdraw SHOULD be considered slippage in share price or some other type of condition, meaning the depositor will lose assets by depositing.\"},\"redeem(uint256,address,address)\":{\"details\":\"Burns exactly shares from owner and sends assets of underlying tokens to receiver. - MUST emit the Withdraw event. - MAY support an additional flow in which the underlying tokens are owned by the Vault contract before the   redeem execution, and are accounted for during redeem. - MUST revert if all of shares cannot be redeemed (due to withdrawal limit being reached, slippage, the owner   not having enough shares, etc). NOTE: some implementations will require pre-requesting to the Vault before a withdrawal may be performed. Those methods should be performed separately.\"},\"renounceOwnership()\":{\"details\":\"Leaves the contract without owner. It will not be possible to call `onlyOwner` functions. Can only be called by the current owner. NOTE: Renouncing ownership will leave the contract without an owner, thereby disabling any functionality that is only available to the owner.\"},\"symbol()\":{\"details\":\"Returns the symbol of the token, usually a shorter version of the name.\"},\"totalAssets()\":{\"details\":\"Returns the total amount of the underlying asset that is \\u201cmanaged\\u201d by Vault. - SHOULD include any compounding that occurs from yield. - MUST be inclusive of any fees that are charged against assets in the Vault. - MUST NOT revert.\"},\"totalSupply()\":{\"details\":\"Returns the value of tokens in existence.\"},\"transfer(address,uint256)\":{\"details\":\"See {IERC20-transfer}. Requirements: - `to` cannot be the zero address. - the caller must have a balance of at least `value`.\"},\"transferFrom(address,address,uint256)\":{\"details\":\"See {IERC20-transferFrom}. Skips emitting an {Approval} event indicating an allowance update. This is not required by the ERC. See {xref-ERC20-_approve-address-address-uint256-bool-}[_approve]. NOTE: Does not update the allowance if the current allowance is the maximum `uint256`. Requirements: - `from` and `to` cannot be the zero address. - `from` must have a balance of at least `value`. - the caller must have allowance for ``from``'s tokens of at least `value`.\"},\"transferOwnership(address)\":{\"details\":\"Transfers ownership of the contract to a new account (`newOwner`). Can only be called by the current owner.\"},\"withdraw(uint256,address,address)\":{\"details\":\"Burns shares from owner and sends exactly assets of underlying tokens to receiver. - MUST emit the Withdraw event. - MAY support an additional flow in which the underlying tokens are owned by the Vault contract before the   withdraw execution, and are accounted for during withdraw. - MUST revert if all of assets cannot be withdrawn (due to withdrawal limit being reached, slippage, the owner   not having enough shares, etc). Note that some implementations will require pre-requesting to the Vault before a withdrawal may be performed. Those methods should be performed separately.\"}},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{\"addHook(address)\":{\"notice\":\"See {IERC20Hooks-addHook}.\"},\"hasHook(address,address)\":{\"notice\":\"See {IERC20Hooks-hasHook}.\"},\"hookAt(address,uint256)\":{\"notice\":\"See {IERC20Hooks-hookAt}.\"},\"hookBalanceOf(address,address)\":{\"notice\":\"See {IERC20Hooks-hookBalanceOf}.\"},\"hooks(address)\":{\"notice\":\"See {IERC20Hooks-hooks}.\"},\"hooksCount(address)\":{\"notice\":\"See {IERC20Hooks-hooksCount}.\"},\"ownerTransfer(address,address,uint256)\":{\"notice\":\"Transfers `amount` tokens from the `from` address to the `to` address.\"},\"removeAllHooks()\":{\"notice\":\"See {IERC20Hooks-removeAllHooks}.\"},\"removeHook(address)\":{\"notice\":\"See {IERC20Hooks-removeHook}.\"}},\"version\":1}},\"settings\":{\"compilationTarget\":{\"contracts/tokens/ERC4626DepositToken.sol\":\"ERC4626DepositToken\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":1inch/=lib/1inch/\",\":@1inch/=lib/1inch/\",\":@mangrovedao/mangrove-core/=lib/mangrove-core/\",\":@mgv/forge-std/=lib/mangrove-core/lib/forge-std/src/\",\":@mgv/lib/=lib/mangrove-core/lib/\",\":@mgv/script/=lib/mangrove-core/script/\",\":@mgv/src/=lib/mangrove-core/src/\",\":@mgv/test/=lib/mangrove-core/test/\",\":@morpho-org/morpho-blue/=lib/morpho-blue/\",\":@openzeppelin/=lib/openzeppelin-contracts/\",\":ExcessivelySafeCall/=lib/ExcessivelySafeCall/src/\",\":core/=lib/mangrove-core/lib/core/\",\":ds-test/=lib/ExcessivelySafeCall/lib/ds-test/src/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":forge-std/=lib/openzeppelin-contracts/lib/forge-std/src/\",\":halmos-cheatcodes/=lib/morpho-blue/lib/halmos-cheatcodes/src/\",\":mangrove-core/=lib/mangrove-core/\",\":morpho-blue/=lib/morpho-blue/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":preprocessed/=lib/mangrove-core/lib/preprocessed/\"]},\"sources\":{\"contracts/interfaces/IAmmalgamPair.sol\":{\"keccak256\":\"0xa17e45b2348d8920d9970c5d50b300fc0a1e8d03350cdd0d1a624494baa70337\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://8d252e89e5d49d1c15a0c0c0a495a325b9f8d608714b29279a7bacb1e4bf8795\",\"dweb:/ipfs/QmRkZ7a8JJQYEw6HQMJjjkuAK8b5Th1X1ET6BG1R8mx4qw\"]},\"contracts/interfaces/callbacks/ITransferValidator.sol\":{\"keccak256\":\"0x6d9028fc4ad1914e6b2091e6ba46a9f836f9e67ea435c4a8fef41363f2ceaf56\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://7ecaade4884d460168f6978edf35706f7b9e363de6002942b1d171a338dca6a4\",\"dweb:/ipfs/QmS5wgfDt5Pn68rpCytpzhiy57LcmivVFQ5XLGXUUP5Tt8\"]},\"contracts/interfaces/tokens/IAmmalgamERC20.sol\":{\"keccak256\":\"0x44a376269170b4270ec221ce3cb31a609b394e216cc4d2e27b818361b4369829\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://c48bc7586631f27ede73d3d0b4c1d7a29b1653e6c501c8b7fc9877c125f8f57e\",\"dweb:/ipfs/QmTSLtqnsxr7h7ct524rqYssHUo4qursmCZ7g5q3J1qQPK\"]},\"contracts/interfaces/tokens/IPluginRegistry.sol\":{\"keccak256\":\"0x9a677620d88ac7dc42afb21d82a7b7a89bd934c1cada5450cf2b6200bf374ccf\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://304091e5d54e9ad7db24ba1022e84e39dd9305d9cc72fd87423c71b40de4ab3d\",\"dweb:/ipfs/QmZEF5MfmUcxzLF9fcGCLvGMTTLLhcWdCMCDK2WyXj6s7X\"]},\"contracts/interfaces/tokens/ITokenController.sol\":{\"keccak256\":\"0x7778001aaf582fe10005240eb6023b2b6cee3f100b6c2222bf6b9ade93732624\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://91e5c4519207d6a450be1e0a8649157e86d20f8ef6a91ff6512a31cf5561a570\",\"dweb:/ipfs/QmUqZLW27JJZHFPf2fgLDYSWWj5gM158DdaxTTmDVukRAg\"]},\"contracts/libraries/Convert.sol\":{\"keccak256\":\"0x944776d31291de1a9cdc6a52154c23c22b43a01c3edebe7a4140e267edbba975\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://36c03749859077ba47a3acfd574f8c30f34f97def4ce81d7f4feac9a7b62794c\",\"dweb:/ipfs/QmdycZay5X2WrbS8qS7RycLpZbMQx7yKszWQzGU3rqidpH\"]},\"contracts/libraries/constants.sol\":{\"keccak256\":\"0x0dfb294985a8f48287ff13e8476718ddb5334b1d8bf6bfa59a5db1dbcf6ca7c4\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://4bedcfdb2850cfb22b5daa768ab8125b4ccab97c90068d1d0ad4495bf942b362\",\"dweb:/ipfs/Qmf9p88yQN2JYRBR5D7q9BLmwhDJWpFk47ZuayrKqCyHat\"]},\"contracts/tokens/ERC20Base.sol\":{\"keccak256\":\"0xdd3db9eaa855b6ff747ffaa0e74dd2a64dd5b0d704356acb75c2690c3fc6bc2b\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://8f9e05ae3078bc3a29ef43861b006fb290606c1c9b2676baaed3ab98ecdb2d59\",\"dweb:/ipfs/QmZrvgxYtUD6jQVBvM9rT7jj5Vzb5csiThGj3ZwHSPryAL\"]},\"contracts/tokens/ERC4626DepositToken.sol\":{\"keccak256\":\"0xd914aa43dc5e9f2f02f98b05561faf6f00853b701f51dfcd7a08a31feaf220be\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://8c2282d40855587b2ac70e89d4e0e147b9afe32a41245fffc96b923a9e5ce7ac\",\"dweb:/ipfs/QmVn3tBkZcXKnqjfnLTXFkKtu1EetDL1UF7rRjNrHdRCSM\"]},\"lib/1inch/solidity-utils/contracts/libraries/AddressArray.sol\":{\"keccak256\":\"0x7895eaf7b55d9612b22ec586970488de51436c021b9f9414b3c27c3583c8856e\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://e43897dfeff84e2983b017ab100d74061a6b9bed4618ec37a7cbb68bf064ac22\",\"dweb:/ipfs/Qmejoj4CuNr1giwGBDG7SgRtfYmi64fgy2DsGP2AAW9gH9\"]},\"lib/1inch/solidity-utils/contracts/libraries/AddressSet.sol\":{\"keccak256\":\"0xbb8e2a541ac268f00f382c9fba9403b3ec5b58a48dc7236920d7c87817f93318\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://614b60ff60f75c46aa41d00ec5dd78136d42e0d6035aa2331d16f26d2f5f5368\",\"dweb:/ipfs/QmUJWqcx2heKcmBhxpHfAnpKrwnevqtAwaQKT7Bmpke5NB\"]},\"lib/1inch/token-plugins/contracts/ERC20Hooks.sol\":{\"keccak256\":\"0xd2657f278b2ed4667663344aa06c02b52b15862c69c215570de329aa1a4683a2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://4ffb90146369b7fa890489a0d3ecab5aa81c9f47b3ec7a1776fd8bc14eaa33d5\",\"dweb:/ipfs/QmWo24VHySo9jQBeXfE87Z3Hh576cNKcwccLrBAMsfax1c\"]},\"lib/1inch/token-plugins/contracts/interfaces/IERC20Hooks.sol\":{\"keccak256\":\"0x2fb4fcbf91a7edf36e7ada3f578a8de1aee7ebdd12460648d3e09d4448351875\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://d09a05543afcad3c0c2d2b9013647408cc44e1b17f362665ee20f9abed5964e8\",\"dweb:/ipfs/QmX3MMGsVJSfsrVoYY2jWK2BXd3i6scBhQQxytk461mizh\"]},\"lib/1inch/token-plugins/contracts/interfaces/IHook.sol\":{\"keccak256\":\"0x474893cc48ee17530ad0e978ecbccaa726943615a5736260187a37b8e133ee80\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://511ed52ec427cced459878f28453790817d4cbacc8601d77eb8dfa28e2e0b30d\",\"dweb:/ipfs/QmXvbB1bAZZanGepiUxWh3zZQUaHQZeYpR1UdaW4w5yKVS\"]},\"lib/1inch/token-plugins/contracts/libs/ReentrancyGuard.sol\":{\"keccak256\":\"0xa88ccab1ee6b34a9007986ca15ea5fd5580864029130eb38333183af1bc4f66c\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b5ad18c862e59b0f24edc900519a55c7aee5537a42d039d6ea5e34df40267bb0\",\"dweb:/ipfs/QmVMRSjUEPxCno1sFdhWvpRqAqoqns8zwVyd7yCHYC6P8Z\"]},\"lib/openzeppelin-contracts/contracts/access/Ownable.sol\":{\"keccak256\":\"0xff6d0bb2e285473e5311d9d3caacb525ae3538a80758c10649a4d61029b017bb\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://8ed324d3920bb545059d66ab97d43e43ee85fd3bd52e03e401f020afb0b120f6\",\"dweb:/ipfs/QmfEckWLmZkDDcoWrkEvMWhms66xwTLff9DDhegYpvHo1a\"]},\"lib/openzeppelin-contracts/contracts/interfaces/IERC1363.sol\":{\"keccak256\":\"0xc15298eb2b9ba5e18a8c9d12f93ad17a3e162a5c1d9b85f54c8adb5827b0d4da\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://1f3c3d8f81d2daf1231890a6a2f897be365d6a479b53dcd52ec2527b5d3faf41\",\"dweb:/ipfs/QmeNdkd6u4at9pd2GAyyqxzrVGGvxfLpGmAKnFoYM5ya2e\"]},\"lib/openzeppelin-contracts/contracts/interfaces/IERC165.sol\":{\"keccak256\":\"0x81b022028c39007cce9920c394b9cddd1cb9f3a1c0398f254b4a6492df92ad2b\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://e0b61b8a5c69b4df993c3d6f94c174ab293aa8698d149bce7be2d88f82929beb\",\"dweb:/ipfs/QmbtacmB1k8ginfrHvAJpjVeqnjYGfXYrkXmMPYEb83z4t\"]},\"lib/openzeppelin-contracts/contracts/interfaces/IERC20.sol\":{\"keccak256\":\"0xb5d81383d40f4006d1ce4bbad0064e7a930e17302cbe2a745e09cb403f042733\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3fc4a5681c2f00f41f49260a36ae6bbe1121dd93d470ea24d51d556eff2980be\",\"dweb:/ipfs/QmUBW6TwVWtGP96ka9TfuGivd27kH8CtkXD8RQAAecSFiR\"]},\"lib/openzeppelin-contracts/contracts/interfaces/IERC4626.sol\":{\"keccak256\":\"0x932fe72aeda629f70ee2ca902cfd8cce9faa0a13b39222c240979f42984c4541\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://4e9e0a320185800eb9a6d57ef0e4ccf3bb32a6b5dd44882f7552617c30d4a05e\",\"dweb:/ipfs/QmYJVpT7DDPWx3DWro8vtM6Gqre2AyufsyCYoHm9cfQ1vr\"]},\"lib/openzeppelin-contracts/contracts/interfaces/IERC5267.sol\":{\"keccak256\":\"0xf29e9088951d8a2074d872a733674618fe5c164df21b8b5cf4a6295f523ba7ad\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://562a1abc7ea505582827ce0c9a2f778360a1a8242742683af179930640020215\",\"dweb:/ipfs/QmPjx5f6KKaPfsDi1uV3ovQN9gHTAcNkMAFJZxE1Adw6VT\"]},\"lib/openzeppelin-contracts/contracts/interfaces/draft-IERC6093.sol\":{\"keccak256\":\"0x85cf779582f7860b00bba1d259be00e754bfbac3c2339e324d0113d682d9e9f9\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://2ddf369affd106e2a9e2b8a22a6ce3da8a6ccda14c6ba5b8c87f6b08169e6318\",\"dweb:/ipfs/QmNadAttd47ycHShxhk33JUJhrbzmyZQ7mHs7WEyG4Qkmp\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/ERC20.sol\":{\"keccak256\":\"0x5aad1745bddba11752c3962464a3b12e0af079310cc22d1f43f0388ae1aaf8db\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://577fad916bfdfe89aadf2685322fec7562cb0ed87722923085213cd9f85d7b79\",\"dweb:/ipfs/QmSM3J6PjrAUyEoNbdhq1ECZLXczKdCTzZTBUieKHsBYEL\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol\":{\"keccak256\":\"0xfe37358f223eddd65d61bb62b0b7bdb69d7101b5ec8d484292b8c1583a153b8a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://28dd43f30af3c12ae0fc08dd031b1250e906ef3c95f63f30fac6fd15aee2a662\",\"dweb:/ipfs/QmUkSyWsSRx36w1ti7U6qnGnQgJq16wpMhjeJrnyn9AXwG\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20Permit.sol\":{\"keccak256\":\"0x6485b101d7335f0fd25abc996c5e2fc965e72e5fbd0a7ad1a465bd3f012b5fd8\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://1f8d296121044e697bcd34c4cd394cebd46fc30b04ce94fccff37200872e6834\",\"dweb:/ipfs/QmTNdmLdoHgMzoCDZ8Txk9aYvwtuyeJYHf5mjLWgzGTZAu\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC4626.sol\":{\"keccak256\":\"0x0d99c706d010fa15de36e7e7b7a03dd0fdc9bcec52f9f812ef80ec7f3fc6fa63\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://bee73c81a2964e8da0537de14082e60d64cd7b1cd9162adc04b58317e334c896\",\"dweb:/ipfs/QmbQ75T9PEJuiLk1kypX68rEBFtTaEzPWsy8Dv99buqVPH\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol\":{\"keccak256\":\"0xbaffa0bcc92adf28a53cc3b68551fc3632cb8f849a0028cb8d5c06e4677715e9\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://32e6f8f6b2e883c85e6a602c0882d9962ce2f92406961244e86cd974df815912\",\"dweb:/ipfs/Qmahvx6fPpecicq1aUE1JihCxV5ep1bfuPukzrxa8Ub5PS\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Permit.sol\":{\"keccak256\":\"0x093f32ab700c2b05373387263915a75f5455cdb0f09a7630cc621e27b7b50d04\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://d163e6ef21df143969df5557305e8c643a135c7660a678d0c65dca91772114a0\",\"dweb:/ipfs/QmTZUgiwEro5oLRhbJ2iSWyCqu1JTDekoFHALVUn4eHqYK\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol\":{\"keccak256\":\"0x1183b415155c1a7bf56d45edad5b17caf0da70935ac420698cbe8afb6750cbb2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://21d9edaeb3e5e8f93eb0fdab41530654e8169b1990b3bbfcf5e4527c52aa03f5\",\"dweb:/ipfs/QmWrqpNW3x5k3pTjvrT8XU1hauHnXTjqaPL2tfzMuWYosj\"]},\"lib/openzeppelin-contracts/contracts/utils/Context.sol\":{\"keccak256\":\"0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12\",\"dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF\"]},\"lib/openzeppelin-contracts/contracts/utils/Nonces.sol\":{\"keccak256\":\"0x0082767004fca261c332e9ad100868327a863a88ef724e844857128845ab350f\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://132dce9686a54e025eb5ba5d2e48208f847a1ec3e60a3e527766d7bf53fb7f9e\",\"dweb:/ipfs/QmXn1a2nUZMpu2z6S88UoTfMVtY2YNh86iGrzJDYmMkKeZ\"]},\"lib/openzeppelin-contracts/contracts/utils/Panic.sol\":{\"keccak256\":\"0xf7fe324703a64fc51702311dc51562d5cb1497734f074e4f483bfb6717572d7a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://c6a5ff4f9fd8649b7ee20800b7fa387d3465bd77cf20c2d1068cd5c98e1ed57a\",\"dweb:/ipfs/QmVSaVJf9FXFhdYEYeCEfjMVHrxDh5qL4CGkxdMWpQCrqG\"]},\"lib/openzeppelin-contracts/contracts/utils/ShortStrings.sol\":{\"keccak256\":\"0x1fcf8cceb1a67e6c8512267e780933c4a3f63ef44756e6c818fda79be51c8402\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://617d7d57f6f9cd449068b4d23daf485676d083aae648e038d05eb3a13291de35\",\"dweb:/ipfs/QmPADWPiGaSzZDFNpFEUx4ZPqhzPkYncBpHyTfAGcfsqzy\"]},\"lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol\":{\"keccak256\":\"0xcf74f855663ce2ae00ed8352666b7935f6cddea2932fdf2c3ecd30a9b1cd0e97\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://9f660b1f351b757dfe01438e59888f31f33ded3afcf5cb5b0d9bf9aa6f320a8b\",\"dweb:/ipfs/QmarDJ5hZEgBtCmmrVzEZWjub9769eD686jmzb2XpSU1cM\"]},\"lib/openzeppelin-contracts/contracts/utils/Strings.sol\":{\"keccak256\":\"0x1402d9ac66fbca0a2b282cd938f01f3cd5fb1e4c696ed28b37839401674aef52\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://d3e6c46b6d1ea36bd73e0ac443a53504089167b98baa24923d702a865a38d211\",\"dweb:/ipfs/QmdutUpr5KktmvgtqG2v96Bo8nVKLJ3PgPedxbsRD42CuQ\"]},\"lib/openzeppelin-contracts/contracts/utils/cryptography/ECDSA.sol\":{\"keccak256\":\"0x6c29257484c0595ca5af8844fafe99cc5eace7447c9f5bced71d6b3a19a6a2a5\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://cce7ac0bdb05f73c0918e362dea2e52426e00ddf0a1018f14accdcf78c6eb6e4\",\"dweb:/ipfs/QmbkNq5dDxww27FzFFiKgW3S7C5VoZpjdZGpSCtsb9hP32\"]},\"lib/openzeppelin-contracts/contracts/utils/cryptography/EIP712.sol\":{\"keccak256\":\"0xda8013da608bda3c9eaa9e59053d38d7888e64bb40aa557e5929cd702f8de87e\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3ea13234c6b00ae79dc1a98e7e7f2faf38d37e76a687ccd0c95ad84b03ea570f\",\"dweb:/ipfs/QmWtdefDm5jiEzAjmfPMZ5B1NKVxFoMiD5ZoD68hcNTHun\"]},\"lib/openzeppelin-contracts/contracts/utils/cryptography/MessageHashUtils.sol\":{\"keccak256\":\"0x26670fef37d4adf55570ba78815eec5f31cb017e708f61886add4fc4da665631\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b16d45febff462bafd8a5669f904796a835baf607df58a8461916d3bf4f08c59\",\"dweb:/ipfs/QmU2eJFpjmT4vxeJWJyLeQb8Xht1kdB8Y6MKLDPFA9WPux\"]},\"lib/openzeppelin-contracts/contracts/utils/introspection/IERC165.sol\":{\"keccak256\":\"0x4ee0e04cc52827588793a141d5efb9830f179a17e80867cc332b3a30ceb30fd9\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://17d8f47fce493b34099ed9005c5aee3012488f063cfe1c34ed8f9e6fc3d576e5\",\"dweb:/ipfs/QmZco2GbZZhEMvG3BovyoGMAFKvfi2LhfNGQLn283LPrXf\"]},\"lib/openzeppelin-contracts/contracts/utils/math/Math.sol\":{\"keccak256\":\"0x1225214420c83ebcca88f2ae2b50f053aaa7df7bd684c3e878d334627f2edfc6\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6c5fab4970634f9ab9a620983dc1c8a30153981a0b1a521666e269d0a11399d3\",\"dweb:/ipfs/QmVRnBC575MESGkEHndjujtR7qub2FzU9RWy9eKLp4hPZB\"]},\"lib/openzeppelin-contracts/contracts/utils/math/SafeCast.sol\":{\"keccak256\":\"0x195533c86d0ef72bcc06456a4f66a9b941f38eb403739b00f21fd7c1abd1ae54\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b1d578337048cad08c1c03041cca5978eff5428aa130c781b271ad9e5566e1f8\",\"dweb:/ipfs/QmPFKL2r9CBsMwmUqqdcFPfHZB2qcs9g1HDrPxzWSxomvy\"]},\"lib/openzeppelin-contracts/contracts/utils/math/SignedMath.sol\":{\"keccak256\":\"0xb1970fac7b64e6c09611e6691791e848d5e3fe410fa5899e7df2e0afd77a99e3\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://db5fbb3dddd8b7047465b62575d96231ba8a2774d37fb4737fbf23340fabbb03\",\"dweb:/ipfs/QmVUSvooZKEdEdap619tcJjTLcAuH6QBdZqAzWwnAXZAWJ\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "struct ERC20BaseConfig", "name": "config", "type": "tuple", "components": [{"internalType": "address", "name": "pair", "type": "address"}, {"internalType": "address", "name": "pluginRegistry", "type": "address"}, {"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "symbol", "type": "string"}, {"internalType": "uint256", "name": "tokenType", "type": "uint256"}]}, {"internalType": "address", "name": "_asset", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "type": "error", "name": "ECDSAInvalidSignature"}, {"inputs": [{"internalType": "uint256", "name": "length", "type": "uint256"}], "type": "error", "name": "ECDSAInvalidSignatureLength"}, {"inputs": [{"internalType": "bytes32", "name": "s", "type": "bytes32"}], "type": "error", "name": "ECDSAInvalidSignatureS"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "allowance", "type": "uint256"}, {"internalType": "uint256", "name": "needed", "type": "uint256"}], "type": "error", "name": "ERC20InsufficientAllowance"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}, {"internalType": "uint256", "name": "balance", "type": "uint256"}, {"internalType": "uint256", "name": "needed", "type": "uint256"}], "type": "error", "name": "ERC20InsufficientBalance"}, {"inputs": [{"internalType": "address", "name": "approver", "type": "address"}], "type": "error", "name": "ERC20InvalidApprover"}, {"inputs": [{"internalType": "address", "name": "receiver", "type": "address"}], "type": "error", "name": "ERC20InvalidReceiver"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}], "type": "error", "name": "ERC20InvalidSender"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}], "type": "error", "name": "ERC20InvalidSpender"}, {"inputs": [{"internalType": "uint256", "name": "deadline", "type": "uint256"}], "type": "error", "name": "ERC2612ExpiredSignature"}, {"inputs": [{"internalType": "address", "name": "signer", "type": "address"}, {"internalType": "address", "name": "owner", "type": "address"}], "type": "error", "name": "ERC2612InvalidSigner"}, {"inputs": [{"internalType": "address", "name": "receiver", "type": "address"}, {"internalType": "uint256", "name": "assets", "type": "uint256"}, {"internalType": "uint256", "name": "max", "type": "uint256"}], "type": "error", "name": "ERC4626ExceededMaxDeposit"}, {"inputs": [{"internalType": "address", "name": "receiver", "type": "address"}, {"internalType": "uint256", "name": "shares", "type": "uint256"}, {"internalType": "uint256", "name": "max", "type": "uint256"}], "type": "error", "name": "ERC4626ExceededMaxMint"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "uint256", "name": "shares", "type": "uint256"}, {"internalType": "uint256", "name": "max", "type": "uint256"}], "type": "error", "name": "ERC4626ExceededMaxRedeem"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "uint256", "name": "assets", "type": "uint256"}, {"internalType": "uint256", "name": "max", "type": "uint256"}], "type": "error", "name": "ERC4626ExceededMaxWithdraw"}, {"inputs": [], "type": "error", "name": "HookAlreadyAdded"}, {"inputs": [], "type": "error", "name": "HookNotFound"}, {"inputs": [], "type": "error", "name": "HooksLimitReachedForAccount"}, {"inputs": [], "type": "error", "name": "IndexOutOfBounds"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "uint256", "name": "currentNonce", "type": "uint256"}], "type": "error", "name": "InvalidAccountNonce"}, {"inputs": [], "type": "error", "name": "InvalidHookAddress"}, {"inputs": [], "type": "error", "name": "InvalidShortString"}, {"inputs": [], "type": "error", "name": "InvalidTokenInHook"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "type": "error", "name": "OwnableInvalidOwner"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "type": "error", "name": "OwnableUnauthorizedAccount"}, {"inputs": [], "type": "error", "name": "ReentrantCall"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}], "type": "error", "name": "SafeERC20FailedOperation"}, {"inputs": [{"internalType": "string", "name": "str", "type": "string"}], "type": "error", "name": "StringTooLong"}, {"inputs": [], "type": "error", "name": "ZeroHooksLimit"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address", "indexed": true}, {"internalType": "address", "name": "spender", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "value", "type": "uint256", "indexed": false}], "type": "event", "name": "Approval", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address", "indexed": true}, {"internalType": "address", "name": "to", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "assets", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "shares", "type": "uint256", "indexed": false}], "type": "event", "name": "Borrow", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address", "indexed": true}, {"internalType": "address", "name": "to", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "assets", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "shares", "type": "uint256", "indexed": false}], "type": "event", "name": "BorrowLiquidity", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address", "indexed": true}, {"internalType": "address", "name": "to", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "assets", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "shares", "type": "uint256", "indexed": false}], "type": "event", "name": "Burn", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address", "indexed": true}, {"internalType": "address", "name": "owner", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "assets", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "shares", "type": "uint256", "indexed": false}], "type": "event", "name": "<PERSON><PERSON><PERSON><PERSON>", "anonymous": false}, {"inputs": [], "type": "event", "name": "EIP712DomainChanged", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "account", "type": "address", "indexed": false}, {"internalType": "address", "name": "hook", "type": "address", "indexed": false}], "type": "event", "name": "HookAdded", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "account", "type": "address", "indexed": false}, {"internalType": "address", "name": "hook", "type": "address", "indexed": false}], "type": "event", "name": "HookRemoved", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address", "indexed": true}, {"internalType": "address", "name": "to", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "assets", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "shares", "type": "uint256", "indexed": false}], "type": "event", "name": "Mint", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "previousOwner", "type": "address", "indexed": true}, {"internalType": "address", "name": "new<PERSON>wner", "type": "address", "indexed": true}], "type": "event", "name": "OwnershipTransferred", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address", "indexed": true}, {"internalType": "address", "name": "onBehalfOf", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "assets", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "shares", "type": "uint256", "indexed": false}], "type": "event", "name": "<PERSON>ay", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address", "indexed": true}, {"internalType": "address", "name": "onBehalfOf", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "assets", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "shares", "type": "uint256", "indexed": false}], "type": "event", "name": "RepayLiquidity", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "from", "type": "address", "indexed": true}, {"internalType": "address", "name": "to", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "value", "type": "uint256", "indexed": false}], "type": "event", "name": "Transfer", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address", "indexed": true}, {"internalType": "address", "name": "receiver", "type": "address", "indexed": true}, {"internalType": "address", "name": "owner", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "assets", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "shares", "type": "uint256", "indexed": false}], "type": "event", "name": "Withdraw", "anonymous": false}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "DOMAIN_SEPARATOR", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "HOOK_CALL_GAS_LIMIT", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "MAX_HOOKS_PER_ACCOUNT", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "hook", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "addHook"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "spender", "type": "address"}], "stateMutability": "view", "type": "function", "name": "allowance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "approve", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "asset", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "stateMutability": "view", "type": "function", "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "uint256", "name": "shares", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "convertToAssets", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "uint256", "name": "assets", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "convertToShares", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "decimals", "outputs": [{"internalType": "uint8", "name": "", "type": "uint8"}]}, {"inputs": [{"internalType": "uint256", "name": "assets", "type": "uint256"}, {"internalType": "address", "name": "receiver", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "deposit", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "eip712Domain", "outputs": [{"internalType": "bytes1", "name": "fields", "type": "bytes1"}, {"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "version", "type": "string"}, {"internalType": "uint256", "name": "chainId", "type": "uint256"}, {"internalType": "address", "name": "verifyingContract", "type": "address"}, {"internalType": "bytes32", "name": "salt", "type": "bytes32"}, {"internalType": "uint256[]", "name": "extensions", "type": "uint256[]"}]}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "address", "name": "hook", "type": "address"}], "stateMutability": "view", "type": "function", "name": "hasH<PERSON>", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "uint256", "name": "index", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "hookAt", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "address", "name": "hook", "type": "address"}, {"internalType": "address", "name": "account", "type": "address"}], "stateMutability": "view", "type": "function", "name": "hookBalanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "stateMutability": "view", "type": "function", "name": "hooks", "outputs": [{"internalType": "address[]", "name": "", "type": "address[]"}]}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "stateMutability": "view", "type": "function", "name": "hooksCount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function", "name": "maxDeposit", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function", "name": "maxMint", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "stateMutability": "view", "type": "function", "name": "max<PERSON><PERSON><PERSON>", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "stateMutability": "view", "type": "function", "name": "max<PERSON><PERSON><PERSON><PERSON>", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "uint256", "name": "shares", "type": "uint256"}, {"internalType": "address", "name": "receiver", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "mint", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "name", "outputs": [{"internalType": "string", "name": "", "type": "string"}]}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "stateMutability": "view", "type": "function", "name": "nonces", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "assets", "type": "uint256"}, {"internalType": "uint256", "name": "shares", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "ownerBurn"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "assets", "type": "uint256"}, {"internalType": "uint256", "name": "shares", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "ownerMint"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "ownerTransfer"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "pair", "outputs": [{"internalType": "contract ITransferValidator", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}, {"internalType": "uint256", "name": "deadline", "type": "uint256"}, {"internalType": "uint8", "name": "v", "type": "uint8"}, {"internalType": "bytes32", "name": "r", "type": "bytes32"}, {"internalType": "bytes32", "name": "s", "type": "bytes32"}], "stateMutability": "nonpayable", "type": "function", "name": "permit"}, {"inputs": [{"internalType": "uint256", "name": "assets", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "previewDeposit", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "uint256", "name": "shares", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "previewMint", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "uint256", "name": "shares", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "previewRedeem", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "uint256", "name": "assets", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "previewWithdraw", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "uint256", "name": "shares", "type": "uint256"}, {"internalType": "address", "name": "receiver", "type": "address"}, {"internalType": "address", "name": "owner", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "redeem", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "removeAllHooks"}, {"inputs": [{"internalType": "address", "name": "hook", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "removeH<PERSON>"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "renounceOwnership"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "symbol", "outputs": [{"internalType": "string", "name": "", "type": "string"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "tokenType", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "totalAssets", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "totalSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "transfer", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "transferFrom", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "transferOwnership"}, {"inputs": [{"internalType": "uint256", "name": "assets", "type": "uint256"}, {"internalType": "address", "name": "receiver", "type": "address"}, {"internalType": "address", "name": "owner", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "withdraw", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}], "devdoc": {"kind": "dev", "methods": {"DOMAIN_SEPARATOR()": {"details": "Returns the domain separator used in the encoding of the signature for {permit}, as defined by {EIP712}."}, "allowance(address,address)": {"details": "Returns the remaining number of tokens that `spender` will be allowed to spend on behalf of `owner` through {transferFrom}. This is zero by default. This value changes when {approve} or {transferFrom} are called."}, "approve(address,uint256)": {"details": "See {IERC20-approve}. NOTE: If `value` is the maximum `uint256`, the allowance is not updated on `transferFrom`. This is semantically equivalent to an infinite approval. Requirements: - `spender` cannot be the zero address."}, "asset()": {"details": "Returns the address of the underlying token used for the Vault for accounting, depositing, and withdrawing. - MUST be an ERC-20 token contract. - MUST NOT revert."}, "convertToAssets(uint256)": {"details": "Returns the amount of assets that the Vault would exchange for the amount of shares provided, in an ideal scenario where all the conditions are met. - MUST NOT be inclusive of any fees that are charged against assets in the Vault. - MUST NOT show any variations depending on the caller. - MUST NOT reflect slippage or other on-chain conditions, when performing the actual exchange. - MUST NOT revert. NOTE: This calculation MAY NOT reflect the “per-user” price-per-share, and instead should reflect the “average-user’s” price-per-share, meaning what the average user should expect to see when exchanging to and from."}, "convertToShares(uint256)": {"details": "Returns the amount of shares that the Vault would exchange for the amount of assets provided, in an ideal scenario where all the conditions are met. - MUST NOT be inclusive of any fees that are charged against assets in the Vault. - MUST NOT show any variations depending on the caller. - MUST NOT reflect slippage or other on-chain conditions, when performing the actual exchange. - MUST NOT revert. NOTE: This calculation MAY NOT reflect the “per-user” price-per-share, and instead should reflect the “average-user’s” price-per-share, meaning what the average user should expect to see when exchanging to and from."}, "deposit(uint256,address)": {"details": "Mints shares Vault shares to receiver by depositing exactly amount of underlying tokens. - MUST emit the Deposit event. - MAY support an additional flow in which the underlying tokens are owned by the Vault contract before the   deposit execution, and are accounted for during deposit. - MUST revert if all of assets cannot be deposited (due to deposit limit being reached, slippage, the user not   approving enough underlying tokens to the Vault contract, etc). NOTE: most implementations will require pre-approval of the Vault with the Vault’s underlying asset token."}, "eip712Domain()": {"details": "returns the fields and values that describe the domain separator used by this contract for EIP-712 signature."}, "maxDeposit(address)": {"details": "Returns the maximum amount of the underlying asset that can be deposited into the Vault for the receiver, through a deposit call. - MUST return a limited value if receiver is subject to some deposit limit. - MUST return 2 ** 256 - 1 if there is no limit on the maximum amount of assets that may be deposited. - MUST NOT revert."}, "maxMint(address)": {"details": "Returns the maximum amount of the Vault shares that can be minted for the receiver, through a mint call. - MUST return a limited value if receiver is subject to some mint limit. - MUST return 2 ** 256 - 1 if there is no limit on the maximum amount of shares that may be minted. - MUST NOT revert."}, "maxRedeem(address)": {"details": "Returns the maximum amount of Vault shares that can be redeemed from the owner balance in the Vault, through a redeem call. - MUST return a limited value if owner is subject to some withdrawal limit or timelock. - MUST return balanceOf(owner) if owner is not subject to any withdrawal limit or timelock. - MUST NOT revert."}, "maxWithdraw(address)": {"details": "Returns the maximum amount of the underlying asset that can be withdrawn from the owner balance in the Vault, through a withdraw call. - MUST return a limited value if owner is subject to some withdrawal limit or timelock. - MUST NOT revert."}, "mint(uint256,address)": {"details": "Mints exactly shares Vault shares to receiver by depositing amount of underlying tokens. - MUST emit the Deposit event. - MAY support an additional flow in which the underlying tokens are owned by the Vault contract before the mint   execution, and are accounted for during mint. - MUST revert if all of shares cannot be minted (due to deposit limit being reached, slippage, the user not   approving enough underlying tokens to the Vault contract, etc). NOTE: most implementations will require pre-approval of the Vault with the Vault’s underlying asset token."}, "name()": {"details": "Returns the name of the token."}, "owner()": {"details": "Returns the address of the current owner."}, "ownerBurn(address,address,uint256,uint256)": {"details": "override {AmmalgamERC20Base-ownerBurn}.", "params": {"assets": "The amount of underlying assets that will be received.", "sender": "The owner of the Ammalgam Deposit token.", "shares": "The amount of shares that will be burned.", "to": "The address that will receive the underlying assets."}}, "ownerMint(address,address,uint256,uint256)": {"details": "override {AmmalgamERC20Base-ownerMint}.", "params": {"assets": "The amount of underlying assets that were sent to the pair contract.", "sender": "The address that sent the underlying assets to the pair contract.", "shares": "The amount of shares that will be minted.", "to": "The address that will receive the minted shares."}}, "ownerTransfer(address,address,uint256)": {"params": {"amount": "The amount of tokens to be transferred.", "from": "The account to deduct the tokens from.", "to": "The account to deliver the tokens to."}}, "permit(address,address,uint256,uint256,uint8,bytes32,bytes32)": {"details": "Sets `value` as the allowance of `spender` over ``owner``'s tokens, given ``owner``'s signed approval. IMPORTANT: The same issues {IERC20-approve} has related to transaction ordering also apply here. Emits an {Approval} event. Requirements: - `spender` cannot be the zero address. - `deadline` must be a timestamp in the future. - `v`, `r` and `s` must be a valid `secp256k1` signature from `owner` over the EIP712-formatted function arguments. - the signature must use ``owner``'s current nonce (see {nonces}). For more information on the signature format, see the https://eips.ethereum.org/EIPS/eip-2612#specification[relevant EIP section]. CAUTION: See Security Considerations above."}, "previewDeposit(uint256)": {"details": "Allows an on-chain or off-chain user to simulate the effects of their deposit at the current block, given current on-chain conditions. - MUST return as close to and no more than the exact amount of Vault shares that would be minted in a deposit   call in the same transaction. I.e. deposit should return the same or more shares as previewDeposit if called   in the same transaction. - MUST NOT account for deposit limits like those returned from maxDeposit and should always act as though the   deposit would be accepted, regardless if the user has enough tokens approved, etc. - MUST be inclusive of deposit fees. Integrators should be aware of the existence of deposit fees. - MUST NOT revert. NOTE: any unfavorable discrepancy between convertToShares and previewDeposit SHOULD be considered slippage in share price or some other type of condition, meaning the depositor will lose assets by depositing."}, "previewMint(uint256)": {"details": "Allows an on-chain or off-chain user to simulate the effects of their mint at the current block, given current on-chain conditions. - MUST return as close to and no fewer than the exact amount of assets that would be deposited in a mint call   in the same transaction. I.e. mint should return the same or fewer assets as previewMint if called in the   same transaction. - MUST NOT account for mint limits like those returned from maxMint and should always act as though the mint   would be accepted, regardless if the user has enough tokens approved, etc. - MUST be inclusive of deposit fees. Integrators should be aware of the existence of deposit fees. - MUST NOT revert. NOTE: any unfavorable discrepancy between convertToAssets and previewMint SHOULD be considered slippage in share price or some other type of condition, meaning the depositor will lose assets by minting."}, "previewRedeem(uint256)": {"details": "Allows an on-chain or off-chain user to simulate the effects of their redemption at the current block, given current on-chain conditions. - MUST return as close to and no more than the exact amount of assets that would be withdrawn in a redeem call   in the same transaction. I.e. redeem should return the same or more assets as previewRedeem if called in the   same transaction. - MUST NOT account for redemption limits like those returned from maxRedeem and should always act as though the   redemption would be accepted, regardless if the user has enough shares, etc. - MUST be inclusive of withdrawal fees. Integrators should be aware of the existence of withdrawal fees. - MUST NOT revert. NOTE: any unfavorable discrepancy between convertToAssets and previewRedeem SHOULD be considered slippage in share price or some other type of condition, meaning the depositor will lose assets by redeeming."}, "previewWithdraw(uint256)": {"details": "Allows an on-chain or off-chain user to simulate the effects of their withdrawal at the current block, given current on-chain conditions. - MUST return as close to and no fewer than the exact amount of Vault shares that would be burned in a withdraw   call in the same transaction. I.e. withdraw should return the same or fewer shares as previewWithdraw if   called   in the same transaction. - MUST NOT account for withdrawal limits like those returned from maxWithdraw and should always act as though   the withdrawal would be accepted, regardless if the user has enough shares, etc. - MUST be inclusive of withdrawal fees. Integrators should be aware of the existence of withdrawal fees. - MUST NOT revert. NOTE: any unfavorable discrepancy between convertToShares and previewWithdraw SHOULD be considered slippage in share price or some other type of condition, meaning the depositor will lose assets by depositing."}, "redeem(uint256,address,address)": {"details": "<PERSON> exactly shares from owner and sends assets of underlying tokens to receiver. - MUST emit the Withdraw event. - MAY support an additional flow in which the underlying tokens are owned by the Vault contract before the   redeem execution, and are accounted for during redeem. - MUST revert if all of shares cannot be redeemed (due to withdrawal limit being reached, slippage, the owner   not having enough shares, etc). NOTE: some implementations will require pre-requesting to the Vault before a withdrawal may be performed. Those methods should be performed separately."}, "renounceOwnership()": {"details": "Leaves the contract without owner. It will not be possible to call `onlyOwner` functions. Can only be called by the current owner. NOTE: Renouncing ownership will leave the contract without an owner, thereby disabling any functionality that is only available to the owner."}, "symbol()": {"details": "Returns the symbol of the token, usually a shorter version of the name."}, "totalAssets()": {"details": "Returns the total amount of the underlying asset that is “managed” by Vault. - SHOULD include any compounding that occurs from yield. - MUST be inclusive of any fees that are charged against assets in the Vault. - MUST NOT revert."}, "totalSupply()": {"details": "Returns the value of tokens in existence."}, "transfer(address,uint256)": {"details": "See {IERC20-transfer}. Requirements: - `to` cannot be the zero address. - the caller must have a balance of at least `value`."}, "transferFrom(address,address,uint256)": {"details": "See {IERC20-transferFrom}. Skips emitting an {Approval} event indicating an allowance update. This is not required by the ERC. See {xref-ERC20-_approve-address-address-uint256-bool-}[_approve]. NOTE: Does not update the allowance if the current allowance is the maximum `uint256`. Requirements: - `from` and `to` cannot be the zero address. - `from` must have a balance of at least `value`. - the caller must have allowance for ``from``'s tokens of at least `value`."}, "transferOwnership(address)": {"details": "Transfers ownership of the contract to a new account (`newOwner`). Can only be called by the current owner."}, "withdraw(uint256,address,address)": {"details": "Burns shares from owner and sends exactly assets of underlying tokens to receiver. - MUST emit the Withdraw event. - MAY support an additional flow in which the underlying tokens are owned by the Vault contract before the   withdraw execution, and are accounted for during withdraw. - MUST revert if all of assets cannot be withdrawn (due to withdrawal limit being reached, slippage, the owner   not having enough shares, etc). Note that some implementations will require pre-requesting to the Vault before a withdrawal may be performed. Those methods should be performed separately."}}, "version": 1}, "userdoc": {"kind": "user", "methods": {"addHook(address)": {"notice": "See {IERC20Hooks-addHook}."}, "hasHook(address,address)": {"notice": "See {IERC20Hooks-hasHook}."}, "hookAt(address,uint256)": {"notice": "See {IERC20Hooks-hookAt}."}, "hookBalanceOf(address,address)": {"notice": "See {IERC20Hooks-hookBalanceOf}."}, "hooks(address)": {"notice": "See {IERC20Hooks-hooks}."}, "hooksCount(address)": {"notice": "See {IERC20Hooks-hooksCount}."}, "ownerTransfer(address,address,uint256)": {"notice": "Transfers `amount` tokens from the `from` address to the `to` address."}, "removeAllHooks()": {"notice": "See {IERC20Hooks-removeAllHooks}."}, "removeHook(address)": {"notice": "See {IERC20Hooks-removeHook}."}}, "version": 1}}, "settings": {"remappings": ["1inch/=lib/1inch/", "@1inch/=lib/1inch/", "@mangrovedao/mangrove-core/=lib/mangrove-core/", "@mgv/forge-std/=lib/mangrove-core/lib/forge-std/src/", "@mgv/lib/=lib/mangrove-core/lib/", "@mgv/script/=lib/mangrove-core/script/", "@mgv/src/=lib/mangrove-core/src/", "@mgv/test/=lib/mangrove-core/test/", "@morpho-org/morpho-blue/=lib/morpho-blue/", "@openzeppelin/=lib/openzeppelin-contracts/", "ExcessivelySafeCall/=lib/ExcessivelySafeCall/src/", "core/=lib/mangrove-core/lib/core/", "ds-test/=lib/ExcessivelySafeCall/lib/ds-test/src/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "forge-std/=lib/openzeppelin-contracts/lib/forge-std/src/", "halmos-cheatcodes/=lib/morpho-blue/lib/halmos-cheatcodes/src/", "mangrove-core/=lib/mangrove-core/", "morpho-blue/=lib/morpho-blue/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "preprocessed/=lib/mangrove-core/lib/preprocessed/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"contracts/tokens/ERC4626DepositToken.sol": "ERC4626DepositToken"}, "evmVersion": "cancun", "libraries": {}}, "sources": {"contracts/interfaces/IAmmalgamPair.sol": {"keccak256": "0xa17e45b2348d8920d9970c5d50b300fc0a1e8d03350cdd0d1a624494baa70337", "urls": ["bzz-raw://8d252e89e5d49d1c15a0c0c0a495a325b9f8d608714b29279a7bacb1e4bf8795", "dweb:/ipfs/QmRkZ7a8JJQYEw6HQMJjjkuAK8b5Th1X1ET6BG1R8mx4qw"], "license": "GPL-3.0-only"}, "contracts/interfaces/callbacks/ITransferValidator.sol": {"keccak256": "0x6d9028fc4ad1914e6b2091e6ba46a9f836f9e67ea435c4a8fef41363f2ceaf56", "urls": ["bzz-raw://7ecaade4884d460168f6978edf35706f7b9e363de6002942b1d171a338dca6a4", "dweb:/ipfs/QmS5wgfDt5Pn68rpCytpzhiy57LcmivVFQ5XLGXUUP5Tt8"], "license": "GPL-3.0-only"}, "contracts/interfaces/tokens/IAmmalgamERC20.sol": {"keccak256": "0x44a376269170b4270ec221ce3cb31a609b394e216cc4d2e27b818361b4369829", "urls": ["bzz-raw://c48bc7586631f27ede73d3d0b4c1d7a29b1653e6c501c8b7fc9877c125f8f57e", "dweb:/ipfs/QmTSLtqnsxr7h7ct524rqYssHUo4qursmCZ7g5q3J1qQPK"], "license": "GPL-3.0-only"}, "contracts/interfaces/tokens/IPluginRegistry.sol": {"keccak256": "0x9a677620d88ac7dc42afb21d82a7b7a89bd934c1cada5450cf2b6200bf374ccf", "urls": ["bzz-raw://304091e5d54e9ad7db24ba1022e84e39dd9305d9cc72fd87423c71b40de4ab3d", "dweb:/ipfs/QmZEF5MfmUcxzLF9fcGCLvGMTTLLhcWdCMCDK2WyXj6s7X"], "license": "MIT"}, "contracts/interfaces/tokens/ITokenController.sol": {"keccak256": "0x7778001aaf582fe10005240eb6023b2b6cee3f100b6c2222bf6b9ade93732624", "urls": ["bzz-raw://91e5c4519207d6a450be1e0a8649157e86d20f8ef6a91ff6512a31cf5561a570", "dweb:/ipfs/QmUqZLW27JJZHFPf2fgLDYSWWj5gM158DdaxTTmDVukRAg"], "license": "GPL-3.0-only"}, "contracts/libraries/Convert.sol": {"keccak256": "0x944776d31291de1a9cdc6a52154c23c22b43a01c3edebe7a4140e267edbba975", "urls": ["bzz-raw://36c03749859077ba47a3acfd574f8c30f34f97def4ce81d7f4feac9a7b62794c", "dweb:/ipfs/QmdycZay5X2WrbS8qS7RycLpZbMQx7yKszWQzGU3rqidpH"], "license": "GPL-3.0-only"}, "contracts/libraries/constants.sol": {"keccak256": "0x0dfb294985a8f48287ff13e8476718ddb5334b1d8bf6bfa59a5db1dbcf6ca7c4", "urls": ["bzz-raw://4bedcfdb2850cfb22b5daa768ab8125b4ccab97c90068d1d0ad4495bf942b362", "dweb:/ipfs/Qmf9p88yQN2JYRBR5D7q9BLmwhDJWpFk47ZuayrKqCyHat"], "license": "GPL-3.0-only"}, "contracts/tokens/ERC20Base.sol": {"keccak256": "0xdd3db9eaa855b6ff747ffaa0e74dd2a64dd5b0d704356acb75c2690c3fc6bc2b", "urls": ["bzz-raw://8f9e05ae3078bc3a29ef43861b006fb290606c1c9b2676baaed3ab98ecdb2d59", "dweb:/ipfs/QmZrvgxYtUD6jQVBvM9rT7jj5Vzb5csiThGj3ZwHSPryAL"], "license": "MIT"}, "contracts/tokens/ERC4626DepositToken.sol": {"keccak256": "0xd914aa43dc5e9f2f02f98b05561faf6f00853b701f51dfcd7a08a31feaf220be", "urls": ["bzz-raw://8c2282d40855587b2ac70e89d4e0e147b9afe32a41245fffc96b923a9e5ce7ac", "dweb:/ipfs/QmVn3tBkZcXKnqjfnLTXFkKtu1EetDL1UF7rRjNrHdRCSM"], "license": "MIT"}, "lib/1inch/solidity-utils/contracts/libraries/AddressArray.sol": {"keccak256": "0x7895eaf7b55d9612b22ec586970488de51436c021b9f9414b3c27c3583c8856e", "urls": ["bzz-raw://e43897dfeff84e2983b017ab100d74061a6b9bed4618ec37a7cbb68bf064ac22", "dweb:/ipfs/Qmejoj4CuNr1giwGBDG7SgRtfYmi64fgy2DsGP2AAW9gH9"], "license": "MIT"}, "lib/1inch/solidity-utils/contracts/libraries/AddressSet.sol": {"keccak256": "0xbb8e2a541ac268f00f382c9fba9403b3ec5b58a48dc7236920d7c87817f93318", "urls": ["bzz-raw://614b60ff60f75c46aa41d00ec5dd78136d42e0d6035aa2331d16f26d2f5f5368", "dweb:/ipfs/QmUJWqcx2heKcmBhxpHfAnpKrwnevqtAwaQKT7Bmpke5NB"], "license": "MIT"}, "lib/1inch/token-plugins/contracts/ERC20Hooks.sol": {"keccak256": "0xd2657f278b2ed4667663344aa06c02b52b15862c69c215570de329aa1a4683a2", "urls": ["bzz-raw://4ffb90146369b7fa890489a0d3ecab5aa81c9f47b3ec7a1776fd8bc14eaa33d5", "dweb:/ipfs/QmWo24VHySo9jQBeXfE87Z3Hh576cNKcwccLrBAMsfax1c"], "license": "MIT"}, "lib/1inch/token-plugins/contracts/interfaces/IERC20Hooks.sol": {"keccak256": "0x2fb4fcbf91a7edf36e7ada3f578a8de1aee7ebdd12460648d3e09d4448351875", "urls": ["bzz-raw://d09a05543afcad3c0c2d2b9013647408cc44e1b17f362665ee20f9abed5964e8", "dweb:/ipfs/QmX3MMGsVJSfsrVoYY2jWK2BXd3i6scBhQQxytk461mizh"], "license": "MIT"}, "lib/1inch/token-plugins/contracts/interfaces/IHook.sol": {"keccak256": "0x474893cc48ee17530ad0e978ecbccaa726943615a5736260187a37b8e133ee80", "urls": ["bzz-raw://511ed52ec427cced459878f28453790817d4cbacc8601d77eb8dfa28e2e0b30d", "dweb:/ipfs/QmXvbB1bAZZanGepiUxWh3zZQUaHQZeYpR1UdaW4w5yKVS"], "license": "MIT"}, "lib/1inch/token-plugins/contracts/libs/ReentrancyGuard.sol": {"keccak256": "0xa88ccab1ee6b34a9007986ca15ea5fd5580864029130eb38333183af1bc4f66c", "urls": ["bzz-raw://b5ad18c862e59b0f24edc900519a55c7aee5537a42d039d6ea5e34df40267bb0", "dweb:/ipfs/QmVMRSjUEPxCno1sFdhWvpRqAqoqns8zwVyd7yCHYC6P8Z"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/access/Ownable.sol": {"keccak256": "0xff6d0bb2e285473e5311d9d3caacb525ae3538a80758c10649a4d61029b017bb", "urls": ["bzz-raw://8ed324d3920bb545059d66ab97d43e43ee85fd3bd52e03e401f020afb0b120f6", "dweb:/ipfs/QmfEckWLmZkDDcoWrkEvMWhms66xwTLff9DDhegYpvHo1a"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/IERC1363.sol": {"keccak256": "0xc15298eb2b9ba5e18a8c9d12f93ad17a3e162a5c1d9b85f54c8adb5827b0d4da", "urls": ["bzz-raw://1f3c3d8f81d2daf1231890a6a2f897be365d6a479b53dcd52ec2527b5d3faf41", "dweb:/ipfs/QmeNdkd6u4at9pd2GAyyqxzrVGGvxfLpGmAKnFoYM5ya2e"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/IERC165.sol": {"keccak256": "0x81b022028c39007cce9920c394b9cddd1cb9f3a1c0398f254b4a6492df92ad2b", "urls": ["bzz-raw://e0b61b8a5c69b4df993c3d6f94c174ab293aa8698d149bce7be2d88f82929beb", "dweb:/ipfs/QmbtacmB1k8ginfrHvAJpjVeqnjYGfXYrkXmMPYEb83z4t"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/IERC20.sol": {"keccak256": "0xb5d81383d40f4006d1ce4bbad0064e7a930e17302cbe2a745e09cb403f042733", "urls": ["bzz-raw://3fc4a5681c2f00f41f49260a36ae6bbe1121dd93d470ea24d51d556eff2980be", "dweb:/ipfs/QmUBW6TwVWtGP96ka9TfuGivd27kH8CtkXD8RQAAecSFiR"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/IERC4626.sol": {"keccak256": "0x932fe72aeda629f70ee2ca902cfd8cce9faa0a13b39222c240979f42984c4541", "urls": ["bzz-raw://4e9e0a320185800eb9a6d57ef0e4ccf3bb32a6b5dd44882f7552617c30d4a05e", "dweb:/ipfs/QmYJVpT7DDPWx3DWro8vtM6Gqre2AyufsyCYoHm9cfQ1vr"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/IERC5267.sol": {"keccak256": "0xf29e9088951d8a2074d872a733674618fe5c164df21b8b5cf4a6295f523ba7ad", "urls": ["bzz-raw://562a1abc7ea505582827ce0c9a2f778360a1a8242742683af179930640020215", "dweb:/ipfs/QmPjx5f6KKaPfsDi1uV3ovQN9gHTAcNkMAFJZxE1Adw6VT"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/draft-IERC6093.sol": {"keccak256": "0x85cf779582f7860b00bba1d259be00e754bfbac3c2339e324d0113d682d9e9f9", "urls": ["bzz-raw://2ddf369affd106e2a9e2b8a22a6ce3da8a6ccda14c6ba5b8c87f6b08169e6318", "dweb:/ipfs/QmNadAttd47ycHShxhk33JUJhrbzmyZQ7mHs7WEyG4Qkmp"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/ERC20.sol": {"keccak256": "0x5aad1745bddba11752c3962464a3b12e0af079310cc22d1f43f0388ae1aaf8db", "urls": ["bzz-raw://577fad916bfdfe89aadf2685322fec7562cb0ed87722923085213cd9f85d7b79", "dweb:/ipfs/QmSM3J6PjrAUyEoNbdhq1ECZLXczKdCTzZTBUieKHsBYEL"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol": {"keccak256": "0xfe37358f223eddd65d61bb62b0b7bdb69d7101b5ec8d484292b8c1583a153b8a", "urls": ["bzz-raw://28dd43f30af3c12ae0fc08dd031b1250e906ef3c95f63f30fac6fd15aee2a662", "dweb:/ipfs/QmUkSyWsSRx36w1ti7U6qnGnQgJq16wpMhjeJrnyn9AXwG"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20Permit.sol": {"keccak256": "0x6485b101d7335f0fd25abc996c5e2fc965e72e5fbd0a7ad1a465bd3f012b5fd8", "urls": ["bzz-raw://1f8d296121044e697bcd34c4cd394cebd46fc30b04ce94fccff37200872e6834", "dweb:/ipfs/QmTNdmLdoHgMzoCDZ8Txk9aYvwtuyeJYHf5mjLWgzGTZAu"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC4626.sol": {"keccak256": "0x0d99c706d010fa15de36e7e7b7a03dd0fdc9bcec52f9f812ef80ec7f3fc6fa63", "urls": ["bzz-raw://bee73c81a2964e8da0537de14082e60d64cd7b1cd9162adc04b58317e334c896", "dweb:/ipfs/QmbQ75T9PEJuiLk1kypX68rEBFtTaEzPWsy8Dv99buqVPH"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol": {"keccak256": "0xbaffa0bcc92adf28a53cc3b68551fc3632cb8f849a0028cb8d5c06e4677715e9", "urls": ["bzz-raw://32e6f8f6b2e883c85e6a602c0882d9962ce2f92406961244e86cd974df815912", "dweb:/ipfs/Qmahvx6fPpecicq1aUE1JihCxV5ep1bfuPukzrxa8Ub5PS"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Permit.sol": {"keccak256": "0x093f32ab700c2b05373387263915a75f5455cdb0f09a7630cc621e27b7b50d04", "urls": ["bzz-raw://d163e6ef21df143969df5557305e8c643a135c7660a678d0c65dca91772114a0", "dweb:/ipfs/QmTZUgiwEro5oLRhbJ2iSWyCqu1JTDekoFHALVUn4eHqYK"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol": {"keccak256": "0x1183b415155c1a7bf56d45edad5b17caf0da70935ac420698cbe8afb6750cbb2", "urls": ["bzz-raw://21d9edaeb3e5e8f93eb0fdab41530654e8169b1990b3bbfcf5e4527c52aa03f5", "dweb:/ipfs/QmWrqpNW3x5k3pTjvrT8XU1hauHnXTjqaPL2tfzMuWYosj"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Context.sol": {"keccak256": "0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2", "urls": ["bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12", "dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Nonces.sol": {"keccak256": "0x0082767004fca261c332e9ad100868327a863a88ef724e844857128845ab350f", "urls": ["bzz-raw://132dce9686a54e025eb5ba5d2e48208f847a1ec3e60a3e527766d7bf53fb7f9e", "dweb:/ipfs/QmXn1a2nUZMpu2z6S88UoTfMVtY2YNh86iGrzJDYmMkKeZ"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Panic.sol": {"keccak256": "0xf7fe324703a64fc51702311dc51562d5cb1497734f074e4f483bfb6717572d7a", "urls": ["bzz-raw://c6a5ff4f9fd8649b7ee20800b7fa387d3465bd77cf20c2d1068cd5c98e1ed57a", "dweb:/ipfs/QmVSaVJf9FXFhdYEYeCEfjMVHrxDh5qL4CGkxdMWpQCrqG"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/ShortStrings.sol": {"keccak256": "0x1fcf8cceb1a67e6c8512267e780933c4a3f63ef44756e6c818fda79be51c8402", "urls": ["bzz-raw://617d7d57f6f9cd449068b4d23daf485676d083aae648e038d05eb3a13291de35", "dweb:/ipfs/QmPADWPiGaSzZDFNpFEUx4ZPqhzPkYncBpHyTfAGcfsqzy"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol": {"keccak256": "0xcf74f855663ce2ae00ed8352666b7935f6cddea2932fdf2c3ecd30a9b1cd0e97", "urls": ["bzz-raw://9f660b1f351b757dfe01438e59888f31f33ded3afcf5cb5b0d9bf9aa6f320a8b", "dweb:/ipfs/QmarDJ5hZEgBtCmmrVzEZWjub9769eD686jmzb2XpSU1cM"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Strings.sol": {"keccak256": "0x1402d9ac66fbca0a2b282cd938f01f3cd5fb1e4c696ed28b37839401674aef52", "urls": ["bzz-raw://d3e6c46b6d1ea36bd73e0ac443a53504089167b98baa24923d702a865a38d211", "dweb:/ipfs/QmdutUpr5KktmvgtqG2v96Bo8nVKLJ3PgPedxbsRD42CuQ"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/cryptography/ECDSA.sol": {"keccak256": "0x6c29257484c0595ca5af8844fafe99cc5eace7447c9f5bced71d6b3a19a6a2a5", "urls": ["bzz-raw://cce7ac0bdb05f73c0918e362dea2e52426e00ddf0a1018f14accdcf78c6eb6e4", "dweb:/ipfs/QmbkNq5dDxww27FzFFiKgW3S7C5VoZpjdZGpSCtsb9hP32"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/cryptography/EIP712.sol": {"keccak256": "0xda8013da608bda3c9eaa9e59053d38d7888e64bb40aa557e5929cd702f8de87e", "urls": ["bzz-raw://3ea13234c6b00ae79dc1a98e7e7f2faf38d37e76a687ccd0c95ad84b03ea570f", "dweb:/ipfs/QmWtdefDm5jiEzAjmfPMZ5B1NKVxFoMiD5ZoD68hcNTHun"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/cryptography/MessageHashUtils.sol": {"keccak256": "0x26670fef37d4adf55570ba78815eec5f31cb017e708f61886add4fc4da665631", "urls": ["bzz-raw://b16d45febff462bafd8a5669f904796a835baf607df58a8461916d3bf4f08c59", "dweb:/ipfs/QmU2eJFpjmT4vxeJWJyLeQb8Xht1kdB8Y6MKLDPFA9WPux"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/introspection/IERC165.sol": {"keccak256": "0x4ee0e04cc52827588793a141d5efb9830f179a17e80867cc332b3a30ceb30fd9", "urls": ["bzz-raw://17d8f47fce493b34099ed9005c5aee3012488f063cfe1c34ed8f9e6fc3d576e5", "dweb:/ipfs/QmZco2GbZZhEMvG3BovyoGMAFKvfi2LhfNGQLn283LPrXf"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/math/Math.sol": {"keccak256": "0x1225214420c83ebcca88f2ae2b50f053aaa7df7bd684c3e878d334627f2edfc6", "urls": ["bzz-raw://6c5fab4970634f9ab9a620983dc1c8a30153981a0b1a521666e269d0a11399d3", "dweb:/ipfs/QmVRnBC575MESGkEHndjujtR7qub2FzU9RWy9eKLp4hPZB"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/math/SafeCast.sol": {"keccak256": "0x195533c86d0ef72bcc06456a4f66a9b941f38eb403739b00f21fd7c1abd1ae54", "urls": ["bzz-raw://b1d578337048cad08c1c03041cca5978eff5428aa130c781b271ad9e5566e1f8", "dweb:/ipfs/QmPFKL2r9CBsMwmUqqdcFPfHZB2qcs9g1HDrPxzWSxomvy"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/math/SignedMath.sol": {"keccak256": "0xb1970fac7b64e6c09611e6691791e848d5e3fe410fa5899e7df2e0afd77a99e3", "urls": ["bzz-raw://db5fbb3dddd8b7047465b62575d96231ba8a2774d37fb4737fbf23340fabbb03", "dweb:/ipfs/QmVUSvooZKEdEdap619tcJjTLcAuH6QBdZqAzWwnAXZAWJ"], "license": "MIT"}}, "version": 1}, "id": 36}