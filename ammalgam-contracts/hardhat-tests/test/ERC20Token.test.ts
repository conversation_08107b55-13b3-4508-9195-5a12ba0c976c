import { ethers } from 'hardhat'
import { ERC20LiquidityToken, ERC20TokenFactoryStub, ERC4626DepositToken } from '../typechain-types'
import { shouldBehaveLikeERC20 } from '../lib/openzeppelin-contracts/test/token/ERC20/ERC20.behavior'
import { Signer } from 'ethers'
const chai = require('../node_modules/chai')

const { expect } = chai

describe('ERC20LiquidityToken', function () {
  const initialSupply = ethers.parseEther('1000')
  let erc20LiquidityFactory: ERC20TokenFactoryStub
  let erc4626DepositFactory: ERC20TokenFactoryStub
  let erc20LiquidityToken: ERC20LiquidityToken
  let erc4626DepositToken: ERC4626DepositToken
  let owner: Signer
  let holder: Signer
  let recipient: Signer
  let other: Signer

  async function impersonateSigner(address: string): Promise<Signer> {
    await ethers.provider.send('hardhat_impersonateAccount', [address])
    return await ethers.getSigner(address)
  }

  async function fundAccount(address: string, amount: string) {
    const hexAmount = ethers.toBeHex(ethers.parseEther(amount)) // Convert to hexadecimal
    await ethers.provider.send('hardhat_setBalance', [address, hexAmount])
  }

  beforeEach(async function () {
    ;[owner, holder, recipient, other] = await ethers.getSigners()

    const transferValidator = await ethers.getContractFactory('TransferValidatorStub')
    const transferValidatorContract = await transferValidator.deploy()
    await transferValidatorContract.waitForDeployment()

    const pluginRegistry = await ethers.getContractFactory('PluginRegistryStub')
    const pluginRegistryContract = await pluginRegistry.deploy()
    await pluginRegistryContract.waitForDeployment()

    const liquidityConfig = {
      name: 'AMMLiquidityToken',
      symbol: 'AMML',
      tokenType: 0,
      pair: transferValidatorContract.target,
      pluginRegistry: pluginRegistryContract.target,
    }

    const depositConfig = {
      name: 'AMMDepositToken',
      symbol: 'AMMD',
      tokenType: 1,
      pair: transferValidatorContract.target,
      pluginRegistry: pluginRegistryContract.target,
    }

    // Deploy the ERC20LiquidityTokenFactory contract
    const ERC20LiquidityTokenFactory = await ethers.getContractFactory('ERC20LiquidityTokenFactoryStub')
    const liquidityTokenFactory = await ERC20LiquidityTokenFactory.deploy()
    await liquidityTokenFactory.waitForDeployment() // Wait for deployment to complete

    // Deploy the ERC4626DepositTokenFactory contract
    const ERC4626DepositTokenFactory = await ethers.getContractFactory('ERC4626DepositTokenFactoryStub')
    const depositTokenFactory = await ERC4626DepositTokenFactory.deploy()
    await depositTokenFactory.waitForDeployment() // Wait for deployment to complete

    // Create the ERC20TokenFactoryStub contract for liquidity tokens
    const ERC20TokenFactoryStub = await ethers.getContractFactory('ERC20TokenFactoryStub')
    erc20LiquidityFactory = await ERC20TokenFactoryStub.deploy(liquidityTokenFactory, liquidityConfig)
    await erc20LiquidityFactory.waitForDeployment()

    // Create the ERC20TokenFactoryStub contract for deposit tokens
    erc4626DepositFactory = await ERC20TokenFactoryStub.deploy(depositTokenFactory, depositConfig)
    await erc4626DepositFactory.waitForDeployment()

    erc20LiquidityToken = (await ethers.getContractAt(
      'ERC20LiquidityToken',
      await erc20LiquidityFactory.token()
    )) as ERC20LiquidityToken

    erc4626DepositToken = (await ethers.getContractAt(
      'ERC4626DepositToken',
      await erc4626DepositFactory.token()
    )) as ERC4626DepositToken

    // Impersonate the transfer validator (pair) address and fund it
    const pairSigner = await impersonateSigner(transferValidatorContract.target as string)
    await fundAccount(transferValidatorContract.target as string, '10')

    // Transfer ownership using the pair signer
    await erc20LiquidityToken.connect(pairSigner).transferOwnership(erc20LiquidityFactory.target)
    await erc4626DepositToken.connect(pairSigner).transferOwnership(erc4626DepositFactory.target)

    const mintLiquidityTokenTx = await erc20LiquidityFactory.mintERC20Token(
      await holder.getAddress(), // Sender
      await holder.getAddress(), // Recipient
      initialSupply,
      initialSupply
    )
    await mintLiquidityTokenTx.wait()

    const mintDepositTokenTx = await erc4626DepositFactory.mintERC20Token(
      await holder.getAddress(), // Sender
      await holder.getAddress(), // Recipient
      initialSupply,
      initialSupply
    )
    await mintDepositTokenTx.wait()
  })

  describe('ERC20 behavior - liquidity token', function () {
    beforeEach(function () {
      // Set up for shouldBehaveLikeERC20
      ;(this as any).token = erc20LiquidityToken
      ;(this as any).accounts = [holder, recipient, other]
    })

    shouldBehaveLikeERC20(initialSupply)
  })

  describe('ERC20 behavior - deposit token', function () {
    beforeEach(function () {
      // Set up for shouldBehaveLikeERC20
      ;(this as any).token = erc4626DepositToken
      ;(this as any).accounts = [holder, recipient, other]
    })

    shouldBehaveLikeERC20(initialSupply)
  })

  it('should preserve ERC20 functionality for transfers - liquidity token', async function () {
    const holderAddress = await holder.getAddress()
    const recipientAddress = await recipient.getAddress()

    const balanceBefore = await erc20LiquidityToken.balanceOf(holderAddress)
    await erc20LiquidityToken.connect(holder).transfer(recipientAddress, ethers.parseEther('100'))
    const balanceAfter = await erc20LiquidityToken.balanceOf(holderAddress)

    expect(balanceAfter).to.equal(balanceBefore - ethers.parseEther('100'))
  })

  it('should preserve ERC20 functionality for transfers - deposit token', async function () {
    const holderAddress = await holder.getAddress()
    const recipientAddress = await recipient.getAddress()

    const balanceBefore = await erc4626DepositToken.balanceOf(holderAddress)
    await erc4626DepositToken.connect(holder).transfer(recipientAddress, ethers.parseEther('100'))
    const balanceAfter = await erc4626DepositToken.balanceOf(holderAddress)

    expect(balanceAfter).to.equal(balanceBefore - ethers.parseEther('100'))
  })
})
