// SPDX-License-Identifier: MIT
pragma solidity ^0.8.28;

import "forge-std/Test.sol";
import "forge-std/console.sol";
import {Math} from "@openzeppelin/contracts/utils/math/Math.sol";
import {IERC20} from "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import {ERC20} from "@openzeppelin/contracts/token/ERC20/ERC20.sol";
import {ERC4626} from "@openzeppelin/contracts/token/ERC20/extensions/ERC4626.sol";

import {ERC4626DepositToken} from "contracts/tokens/ERC4626DepositToken.sol";
import {ERC4626DebtToken} from "contracts/tokens/ERC4626DebtToken.sol";
import {ERC20BaseConfig} from "contracts/tokens/ERC20Base.sol";
import {Convert} from "contracts/libraries/Convert.sol";

// Mock asset token for testing
contract MockAsset is ERC20 {
    constructor() ERC20("Mock Asset", "MOCK") {
        _mint(msg.sender, 1000000 * 10**18);
    }
    
    function mint(address to, uint256 amount) external {
        _mint(to, amount);
    }
}

// Standard ERC4626 implementation for comparison
contract StandardERC4626 is ERC4626 {
    constructor(IERC20 asset) ERC4626(asset) ERC20("Standard Vault", "sVAULT") {}
    
    function mint(address to, uint256 amount) external {
        _mint(to, amount);
    }
    
    function setTotalAssets(uint256 amount) external {
        // For testing purposes - manipulate total assets
    }
}

// Mock pair contract to satisfy ERC4626DepositToken requirements
contract MockPair {
    mapping(uint256 => uint128) public totalAssetsMap;

    function totalAssets() external view returns (uint128[6] memory) {
        uint128[6] memory assets;
        assets[0] = totalAssetsMap[0];
        assets[1] = totalAssetsMap[1];
        assets[2] = totalAssetsMap[2];
        assets[3] = totalAssetsMap[3];
        assets[4] = totalAssetsMap[4];
        assets[5] = totalAssetsMap[5];
        return assets;
    }

    function setTotalAssets(uint256 tokenType, uint128 amount) external {
        totalAssetsMap[tokenType] = amount;
    }

    function deposit(address) external {}
    function withdraw(address) external {}
    function borrow(address, uint256, uint256, bytes calldata) external {}
    function repay(address) external {}

    // Add validateOnUpdate function that ERC20Base expects
    function validateOnUpdate(address, address, bool) external pure {}
}

contract ERC4626RoundingVulnerabilityPOC is Test {
    MockAsset public asset;
    StandardERC4626 public standardVault;
    ERC4626DepositToken public depositToken;
    ERC4626DebtToken public debtToken;
    MockPair public mockPair;
    
    address public alice = address(0x1);
    address public bob = address(0x2);
    address public attacker = address(0x3);
    
    function setUp() public {
        // Deploy mock asset
        asset = new MockAsset();
        
        // Deploy standard ERC4626 for comparison
        standardVault = new StandardERC4626(IERC20(address(asset)));
        
        // Deploy mock pair
        mockPair = new MockPair();
        
        // Configure ERC20Base config
        ERC20BaseConfig memory config = ERC20BaseConfig({
            pair: address(mockPair),
            pluginRegistry: address(0),
            name: "Test Deposit Token",
            symbol: "TDT",
            tokenType: 0
        });
        
        // Deploy Ammalgam tokens
        depositToken = new ERC4626DepositToken(config, address(asset));

        config.name = "Test Debt Token";
        config.symbol = "TDebt";
        config.tokenType = 1;
        debtToken = new ERC4626DebtToken(config, address(asset));
        
        // Setup initial state
        mockPair.setTotalAssets(0, uint128(1000 * 10**18)); // 1000 assets for deposit token
        mockPair.setTotalAssets(1, uint128(500 * 10**18));  // 500 assets for debt token
        
        // Mint some shares to establish exchange rate
        vm.startPrank(address(mockPair));
        depositToken.ownerMint(alice, alice, 1000 * 10**18, 1000 * 10**18);
        debtToken.ownerMint(bob, bob, 500 * 10**18, 500 * 10**18);
        vm.stopPrank();
        
        // Give users some assets
        asset.mint(alice, 10000 * 10**18);
        asset.mint(bob, 10000 * 10**18);
        asset.mint(attacker, 10000 * 10**18);
    }
    
    function testERC4626StandardCompliance() public {
        console.log("=== Testing ERC4626 Standard Compliance ===");
        
        uint256 testAssets = 1000 * 10**18;
        
        // Test convertToShares - MUST round down according to ERC4626
        uint256 standardShares = standardVault.convertToShares(testAssets);
        uint256 depositShares = depositToken.convertToShares(testAssets);
        uint256 debtShares = debtToken.convertToShares(testAssets);
        
        console.log("Standard ERC4626 convertToShares:", standardShares);
        console.log("Deposit Token convertToShares:", depositShares);
        console.log("Debt Token convertToShares:", debtShares);
        
        // Test with fractional amounts that would cause rounding differences
        testAssets = 1001; // Small amount that will cause rounding
        
        standardShares = standardVault.convertToShares(testAssets);
        depositShares = depositToken.convertToShares(testAssets);
        debtShares = debtToken.convertToShares(testAssets);
        
        console.log("\n--- Testing with fractional amounts ---");
        console.log("Assets:", testAssets);
        console.log("Standard ERC4626 shares:", standardShares);
        console.log("Deposit Token shares:", depositShares);
        console.log("Debt Token shares:", debtShares);
    }
    
    function testRoundingBehaviorDetailed() public {
        console.log("=== Detailed Rounding Behavior Analysis ===");
        
        // Set up specific exchange rates to amplify rounding effects
        mockPair.setTotalAssets(0, uint128(3 * 10**18)); // 3 assets
        vm.startPrank(address(mockPair));
        depositToken.ownerMint(alice, alice, 0, 2 * 10**18); // 2 shares
        vm.stopPrank();
        
        // Now exchange rate is 3:2 (1.5 assets per share)
        
        uint256[] memory testAmounts = new uint256[](5);
        testAmounts[0] = 1;
        testAmounts[1] = 2;
        testAmounts[2] = 4;
        testAmounts[3] = 5;
        testAmounts[4] = 7;
        
        console.log("Exchange rate: 3 assets : 2 shares (1.5 assets per share)");
        console.log("Expected shares = assets * 2 / 3");
        
        for (uint i = 0; i < testAmounts.length; i++) {
            uint256 assets = testAmounts[i];
            uint256 expectedFloor = (assets * 2) / 3; // Floor division
            uint256 expectedCeil = Math.ceilDiv(assets * 2, 3); // Ceil division
            
            uint256 actualShares = depositToken.convertToShares(assets);
            
            console.log("\nAssets:", assets);
            console.log("Expected (Floor):", expectedFloor);
            console.log("Expected (Ceil):", expectedCeil);
            console.log("Actual:", actualShares);
            
            if (actualShares != expectedFloor) {
                console.log("VIOLATION: Should round down but got:", actualShares);
            } else {
                console.log("Correct: Rounds down as expected");
            }
        }
    }

    function testPreviewFunctionRounding() public {
        console.log("=== Testing Preview Function Rounding ===");

        // Set up exchange rate for testing
        mockPair.setTotalAssets(0, uint128(7 * 10**18)); // 7 assets
        vm.startPrank(address(mockPair));
        depositToken.ownerMint(alice, alice, 0, 3 * 10**18); // 3 shares
        vm.stopPrank();

        // Exchange rate: 7 assets : 3 shares

        uint256 testAssets = 10;
        uint256 testShares = 5;

        console.log("Exchange rate: 7 assets : 3 shares");

        // Test all preview functions
        uint256 previewDeposit = depositToken.previewDeposit(testAssets);
        uint256 previewMint = depositToken.previewMint(testShares);
        uint256 previewWithdraw = depositToken.previewWithdraw(testAssets);
        uint256 previewRedeem = depositToken.previewRedeem(testShares);

        console.log("\nPreview Functions Results:");
        console.log("previewDeposit(", testAssets, "):", previewDeposit);
        console.log("previewMint(", testShares, "):", previewMint);
        console.log("previewWithdraw(", testAssets, "):", previewWithdraw);
        console.log("previewRedeem(", testShares, "):", previewRedeem);

        // Calculate expected values manually
        // previewDeposit should round DOWN: assets * shares / totalAssets
        uint256 expectedDepositFloor = (testAssets * 3) / 7; // = 30/7 = 4 (floor)
        uint256 expectedDepositCeil = Math.ceilDiv(testAssets * 3, 7); // = 5 (ceil)

        // previewWithdraw should round UP: assets * shares / totalAssets
        uint256 expectedWithdrawFloor = (testAssets * 3) / 7; // = 4 (floor)
        uint256 expectedWithdrawCeil = Math.ceilDiv(testAssets * 3, 7); // = 5 (ceil)

        console.log("\nExpected Values:");
        console.log("previewDeposit should be (Floor):", expectedDepositFloor);
        console.log("previewWithdraw should be (Ceil):", expectedWithdrawCeil);

        // Verify compliance
        if (previewDeposit != expectedDepositFloor) {
            console.log("previewDeposit VIOLATION: Expected", expectedDepositFloor, "got", previewDeposit);
        } else {
            console.log("previewDeposit correct");
        }

        if (previewWithdraw != expectedWithdrawCeil) {
            console.log("previewWithdraw VIOLATION: Expected", expectedWithdrawCeil, "got", previewWithdraw);
        } else {
            console.log("previewWithdraw correct");
        }
    }

    function testAttackScenario() public {
        console.log("=== Testing Attack Scenario ===");

        // Scenario: Attacker exploits rounding to gain advantage

        // Set up a vault state where rounding matters
        mockPair.setTotalAssets(0, uint128(1000000)); // 1M assets (6 decimals for easier math)
        vm.startPrank(address(mockPair));
        depositToken.ownerMint(alice, alice, 0, 333333); // 333,333 shares
        vm.stopPrank();

        // Exchange rate: 1,000,000 assets : 333,333 shares ≈ 3.000003 assets per share

        console.log("Initial state:");
        console.log("Total Assets:", mockPair.totalAssetsMap(0));
        console.log("Total Shares:", depositToken.totalSupply());
        console.log("Exchange Rate: ~3.000003 assets per share");

        // Test small deposits that would be affected by rounding
        uint256[] memory attackAmounts = new uint256[](5);
        attackAmounts[0] = 1;
        attackAmounts[1] = 2;
        attackAmounts[2] = 3;
        attackAmounts[3] = 4;
        attackAmounts[4] = 5;

        console.log("\nTesting small deposit amounts:");

        for (uint i = 0; i < attackAmounts.length; i++) {
            uint256 assets = attackAmounts[i];
            uint256 shares = depositToken.convertToShares(assets);
            uint256 backToAssets = depositToken.convertToAssets(shares);

            console.log("\nAssets deposited:", assets);
            console.log("Shares received:", shares);
            console.log("Assets when converted back:", backToAssets);

            if (backToAssets > assets) {
                console.log("POTENTIAL EXPLOIT: Gained", backToAssets - assets, "assets");
            } else if (backToAssets < assets) {
                console.log("User loses", assets - backToAssets, "assets due to rounding");
            } else {
                console.log("No rounding loss/gain");
            }
        }
    }

    function testDirectConversionLogic() public {
        console.log("=== Testing Direct Conversion Logic ===");

        // Set up simple 3:2 ratio
        mockPair.setTotalAssets(0, uint128(3 * 10**18)); // 3 assets
        vm.startPrank(address(mockPair));
        depositToken.ownerMint(alice, alice, 0, 2 * 10**18); // 2 shares
        vm.stopPrank();

        console.log("Total Assets:", depositToken.totalAssets());
        console.log("Total Shares:", depositToken.totalSupply());

        // Test the Convert library directly
        uint256 directResult = Convert.toShares(1, 3 * 10**18, 2 * 10**18, false);
        console.log("Direct Convert.toShares(1, 3e18, 2e18, false):", directResult);

        // Test the ERC4626 function
        uint256 erc4626Result = depositToken.convertToShares(1);
        console.log("ERC4626 convertToShares(1):", erc4626Result);

        // Test with larger amounts
        uint256 directResult1000 = Convert.toShares(1000, 3 * 10**18, 2 * 10**18, false);
        console.log("Direct Convert.toShares(1000, 3e18, 2e18, false):", directResult1000);

        uint256 erc4626Result1000 = depositToken.convertToShares(1000);
        console.log("ERC4626 convertToShares(1000):", erc4626Result1000);

        // Expected: 1000 * 2e18 / 3e18 = 2000e18 / 3e18 = 666.666... = 666 (floor)
        uint256 numerator = 1000 * 2 * 10**18;
        uint256 denominator = 3 * 10**18;
        uint256 expected = numerator / denominator;
        console.log("Expected (manual calculation):", expected);
    }

    function testRoundingViolation() public {
        console.log("=== Testing for ERC4626 Rounding Violation ===");

        // Create a fresh vault with a simple ratio that will show rounding differences
        mockPair.setTotalAssets(0, uint128(3)); // 3 assets
        vm.startPrank(address(mockPair));
        depositToken.ownerMint(alice, alice, 3, 2); // 2 shares for 3 assets
        vm.stopPrank();

        console.log("Vault state:");
        console.log("Total Assets:", depositToken.totalAssets());
        console.log("Total Shares:", depositToken.totalSupply());
        console.log("Exchange rate: 3 assets : 2 shares");

        // Test the key ERC4626 functions with amounts that will cause rounding
        uint256[] memory testAmounts = new uint256[](3);
        testAmounts[0] = 1;
        testAmounts[1] = 2;
        testAmounts[2] = 4;

        for (uint i = 0; i < testAmounts.length; i++) {
            uint256 assets = testAmounts[i];

            console.log("\n--- Testing with", assets, "assets ---");

            // Get all the conversion results
            uint256 convertToShares = depositToken.convertToShares(assets);
            uint256 convertToAssets = depositToken.convertToAssets(convertToShares);
            uint256 previewDeposit = depositToken.previewDeposit(assets);
            uint256 previewWithdraw = depositToken.previewWithdraw(assets);

            console.log("convertToShares:", convertToShares);
            console.log("convertToAssets(shares):", convertToAssets);
            console.log("previewDeposit:", previewDeposit);
            console.log("previewWithdraw:", previewWithdraw);

            // Calculate expected values manually
            // For 3 assets : 2 shares ratio
            // convertToShares should round DOWN: assets * 2 / 3
            uint256 expectedShares = (assets * 2) / 3;
            uint256 expectedSharesCeil = Math.ceilDiv(assets * 2, 3);

            console.log("Expected shares (floor):", expectedShares);
            console.log("Expected shares (ceil):", expectedSharesCeil);

            // Check for violations
            if (convertToShares != expectedShares) {
                console.log("VIOLATION: convertToShares should round DOWN");
                console.log("Expected:", expectedShares, "Got:", convertToShares);
            }

            if (previewDeposit != expectedShares) {
                console.log("VIOLATION: previewDeposit should round DOWN (same as convertToShares)");
                console.log("Expected:", expectedShares, "Got:", previewDeposit);
            }

            if (previewWithdraw != expectedSharesCeil) {
                console.log("VIOLATION: previewWithdraw should round UP");
                console.log("Expected:", expectedSharesCeil, "Got:", previewWithdraw);
            }

            // Check round-trip consistency
            if (convertToAssets > assets) {
                console.log("POTENTIAL EXPLOIT: Round-trip gives more assets than input");
                console.log("Gain:", convertToAssets - assets, "assets");
            } else if (convertToAssets < assets) {
                console.log("User loss:", assets - convertToAssets, "assets");
            }
        }
    }

    function testBypassAttempts() public {
        console.log("=== Testing Bypass Attempts and Persistence ===");

        // Test 1: Different exchange rates
        console.log("\n1. Testing with different exchange rates:");

        uint256[] memory ratios = new uint256[](3);
        ratios[0] = 1; // 1:1
        ratios[1] = 2; // 2:1
        ratios[2] = 10; // 10:1

        for (uint i = 0; i < ratios.length; i++) {
            uint256 ratio = ratios[i];

            // Reset vault
            mockPair.setTotalAssets(0, uint128(ratio));
            vm.startPrank(address(mockPair));
            depositToken.ownerMint(alice, alice, ratio, 1);
            vm.stopPrank();

            uint256 shares = depositToken.convertToShares(1);
            uint256 expectedShares = 1 / ratio; // Should be 0 for ratios > 1

            console.log("Ratio:");
            console.log(ratio);
            console.log("convertToShares(1):");
            console.log(shares);
            console.log("expected:");
            console.log(expectedShares);

            if (shares != expectedShares && expectedShares == 0) {
                console.log("VULNERABILITY PERSISTS at ratio:");
                console.log(ratio);
            }
        }

        // Test 2: Large numbers (overflow protection)
        console.log("\n2. Testing with large numbers:");

        mockPair.setTotalAssets(0, uint128(type(uint64).max));
        vm.startPrank(address(mockPair));
        depositToken.ownerMint(alice, alice, type(uint64).max, type(uint64).max);
        vm.stopPrank();

        try depositToken.convertToShares(type(uint64).max) returns (uint256 largeShares) {
            console.log("Large number conversion successful:", largeShares);
            console.log("No overflow protection bypassed");
        } catch {
            console.log("Large number conversion failed - overflow protection exists");
        }

        // Test 3: Zero values (edge case protection)
        console.log("\n3. Testing with zero values:");

        uint256 zeroShares = depositToken.convertToShares(0);
        console.log("convertToShares(0):", zeroShares);

        if (zeroShares != 0) {
            console.log("VULNERABILITY: convertToShares(0) should return 0");
        }

        // Test 4: Consistency across all ERC4626 functions
        console.log("\n4. Testing consistency across ERC4626 functions:");

        mockPair.setTotalAssets(0, uint128(7));
        vm.startPrank(address(mockPair));
        depositToken.ownerMint(alice, alice, 7, 3);
        vm.stopPrank();

        uint256 testAmount = 5;
        uint256 convertShares = depositToken.convertToShares(testAmount);
        uint256 convertAssets = depositToken.convertToAssets(testAmount);
        uint256 previewDep = depositToken.previewDeposit(testAmount);
        uint256 previewWith = depositToken.previewWithdraw(testAmount);
        uint256 previewMint = depositToken.previewMint(testAmount);
        uint256 previewRed = depositToken.previewRedeem(testAmount);

        console.log("All ERC4626 functions with input:");
        console.log(testAmount);
        console.log("convertToShares:");
        console.log(convertShares);
        console.log("convertToAssets:");
        console.log(convertAssets);
        console.log("previewDeposit:");
        console.log(previewDep);
        console.log("previewWithdraw:");
        console.log(previewWith);
        console.log("previewMint:");
        console.log(previewMint);
        console.log("previewRedeem:");
        console.log(previewRed);

        // Check if the vulnerability affects all functions
        bool allFunctionsAffected = true;
        if (convertShares == previewDep && previewWith > previewDep) {
            allFunctionsAffected = false;
        }

        if (allFunctionsAffected) {
            console.log("VULNERABILITY affects multiple ERC4626 functions");
        } else {
            console.log("Some functions may be correctly implemented");
        }
    }

    function testTotalSupplyIssue() public {
        console.log("=== Investigating totalSupply Issue ===");

        // Start with a clean slate
        console.log("Initial totalSupply:", depositToken.totalSupply());

        // Mint exactly 2 shares
        vm.startPrank(address(mockPair));
        depositToken.ownerMint(alice, alice, 3, 2);
        vm.stopPrank();

        console.log("After minting 2 shares:");
        console.log("totalSupply():", depositToken.totalSupply());
        console.log("balanceOf(alice):", depositToken.balanceOf(alice));

        // Check if there's a decimal offset issue
        console.log("decimals():", depositToken.decimals());
        console.log("asset decimals:", MockAsset(address(asset)).decimals());

        // Check the underlying ERC20 totalSupply directly
        console.log("ERC20 totalSupply (direct):", ERC20(address(depositToken)).totalSupply());

        // Test with a fresh token to see if it's cumulative
        ERC20BaseConfig memory config2 = ERC20BaseConfig({
            pair: address(mockPair),
            pluginRegistry: address(0),
            name: "Test Deposit Token 2",
            symbol: "TDT2",
            tokenType: 0
        });

        ERC4626DepositToken freshToken = new ERC4626DepositToken(config2, address(asset));

        console.log("\nFresh token test:");
        console.log("Fresh token initial totalSupply:", freshToken.totalSupply());

        vm.startPrank(address(mockPair));
        freshToken.ownerMint(alice, alice, 100, 50);
        vm.stopPrank();

        console.log("Fresh token after minting 50 shares:");
        console.log("totalSupply():", freshToken.totalSupply());
        console.log("balanceOf(alice):", freshToken.balanceOf(alice));

        // Test if the issue is in the constructor or minting
        console.log("\nDebugging the minting process:");

        // Check if ERC4626 has virtual shares
        console.log("Virtual shares calculation test:");
        uint256 virtualShares = 10 ** 0; // _decimalsOffset() default is 0
        console.log("Virtual shares (10^decimalsOffset):", virtualShares);

        // Manual calculation of what totalSupply should be
        uint256 expectedTotalSupply = 2; // We minted 2 shares
        console.log("Expected totalSupply:", expectedTotalSupply);
        console.log("Actual totalSupply:", depositToken.totalSupply());
        console.log("Difference:", depositToken.totalSupply() - expectedTotalSupply);

        // Check if the issue is 10^18 related
        uint256 oneEther = 1 ether;
        console.log("1 ether (10^18):", oneEther);
        console.log("Is difference ~1e18?", (depositToken.totalSupply() - expectedTotalSupply) / oneEther);
    }
}
