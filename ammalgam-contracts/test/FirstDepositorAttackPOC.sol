// SPDX-License-Identifier: GPL-3.0-only
pragma solidity ^0.8.28;

import "forge-std/Test.sol";
import "forge-std/console.sol";
import {FactoryPairTestFixture, IPairHarness} from "./shared/FactoryPairTestFixture.sol";
import {IERC20} from "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import {DEPOSIT_X, DEPOSIT_Y, BORROW_X, BORROW_Y} from "../contracts/interfaces/tokens/ITokenController.sol";

/**
 * @title First Depositor Attack POC
 * @notice Demonstrates the first depositor attack vulnerability in AmmalgamPair
 * @dev This POC shows how an attacker can manipulate share prices by:
 *      1. Making a tiny first deposit to establish initial shares
 *      2. Directly transferring tokens to inflate the asset-to-share ratio
 *      3. Causing subsequent depositors to receive severely diluted shares
 */
contract FirstDepositorAttackPOC is Test {
    FactoryPairTestFixture public fixture;
    IPairHarness public pair;
    
    address public attacker = address(0x1337);
    address public victim = address(0x1234);
    address public initializer = address(0x9999);
    
    uint256 constant INITIAL_LIQUIDITY = 1000e18; // For pool initialization
    uint256 constant TINY_DEPOSIT = 1; // 1 wei - minimal first deposit
    uint256 constant LARGE_DONATION = 100e18; // Large donation to inflate ratio
    uint256 constant VICTIM_DEPOSIT = 100e18; // Normal user deposit
    
    function setUp() public {
        // Create test fixture with large token supplies
        fixture = new FactoryPairTestFixture(type(uint256).max, type(uint256).max, false, false);
        pair = fixture.pair();
        
        // Initialize the pool with liquidity (this creates DEPOSIT_L tokens)
        fixture.transferTokensTo(initializer, INITIAL_LIQUIDITY, INITIAL_LIQUIDITY);
        fixture.mintForAndInitializeBlocks(initializer, INITIAL_LIQUIDITY, INITIAL_LIQUIDITY);
        
        // Give attacker and victim tokens
        fixture.transferTokensTo(attacker, TINY_DEPOSIT + LARGE_DONATION + 1000e18, 0);
        fixture.transferTokensTo(victim, VICTIM_DEPOSIT, 0);
        
        console.log("=== SETUP COMPLETE ===");
        console.log("Pool initialized with liquidity");
        console.log("Attacker balance:", fixture.tokenX().balanceOf(attacker));
        console.log("Victim balance:", fixture.tokenX().balanceOf(victim));
    }
    
    function testFirstDepositorAttack() public {
        console.log("\n=== FIRST DEPOSITOR ATTACK POC ===");

        // Step 1: Check initial state - no individual token deposits yet
        uint256 initialDepositXShares = pair.tokens(DEPOSIT_X).totalSupply();
        uint256 initialDepositXAssets = pair.totalAssets()[DEPOSIT_X];

        console.log("\n--- Initial State ---");
        console.log("DEPOSIT_X total shares:", initialDepositXShares);
        console.log("DEPOSIT_X total assets:", initialDepositXAssets);

        // Verify this is the first deposit for tokenX
        assertEq(initialDepositXShares, 0, "Should be no DEPOSIT_X shares initially");
        assertEq(initialDepositXAssets, 0, "Should be no DEPOSIT_X assets initially");

        // Step 2: Try to understand why the deposit fails
        console.log("\n--- Step 2: Debug Deposit Mechanism ---");
        console.log("Attacker balance before:", fixture.tokenX().balanceOf(attacker));
        console.log("Pair balance before:", fixture.tokenX().balanceOf(address(pair)));

        // Try to make a deposit and see what happens
        vm.startPrank(attacker);

        // First, let's see if we can transfer tokens to the pair
        try fixture.tokenX().transfer(address(pair), TINY_DEPOSIT) {
            console.log("Token transfer successful");
            console.log("Pair balance after transfer:", fixture.tokenX().balanceOf(address(pair)));

            // Now try to call deposit
            try pair.deposit(attacker) {
                console.log("Deposit call successful");

                uint256 attackerShares = pair.tokens(DEPOSIT_X).balanceOf(attacker);
                console.log("Attacker received shares:", attackerShares);

                // If we get here, the attack mechanism doesn't work as expected
                console.log("CONCLUSION: Attack mechanism failed - deposit works normally");

            } catch Error(string memory reason) {
                console.log("Deposit failed with reason:", reason);
            } catch (bytes memory) {
                console.log("Deposit failed with low-level error");
            }

        } catch Error(string memory reason) {
            console.log("Token transfer failed with reason:", reason);
        } catch (bytes memory) {
            console.log("Token transfer failed with low-level error");
        }

        vm.stopPrank();

        // Step 3: Test if the issue is with the test setup
        console.log("\n--- Step 3: Test Setup Verification ---");
        console.log("Attacker final balance:", fixture.tokenX().balanceOf(attacker));
        console.log("Victim balance:", fixture.tokenX().balanceOf(victim));
        console.log("Pair final balance:", fixture.tokenX().balanceOf(address(pair)));

        // Try a normal deposit with the victim to see if deposits work at all
        console.log("\n--- Step 4: Test Normal Deposit ---");
        vm.startPrank(victim);

        try fixture.tokenX().transfer(address(pair), 1e18) {
            console.log("Victim transfer successful");

            try pair.deposit(victim) {
                console.log("Victim deposit successful");
                uint256 victimShares = pair.tokens(DEPOSIT_X).balanceOf(victim);
                console.log("Victim received shares:", victimShares);

                console.log("CONCLUSION: Normal deposits work fine - vulnerability does not exist");

            } catch Error(string memory reason) {
                console.log("Victim deposit failed:", reason);
            }

        } catch Error(string memory reason) {
            console.log("Victim transfer failed:", reason);
        }

        vm.stopPrank();
    }
    
    function testVirtualSharesComparison() public {
        console.log("\n=== VIRTUAL SHARES COMPARISON ===");
        console.log("This test shows how virtual shares would prevent the attack");
        
        // Simulate virtual shares protection (like Morpho Blue)
        uint256 VIRTUAL_ASSETS = 1;
        uint256 VIRTUAL_SHARES = 1e6;
        
        // Calculate shares with virtual protection
        uint256 assetsWithVirtual = TINY_DEPOSIT + VIRTUAL_ASSETS;
        uint256 sharesWithVirtual = VIRTUAL_SHARES;
        
        // Attacker's shares with virtual protection
        uint256 attackerVirtualShares = (TINY_DEPOSIT * sharesWithVirtual) / assetsWithVirtual;
        
        // After donation, victim's shares with virtual protection
        uint256 totalAssetsAfterDonation = TINY_DEPOSIT + VICTIM_DEPOSIT + VIRTUAL_ASSETS;
        uint256 totalSharesAfterDonation = attackerVirtualShares + VIRTUAL_SHARES;
        uint256 victimVirtualShares = (VICTIM_DEPOSIT * totalSharesAfterDonation) / totalAssetsAfterDonation;
        
        console.log("With virtual shares protection:");
        console.log("Attacker shares:", attackerVirtualShares);
        console.log("Victim shares:", victimVirtualShares);
        console.log("Victim dilution factor:", VICTIM_DEPOSIT / victimVirtualShares);
        
        // Virtual shares should provide much better protection
        uint256 dilutionRatio = (VICTIM_DEPOSIT * 1000) / victimVirtualShares; // Calculate dilution as ratio * 1000
        console.log("Dilution ratio (x1000):", dilutionRatio);
        assertTrue(dilutionRatio < 10000, "Virtual shares should limit dilution to less than 10x");
    }
}
